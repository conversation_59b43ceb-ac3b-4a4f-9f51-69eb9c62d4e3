Log::info(      'Number'                                            .                                                   $this->journal->Number);
  Log::info(      'ServiceID'                                            .                                                    $this->body['SID']);
  Log::info(      'NetworkID'                                            .                                                    "null");
  Log::info(      'SubscriberNumber'                                            .                                                    $this->body['SNO']);
  Log::info(      'Amount'                                            .                                                    $this->body['AMT']);
  Log::info(      'FactionID'                                            .                                                    $this->body['FID']);
  Log::info(      'RegionID'                                            .                                                    "null"); // default
  Log::info(      'LineType'                                            .                                                    $this->body['SID']==2?($this->body['mLtype']):'غير معروف'); // default
  Log::info(      'Date'                                            .                                                    date('Y-m-d H:i:s'));
  Log::info(      'Status'                                            .                                                    2);   // 0 faild  1 complited  // 2 hold // 5 time out // 6 reverse
  Log::info(      'Note'                                            .                                                    "null");
  Log::info(      'CreditorAccountID'                                            .                                                    $this->body['SID']==2? 519814  :( $this->body['SID']==40?316581 : 379015));
  Log::info(      'CurrencyID'                                            .                                                    1);
  Log::info(      'DebitorAccountID'                                            .                                                   $this->accountUser->AccountID );
  Log::info(      'AgentID'                                            .                                                    "null");
  Log::info(      'RefNumber'                                            .                                                    "null");
  Log::info(      'TransactionID'                                            .                                                    $this->journal->Number);
  Log::info(      'ProviderID'                                            .                                                    $this->body['SID']==2?10031:(this->body['SID']==40? 10018: 10028));
  Log::info(      'EntryID'                                            .                                                    $this->journal->id);
  Log::info(      'PaymentEntryID'                                            .                                                    "null");
  Log::info(      'Channel'                                            .                                                    2);
  Log::info(      'CreatedBy'                                            .                                                    $userInfo->ID );
  Log::info(      'BranchBy'                                            .                                                    "null");
  Log::info(      'CreatedTime'                                            .                                                     date('Y-m-d H:i:s'));
  Log::info(      'BranchID'                                            .                                                    $this->accountUser->BranchID);
  Log::info(      'ProviderRM'                                            .                                                    '');
  Log::info(      'ProviderPrice'                                            .                                                   $this->body['SID']==2?(number_format(floatval( $this->body['LATEFnum']*1.21));2);".");"")): $faction->ProviderPrice);
  Log::info(      'SubNote'                                            .                                                    "null");
  Log::info(      'Datestamb'                                            .                                                    date('Ymd'));
  Log::info(      'UniqueNo'                                            .                                                     date('YmdHis'));
  Log::info(      'Quantity'                                            .                                                   $this->body['SID']==2? $this->body['LATEFnum']:$faction->Quantity);
  Log::info(      'UnitPrice'                                            .                                                   $this->body['SID']==2?(number_format(floatval(($this->body['AMT'] /$this->body['LATEFnum'])));3);".");"")): ($faction->Price > $this->body['AMT'] ? $faction->Price : $this->body['AMT'])/$faction->Quantity);
  Log::info(      'UnitCost'                                            .                                                   $this->body['SID']==2?1.21 : $faction->ProviderPrice/$faction->Quantity);
  Log::info(      'CostAmount'                                            .                                                    $this->body['SID']==2?(number_format(floatval( $this->body['LATEFnum']*1.21));2);".");"")): $faction->ProviderPrice);
  Log::info(      'DifferentialAmount'                                            .                                                    0.00);
  Log::info(      'CommissionAmount'                                            .                                                    0.00);
  Log::info(      'Discount'                                            .                                                    0.00);
  Log::info(      'TotalCost'                                            .                                                    ( $this->body['SID']==2?(number_format(floatval( $this->body['LATEFnum']*1.21));2);".");"")): $faction->ProviderPrice));
  Log::info(      'TotalAmount'                                            .                                                    $this->body['SID']==2? $this->body['AMT'] : number_format(floatval(($faction->Price > $this->body['AMT'] ? $faction->Price : $this->body['AMT'])));2);".");""));
  Log::info(      'Profits'                                            .                                                    $this->body['SID']==2?( $this->body['AMT']-(number_format(floatval( $this->body['LATEFnum']*1.21));2);".");""))) :($faction->Price > $this->body['AMT'] ? $faction->Price : $this->body['AMT'])-$faction->ProviderPrice);
  Log::info(      'Method'                                            .                                                    2); // default
  Log::info(      'Type'                                            .                                                    0); // default
  Log::info(      'Class'                                            .                                                    0); // default
  Log::info(      'LType'                                            .                                                    $this->body['LType']); // default
  Log::info(      'OperatorID'                                            .                                                    1); // default
  Log::info(      'AppTechApi'                                            .                                                    0); // default
  Log::info(      'BillNumber'                                            .                                                    $this->body['SID'].'00'.$this->journal->Number);
  Log::info(      'BillState'                                            .                                                    0); // default
  Log::info(      'Debited'                                            .                                                    1); // default
  Log::info(      'ByChild'                                            .                                                   $tByChild);//this->client?( $this->client->ParentAccountID ? 1 : 0):0); // default
  Log::info(      'IsDirect'                                            .                                                    1); // default
  Log::info(      'BundleName'                                            .                                                    $this->body['SID']==2?"ام تي ان رصيد مفتوح": $faction->Name); // default
  Log::info(      'BundleCode'                                            .                                                    $this->body['SID']==2? $this->body['AMT'] : $faction->ProviderCode); // default
  Log::info(      'ExCode'                                            .                                                    "null"); // default
  Log::info(      'TransNumber'                                            .                                                    date('YmdHis')); // default
  Log::info(      'OperationID'                                            .                                                    0); // default
  Log::info(      'AccountID'                                            .                                                   $tAccountID);//this->client?($this->client->ParentAccountID ?? $this->client->AccountID ):$this->accountUser->AccountID);
  Log::info(      'State'                                            .                                                    0);
  Log::info(      'StateClass'                                            .                                                    ''); // default
  Log::info(      'Identifier'                                            .                                                    $this->header['latefidentifier']); // default
  Log::info(      'AdminNote'                                            .                                                    ''); // default
  Log::info(      'AccountNote'                                            .                                                    ''); // default
  Log::info(      'Description'                                            .                                                    "null"); // default
  Log::info(      'Responded'                                            .                                                    0); // default
  Log::info(      'RequestInfo'                                            .                                                    $this->body['SID'].'#'.$this->body['SNO'].'#'.$this->body['AMT'].'#'.$this->body['FID']); // default
  Log::info(      'ResponseTime'                                            .                                                   date('Y-m-d H:i:s') );
  Log::info(      'ResponseStatus'                                            .                                                    0); // default
  Log::info(      'ExecutionPeroid'                                            .                                                    date('s')); // default
  Log::info(      'FaildRequest'                                            .                                                    0); // default
  Log::info(      'FailedReason'                                            .                                                    "null"); // default
  Log::info(      'FailedType'                                            .                                                    0); // default
  Log::info(      'Cured'                                            .                                                    0); // default
  Log::info(      'CuredBy'                                            .                                                    "null"); // default
  Log::info(      'CuredInfo'                                            .                                                    "null"); // default
  Log::info(      'InspectInfo'                                            .                                                   "null");
  Log::info(      'Flag'                                            .                                                    2);
  Log::info(      'Action'                                            .                                                    0); // default
  Log::info(      'QuotaionID'                                            .                                                    0); // default
  Log::info(      'SyncID'                                            .                                                   0); // default
            