<?php

namespace App\Services;

use App\Helpers\EncreptData;
use App\Jobs\CheckTopup;
use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use App\Jobs\InspectTopup;
use App\Jobs\ProcessTopupPlanResubscriber;
use App\Models\Journal;
use DateTime;
use Illuminate\Support\Env;
use App\Repositories\ReverseUnderRepository;
use Illuminate\Validation\ValidationException;
use <PERSON><PERSON><PERSON>gmann\Invoker\TimeoutException;
use App\Models\Faction;
use stdClass;

// TODO use inteface for topup gateway
class NetCoinService
{
    private $topup;

    private $Journal;
    public $returnResponse = 1;
    public function __construct($topup, $Journal)
    {
        Log::info("transaction14");

        $this->topup = $topup;
        $this->Journal = $Journal;
    }
 


public static function inquery($phone)
    {

        $userSanaOrAden =   1; 
            Log::info("first inquery phone = ".$phone);
            $encreptData = new EncreptData();
            $operation_token = '98' . '#' . $phone;
            $data = ($userSanaOrAden==1 ? 'Nawafeth777770954Mobile' : 'n777770954') . '#' . 0 . '#' . 'QuerySubsBalance'; // change user
            $userToken =  $encreptData->encryptDataRakan($data, $userSanaOrAden);
            
            $operation_token_encrypt =  $encreptData->encryptDataRakan($operation_token, $userSanaOrAden);
            $response = $encreptData->RestApiRakan($userToken, $operation_token_encrypt, $userSanaOrAden);
            Log::debug("response querySubBalance"); 
            Log::debug($response->body());
            $body = xml_to_array($response->body())['s:Body']['DoOperationResponse']['DoOperationResult'];
            Log::debug($body);

            $body =  explode('#', $body);
            Log::debug($body);
            
            if ($response && $response->ok()) {
                if (strtolower($body[0]) == "ok") {
                    Log::debug( "print  before faction by provider price" );  
                    $lprice=str_replace(',','',$body[2]);
                    $faction = Faction::where('ServiceID', 200)->where('ProviderPrice',str_replace(',','',$body[2]))->first();             

                  // $faction = \DB::TABLE('ServiceID', 200)->first(); 
                  Log::debug( "print  after faction by provider price" );            
                   Log::debug( $faction->Name );
                 
                  // Log::debug( $faction??"nofound");
                   $object = new stdClass;
                    $object->Success = true;
                    $object->Message = "رصيدك".' '.$body[3];
                    
                    $object->Balance =$body[4] .' | '. $body[3] .' رصيدك '.str_replace('ريال','', $faction->Name) ;
                    $object->LineType = " دفع مسبق ";
                    $object->Credit = null;
                    $object->SubDetail = null;
                    $object->Offers = null;
                    $object->LoanStatus = null;
                    Log::debug( "print1" );  
                    $object->mybondle=str_replace(',','',$body[2]);
                    Log::debug( "print  2" );  
                    return $object;
                }
                else {
                    throw ValidationException::withMessages([
                        'exception' => ['TimeOut'],
                    ]);  
                }
            }
            else {
                throw ValidationException::withMessages([
                    'exception' => ['TimeOut'],
                ]);
    
            }
 
        
    }
    public static function confirmProcess($topup, $typeProcess)
    {
    $userSanaOrAden= in_array($topup->ServiceID, [40, 20024]) ? 2 : 1;   // 1 sana   2 aden
        Log::info("transaction15");

        $phone =  $topup->SubscriberNumber;
        $amount = $topup->ProviderPrice;
        $myRefID = $topup->EntryID;



        // replace 1 in opreation token
        Log::info("transaction16");

        // 'mb776529802'   //chenge Data  USER

        $operation_token = in_array($topup->ServiceID, [40]) ? 73 : 98 . '#' . $phone . '#' . $amount . '#0';  //  . '#' .$topup->BundleCode ;
        $data = ($userSanaOrAden==1 ? 'Nawafeth777770954Mobile' : 'n777770954') . '#' . $myRefID . '#' . $typeProcess;
        $encreptData = new EncreptData();

        Log::info( in_array($topup->ServiceID, [40]) ? 73 : 98 . '#' . $phone . '#' . $amount . '#0' ); //.$topup->BundleCode  );

        $userToken =  $encreptData->encryptDataRakan($data,$userSanaOrAden);

        $operation_token_encrypt =  $encreptData->encryptDataRakan($operation_token,$userSanaOrAden);
        $response = $encreptData->RestApiRakan($userToken, $operation_token_encrypt,$userSanaOrAden);
        Log::info("transaction18");

        return  $response;
    }


    public function confirm()
    {

        Log::info("transaction19");

        $response = null;
        try {

            $response = self::confirmProcess($this->topup, 'DoPayment');

            if (!($response && $response->ok())) {
                $this->topup->ResponseInfo = 'timeout';
                $this->topup->ResponseStatus = 0;
                $this->topup->Status = 0;
                $this->topup->Debited = 0;
                $this->topup->Note = "العملية فاشلة";
                $this->topup->FailedReason = "فشل في اجراء العملية";
                $this->topup->ProviderRM = "فشل في اجراء العملية";
                $this->topup->StateClass = "العملية فاشلة";
                $this->topup->AccountNote = "فشل في اجراء العملية تم الغاء العملية";
                $this->topup->AdminNote = "فشل في اجراء العملية تم الغاء العملية";
                $this->topup->ResponseTime = null;
                $this->topup->Responded = 0;
                $this->topup->save();
                Log::info("transaction20");
                throw ValidationException::withMessages([
                    'exception' => ['TimeOut'],
                ]);
            }

            $bodyAll = xml_to_array($response->body())['s:Body']['DoOperationResponse']['DoOperationResult'];

            $body =  explode('#', $bodyAll);


            // $ExecutionPeroid=date('s');
            Log::info($body);

            $this->topup->ExecutionPeroid = date('s');
            if (strtolower($body[0]) == "hold") {
                $this->topup->RefNumber = $body[3];
                $this->topup->ResponseReference = $body[3];
                $this->topup->ResponseInfo = $bodyAll;
                // $this->topup->ResponseStatus = $response->status();
                $this->topup->Status = 2;
                $this->topup->Note = "العملية معلقة";
                $this->topup->ProviderRM = "العملية معلقة";
                $this->topup->StateClass = "العملية معلقة";
                $this->topup->Responded = 1;

                $this->topup->ResponseTime = date('Y-m-d H:i:s');

                $this->topup->save();


                throw ValidationException::withMessages([
                    'exception' => ['response check'],
                ]);
                // return CheckTopup::dispatch($this->topup)->delay(now()->addMinutes(1));

            }

            if (strtolower($body[0]) == "error") {
                $this->topup->ResponseInfo = $bodyAll;
                $this->topup->ResponseStatus = 0;
                $this->topup->Status = 0;
                $this->topup->Debited = 0;
                $this->topup->Note = "العملية فاشلة";
                $this->topup->FailedReason = "فشل في اجراء العملية";
                $this->topup->ProviderRM = "فشل في اجراء العملية";
                $this->topup->StateClass = "العملية فاشلة";
                $this->topup->AccountNote = "فشل في اجراء العملية تم الغاء العملية";
                $this->topup->AdminNote = "فشل في اجراء العملية تم الغاء العملية";
                $this->topup->ResponseTime = date('Y-m-d H:i:s');
                $this->topup->Responded = 1;
                $this->topup->save();
                throw ValidationException::withMessages([
                    'exception' => ['response faild'],
                ]);
                // throw new Exception('response faild');
            }
            $this->topup->Status = 1;
            $this->topup->BillState = 0;
            $this->topup->RefNumber = $body[3];
            $this->topup->ResponseInfo = $bodyAll;
            $this->topup->ResponseStatus = 1;
            $this->topup->Note = "العملية ناجحة";
            $this->topup->StateClass = "جاهز";
            $this->topup->ProviderRM = "جاهز";
            $this->topup->AccountNote = "تمت العملية بنجاح";
            $this->topup->AdminNote = "تمت العملية بنجاح";
            $this->topup->Responded = 1;
            $this->topup->ResponseReference = $body[3];

            $this->topup->save();
            return  1;
        } catch (\Throwable $exception) {
            Log::debug($this->topup);

            if ($exception instanceof ValidationException) {

                Log::debug($exception->getMessage());

                if (str_contains($exception->getMessage(), 'response check')) {
                    CheckTopup::dispatch($this->topup, $this->Journal)->delay(now()->addSeconds(10));
                    return  1;
                }
                Log::debug("validateee");
                Log::debug($exception->getMessage());
            }
            
            
            else {
                if ( $response != null && str_contains($exception->getMessage(), 'timed out')) {
                    CheckTopup::dispatch($this->topup, $this->Journal)->delay(now()->addSeconds(10));
                    return  1;
                }



               // $this->topup->ResponseInfo = json_encode(["provider" => $response != null ? $response->body() : '', "NetCoin" => $exception->getMessage()]);
                $this->topup->save();
            }


            $this->Journal->Status = 0;
            $this->Journal->Debited = 0;
            $this->Journal->save();
            return  0;
        }
    }

    public function check()
    {
        $response = null;


        try {
            Log::debug("check topup");
            $response = self::confirmProcess($this->topup, "QueryTRStatus2");
            Log::debug("check topup 1 confirmProcess");

            if (!($response && $response->ok())) {
                $this->topup->ResponseInfo = 'TimeOUt';
                $this->topup->ResponseStatus = 0;
                $this->topup->Status = 0;
                $this->topup->Debited = 0;
                $this->topup->Note = "العملية فاشلة.";
                $this->topup->FailedReason = "فشل في اجراء العملية";
                $this->topup->ProviderRM = "فشل في اجراء العملية";
                $this->topup->StateClass = "العملية فاشلة";
                $this->topup->AccountNote = "فشل في اجراء العملية تم الغاء العملية";
                $this->topup->AdminNote = "فشل في اجراء العملية تم الغاء العملية";
                $this->topup->ResponseTime = null;
                $this->topup->Responded = 0;
                $this->topup->save();
                Log::debug("check topup 2");

                throw new Exception('response faild');
            }
            Log::debug("check topup 3");

            $bodyAll = xml_to_array($response->body())['s:Body']['DoOperationResponse']['DoOperationResult'];
            $body =  explode('#', $bodyAll);
            Log::debug("check topup 4");

            $this->topup->ResponseInfo = $bodyAll;
            if (strtolower($body[1]) == "hold") {
                return CheckTopup::dispatch($this->topup, $this->Journal)->delay(now()->addSeconds(10));
            }
            if (strtolower($body[0]) == "error" && strtolower($body[1]) == "not found") {
                $this->topup->Status = 0;
                $this->topup->Debited = 0;
                $this->topup->Note = "العملية فاشلة";
                $this->topup->ProviderRM = "العملية معلقة";
                $this->topup->StateClass = "العملية فاشلة";
                $this->topup->AccountNote = "فشل في اجراء العملية تم الغاء العملية";
                $this->topup->AdminNote = "فشل في اجراء العملية تم الغاء العملية";
                $this->topup->FailedReason = "فشل في اجراء العملية";
                $this->topup->Responded = 1;
                // 0 faild  1 complited  // 2 hold // 5 time out // 6 reverse
                $this->topup->save();
                throw new Exception('response failed');
                // return;
            }

            if (strtolower($body[0]) == "error" || strtolower($body[1]) == "failed") {
                $this->topup->Status = 0;
                $this->topup->Debited = 0;
                $this->topup->Note = "العملية فاشلة";
                $this->topup->StateClass = "العملية فاشلة";
                $this->topup->ProviderRM = "فشل في اجراء العملية";
                $this->topup->AccountNote = "فشل في اجراء العملية تم الغاء العملية";
                $this->topup->AdminNote = "فشل في اجراء العملية تم الغاء العملية";
                $this->topup->FailedReason = "فشل في اجراء العملية";
                $this->topup->Responded = 1;

                $this->topup->ResponseStatus = 0;
                $this->topup->save();
                throw new Exception('respose failed');
            }

            $this->topup->Status = 1;
            $this->topup->BillState = 0;
            $this->topup->Note = "العملية ناجحة";
            $this->topup->StateClass = "جاهز";
            $this->topup->ProviderRM = "جاهز";
            $this->topup->AccountNote = "تمت العملية بنجاح";
            $this->topup->AdminNote = "تمت العملية بنجاح";
            $this->topup->Responded = 1;

            $this->topup->ResponseStatus = 1;
            $this->topup->save();
        } catch (\Throwable $exception) {
            Log::debug('debug Throwable');
          //  $this->topup->ResponseInfo = json_encode(["provider" => $response != null ? $response->body() : '', "NetCoin" => $exception->getMessage()]);
            $this->topup->save();
            $this->Journal->Status = 0;
            $this->Journal->Debited = 0;
            $this->Journal->save();
            return  0;
        }
    }
}