<?php

namespace App\Services;

use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use App\Helpers\EncreptData;
use App\Jobs\CheckTopup;
use App\Jobs\ProcessTopupPlanResubscriber;
use App\Models\Bagat;
use App\Models\CardOrder;
use App\Models\CardType;
use App\Models\Journal;
use DateTime;
use Illuminate\Support\Env;
use App\Repositories\ReverseUnderRepository;
use Illuminate\Validation\ValidationException;
use SebastianBergmann\Invoker\TimeoutException;
use App\Models\Faction;
use stdClass;
use App\Models\JournalEntry;
use App\Models\OrderInfo;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Crypt;

// TODO use inteface for topup gateway
class TwasulService
{
    private $topup;

    private $Journal;
    public $returnResponse = 1;
    Public $paymentOrOrder;
    private $orderInfo;
    private $cardType;
    public function __construct($topup, $Journal, $topupOrGame = 'TOPUP')
    {
       
        Log::info("transaction14");
        if ($topupOrGame == 'GAME') {
            $this->paymentOrOrder=$topup;
        } 
        else {
            $this->topup = $topup;
          
        }
        $this->Journal = $Journal;
    }
    public function encoderPassAndToken($myRefID, $phone,$isM)
    {
        if($isM){
            $Username =  'asd123';
            $Password =  'o2n1eld';
        }else{
            $Username =  'nawafdhsouth';
            $Password =  'rwat0hn';
        }
       
        $encodePassword = md5($Password);
        $encodeToken = md5($encodePassword . $myRefID . $Username . $phone);

        return $encodeToken;
    } 
 
    public static function confirmProcess($topup, $url )
    {
        $userSanaOrAden = in_array($topup->ServiceID, [40, 20024,20033]) ? 2 : 1;   // 1 sana   2 aden
        Log::info("transaction15");
        $response = Http::asJson()
            ->timeout(60) // 1 Mi
            ->post($url);
        Log::info($response->json());

        return  $response;
    }


    public function confirm()
    {
        Log::info("transaction19");

        $response = null;
        try {
            $phone =  $this->topup->SubscriberNumber;
            Log::info("transaction19#.");

            $amount = $this->topup->ProviderPrice;
            $myRefID = $this->topup->EntryID;
            $sid = $this->topup->ServiceID;
            Log::info("transaction19#.");

            if ($sid == 2) {
                $codeBundle = $amount;
            }
            else if ($sid == 20033){
                $faction = Bagat::where('ServiceID', $sid)->where('Number', $this->topup->FactionID)->first();

                if ($faction) {
                    $codeBundle = $faction->Code;
                } else {
                    $codeBundle = null;
                }

            } 
            else {
                $faction = Faction::where('id', $this->topup->FactionID)->first();
                if ($faction) {
                    $codeBundle = $amount;
                } else {
                    $codeBundle = null;
                }
            }

            Log::info("transaction19#");


            $url = "https://wkala.yemoney.net/api/yr/" .
                ($this->topup->ServiceID == 2 ? "mtn" : ($this->topup->ServiceID == 20033 ? 'mtnoffer': "adenet")) . "?action=bill" .
                ($this->topup->ServiceID == 2 ? "&type=" . (($this->topup->LineType == "2" || $this->topup->LineType == 2) ? "postpaid" : "prepaid") : "") .
                ($this->topup->ServiceID == 2 ? ("&israsid=" . "1") : "") .
                "&userid=" . (($this->topup->ServiceID == 2 ) ? "5284" : '4936') .
                "&mobile=" .  $phone .
                "&transid=" .  $myRefID .
                "&token=" . $this->encoderPassAndToken($myRefID, $phone, ($this->topup->ServiceID == 2)) .
                "&num=" . $codeBundle;  // here code bundle to provider
            Log::info("transaction19##");

            $response = self::confirmProcess($this->topup,  $url);

            $this->topup->ExecutionPeroid = date('s');


            if (!($response && $response->ok())) {
                $this->topup->ResponseInfo = 'timeout';
                $this->topup->ResponseStatus = 0;
                $this->topup->Status = 0;
                $this->topup->Debited = 0;
                $this->topup->Note = "العملية فاشلة";
                $this->topup->FailedReason = "فشل في اجراء العملية";
                $this->topup->ProviderRM = "فشل في اجراء العملية";
                $this->topup->StateClass = "العملية فاشلة";
                $this->topup->AccountNote = "فشل في اجراء العملية تم الغاء العملية";
                $this->topup->AdminNote = "فشل في اجراء العملية تم الغاء العملية";
                $this->topup->ResponseTime = null;
                $this->topup->Responded = 0;
                $this->topup->save();

                Log::info("transaction20");
                throw ValidationException::withMessages([
                    'exception' => ['TimeOut'],
                ]);
            }

            if ($response && $response->json('resultCode') != "0") {
                $this->topup->RefNumber = intval($response->json('resultCode'));
                $this->topup->ResponseReference = intval($response->json('resultCode'));
                $this->topup->ResponseInfo = json_encode([$response->json()]);
                $this->topup->ResponseStatus = 0;
                $this->topup->Status = 0;
                $this->topup->Debited = 0;
                $this->topup->Note = "العملية فاشلة";
                $this->topup->FailedReason = "فشل في اجراء العملية";
                $this->topup->ProviderRM = $response->json('resultDesc');
                $this->topup->StateClass = "العملية فاشلة";
                $this->topup->AccountNote = "فشل في اجراء العملية تم الغاء العملية";
                $this->topup->AdminNote = "فشل في اجراء العملية تم الغاء العملية";
                $this->topup->ResponseTime = date('Y-m-d H:i:s');
                $this->topup->Responded = 1;
                $this->topup->save();
                throw new Exception('assertion failed');
            }



            if ($response && $response->json('resultCode') == "0" && Str::contains($response->body(), 'under')) {

                $this->topup->ResponseInfo = json_encode([$response->json()]);
                // $this->topup->ResponseStatus = $response->status();
                $this->topup->Status = 2;
                $this->topup->Note = "العملية معلقة";
                $this->topup->ProviderRM = "العملية معلقة";
                $this->topup->StateClass = "العملية معلقة";
                $this->topup->Responded = 1;

                $this->topup->ResponseTime = date('Y-m-d H:i:s');

                $this->topup->save();
                throw ValidationException::withMessages([
                    'exception' => ['response check'],
                ]);


                // return null;
            }

            $this->topup->Status = 1;
            $this->topup->BillState = 0;
            $this->topup->RefNumber = $response->json('ref_id');
            $this->topup->ResponseInfo = json_encode([$response->json()]);
            $this->topup->ResponseStatus = 1;
            $this->topup->Note = "العملية ناجحة";
            $this->topup->StateClass = "جاهز";
            $this->topup->ProviderRM = "جاهز";
            $this->topup->AccountNote = "تمت العملية بنجاح";
            $this->topup->AdminNote = "تمت العملية بنجاح";
            $this->topup->Responded = 1;
            $this->topup->ResponseReference = $response->json('ref_id');

            $this->topup->save();
            return  1;
        } catch (\Throwable $exception) {
            Log::debug($exception->getMessage());

            Log::debug($this->topup);

            if ($exception instanceof ValidationException) {

                Log::debug($exception->getMessage());

                if (str_contains($exception->getMessage(), 'response check')) {

                    CheckTopup::dispatch($this->topup, $this->Journal)->delay(now()->addSeconds(10));
                    return  1;
                }
                Log::debug("validateee");
                Log::debug($exception->getMessage());
            } else {
                $this->topup->ResponseInfo = json_encode(["provider" => $response != null ? $response->body() : '', "twasul" => $exception->getMessage()]);
                $this->topup->save();
            }


            $this->Journal->Status = 0;
            $this->Journal->Debited = 0;
            $this->Journal->save();
            return  0;
        }
    }

    public function check()
    {
        Log::info("transaction19");

        $response = null;
        try {
            $phone =  $this->topup->SubscriberNumber;
            $amount = $this->topup->ProviderPrice;
            $myRefID = $this->topup->EntryID;

            $url = "https://wkala.yemoney.net/api/yr/" .
                "info?" .
                "&userid=" . (($this->topup->ServiceID == 2 ) ? "5284" : '4936') .
                "&mobile=" .  $phone .
                "&transid=" .  $myRefID .
                "&token=" . $this->encoderPassAndToken($myRefID, $phone, ($this->topup->ServiceID == 2 )) .
                "&action=status";


            $response = self::confirmProcess($this->topup,  $url);
           // $this->topup->ExecutionPeroid = date('s');

            if (!($response && $response->ok())) {
                $this->topup->ResponseInfo = 'timeout';
                $this->topup->ResponseStatus = 0;
                $this->topup->Status = 0;
                $this->topup->Debited = 0;
                $this->topup->Note = "العملية فاشلة";
                $this->topup->FailedReason = "فشل في اجراء العملية";
                $this->topup->ProviderRM = "فشل في اجراء العملية";
                $this->topup->StateClass = "العملية فاشلة";
                $this->topup->AccountNote = "فشل في اجراء العملية تم الغاء العملية";
                $this->topup->AdminNote = "فشل في اجراء العملية تم الغاء العملية";
                $this->topup->ResponseTime = null;
                $this->topup->Responded = 0;
                $this->topup->save();
                Log::info("transaction20");
                throw ValidationException::withMessages([
                    'exception' => ['TimeOut'],
                ]);
            }

            if ($response && $response->json("isBan") == "1") {
                $this->topup->ResponseInfo = json_encode([$response->json(),true]);
                $this->topup->ResponseStatus = 0;
                $this->topup->Status = 0;
                $this->topup->Debited = 0;
                $this->topup->Note = "العملية فاشلة";
                $this->topup->FailedReason = "فشل في اجراء العملية";
                $this->topup->ProviderRM = "فشل في اجراء العملية";
                $this->topup->StateClass = "العملية فاشلة";
                $this->topup->AccountNote = "فشل في اجراء العملية تم الغاء العملية";
                $this->topup->AdminNote = "فشل في اجراء العملية تم الغاء العملية";
               // $this->topup->ResponseTime = date('Y-m-d H:i:s');
                $this->topup->Responded = 1;
                $this->topup->save();
                throw new Exception('assertion failed');
            }


            if ($response && $response->json('resultCode') == "0" && Str::contains($response->body(), 'under')) {

                throw ValidationException::withMessages([
                    'exception' => ['response check'],
                ]);
            }

            if ($response && $response->json("isDone") == "1") {

                $this->topup->Status = 1;
                $this->topup->BillState = 0;
                $this->topup->RefNumber = $response->json('sequenceId');
                $this->topup->ResponseInfo = json_encode([$response->json()]);
                $this->topup->ResponseStatus = 1;
                $this->topup->Note = "العملية ناجحة";
                $this->topup->StateClass = "جاهز";
                $this->topup->ProviderRM = "جاهز";
                $this->topup->AccountNote = "تمت العملية بنجاح";
                $this->topup->AdminNote = "تمت العملية بنجاح";
                $this->topup->Responded = 1;
                $this->topup->ResponseReference = $response->json('sequenceId');
                // if ((number_format(floatval($response->json('price')), 2, ".", "")) != (number_format(floatval((($this->topup->ServiceID == 2 || $this->topup->ServiceID == 20033) ?  $this->topup->ProviderPrice : $faction->ProviderPrice), 2, ".", "")))) {
                //     Log::debug("journalEntry provider after response");
                //     $journalEntry = JournalEntry::where('ParentID', $this->Journal->ID)->where('AccountID', ($this->topup->ServiceID == 2 || $this->topup->ServiceID == 20033) ? 254108 : 316581)->first();
                //     if ($journalEntry) {
                //         Log::debug("journalEntry after response true");
                //         $journalEntry->DCAmount = (number_format(floatval($response->json('price')), 2, ".", ""));
                //         $journalEntry->Amount = (number_format(floatval($response->json('price')), 2, ".", ""));
                //         $journalEntry->save();
                //     }
                //     $journalEntry = JournalEntry::where('ParentID', $this->Journal->ID)->where('AccountID', 222839)->first();
                //     Log::debug("journalEntry commision after response");
                //     if ($journalEntry) {
                //         Log::debug("journalEntry commision after response true");
                //         $journalEntry->DCAmount = (number_format(floatval($this->topup->TotalAmount), 2, ".", "")) -  (number_format(floatval($response->json('price')), 2, ".", ""));
                //         $journalEntry->Amount = (number_format(floatval($this->topup->TotalAmount), 2, ".", "")) -  (number_format(floatval($response->json('price')), 2, ".", ""));
                //         $journalEntry->save();
                //     }
                //     Log::debug("update in topup UnitCost");
                //     $this->topup->UnitCost = ((number_format(floatval($response->json('price')), 2, ".", ""))) / $this->topup->ServiceID == 2 ? $this->topup->Quantity : ( $this->topup->Quantity);
                //     Log::debug("update in topup CostAmount");
                //     $this->topup->CostAmount = (number_format(floatval($response->json('price')), 2, ".", ""));
                //     Log::debug("update in topup Profits");
                //     $this->topup->Profits = ($this->topup->TotalAmount) - ((number_format(floatval($response->json('price')), 2, ".", "")));
                //     Log::debug("update in topup UnitCost");
                // }
                $this->topup->save();
                return  1;
            }
        } catch (\Throwable $exception) {

            if ($exception instanceof ValidationException) {

                Log::debug($exception->getMessage());

                if (str_contains($exception->getMessage(), 'response check')) {
                    CheckTopup::dispatch($this->topup, $this->Journal)->delay(now()->addSeconds(10));
                    return  1;
                }
                Log::debug("validateee");
                Log::debug($exception->getMessage());
            } else {
             //   $this->topup->ResponseInfo = json_encode(["provider" => $response->json() != null ? json_encode($response->json()) : '', "NetCoin" => $exception->getMessage()]);
                $this->topup->save();
            }


            $this->Journal->Status = 0;
            $this->Journal->Debited = 0;
            $this->Journal->save();
            return  0;
        }
    }

    public function confirmGame()
    {
        Log::info("transaction19");
 
        $this->orderInfo = OrderInfo::where('ID',$this->paymentOrOrder->ParentID)->first();
        $this->cardType = DB::table("CardType")->where('ID',$this->paymentOrOrder->CardTypeID)->first();// CardType::where('CardTypeID',$this->paymentOrOrder->CardTypeID)->first();
         
       // Log::info($this->orderInfo);
        $response = null;
        try {
            
            $phone =  $this->paymentOrOrder->Phone;
            Log::info("transaction19#.");

            // $amount = $this->topup->ProviderPrice;
            $myRefID = $this->orderInfo->EntryID;
            
            $cardFaction = $this->paymentOrOrder->cardFaction; 
            if($this->paymentOrOrder instanceof CardOrder){
               $playerid=$this->paymentOrOrder->Username;
            }
            else{
                $playerid='';
            }
            // $sid =$this->topup->ServiceID;
            Log::info("transaction19#."); 


            $url = "https://wkala.yemoney.net/api/yr/gameswcards".
                "?userid=" . '5284'.
                "&mobile=" .  $phone .
                "&transid=" .  $myRefID .
                "&token=" . $this->encoderPassAndToken($myRefID, $phone,true) .
                "&type=" . $this->cardType->Note.
                "&uniqcode=" .  $cardFaction->Number.
                "&playerid=" . $playerid .
               // "&player_id=" . $playerid .
                "&playername=" . $this->paymentOrOrder->Username ??'' .
                "&backurl="."http://abuosama.ddns.net:9802/PaymentService/web-hook-payment".
                "&zoneid=" . ''.
                "&backpass=" .  $myRefID.
                "&email=" . $this->paymentOrOrder->Email ?? '';  
                Log::info("transaction19##");
                Log::info( $myRefID);

            $response = self::confirmProcess($this->orderInfo,  $url);

            // $this->orderInfo->UsageTime = date('Y-m-d H:i:s.u');

            Log::info("transaction game 1");
            if (!($response && $response->ok())) {
                // $this->orderInfo->ResponseInfo = 'timeout'; // غير موجود
                // $this->orderInfo->ResponseStatus = 0;// غير موجود
                $this->orderInfo->Status = 3;
                $this->orderInfo->IsDebited = 0;  
                $this->orderInfo->IsRejected = 1;  
                // $this->orderInfo->Debited = 0;// غير موجود
                $this->orderInfo->Note = "العملية فاشلة";  
                $this->orderInfo->RejectReason = "فشل في اجراء العملية"; 
                $this->orderInfo->save();

                Log::info("transaction20");
                throw ValidationException::withMessages([
                    'exception' => ['TimeOut'],
                ]);
            }
            Log::info("transaction game 2");
            if ($response && $response->json('resultCode') != "0" && ! Str::contains($response->body(), 'under proccess')) {
                Log::info("transaction game 3");
                $this->orderInfo->RefNumber = intval($response->json('RefNumber'));
                Log::info("transaction game 4");
                // $this->orderInfo->ResponseReference = intval($response->json('resultCode')); ////not found 
                $this->orderInfo->Description =json_encode([$response->json()]);
                Log::info("transaction game 5");
                // $this->orderInfo->ResponseStatus = 0;  //not found 
                $this->orderInfo->Status = 3;
                $this->orderInfo->IsDebited = 0;  
                $this->orderInfo->IsRejected = 1;  
                 //not found 
                $this->orderInfo->SubNote = "العملية فاشلة";  
                $this->orderInfo->RejectReason = "فشل في اجراء العملية";
                Log::info("transaction game 6");
                $this->orderInfo->save();
                Log::info("transaction game 7");
                throw new Exception('assertion failed');
            }



            if ($response && Str::contains($response->body(), 'under proccess')) {

                $this->orderInfo->Description = json_encode([$response->json()]);
                // $this->orderInfo->ResponseStatus = $response->status();
                $this->orderInfo->Status = 1;
                $this->orderInfo->IsDebited = 1;  
                $this->orderInfo->IsRejected = 0;
                $this->orderInfo->SubNote = "العملية معلقة";
                
                $this->orderInfo->save();
                throw ValidationException::withMessages([
                    'exception' => ['response check'],
                ]);


                // return null;
            }

            $this->orderInfo->Status = 1;
            $this->orderInfo->IsDebited = 1;  
            $this->orderInfo->IsRejected = 0;
            $this->orderInfo->RefNumber = $response->json('ref_id');
            $this->orderInfo->Description =json_encode([$response->json()]);
            $this->orderInfo->SubNote = "العملية ناجحة";

            $this->orderInfo->save();
            return  1;
        } catch (\Throwable $exception) {
            Log::info("transaction19##catch");
            Log::debug( $exception->getMessage());
           // Log::debug( $this->orderInfo);
             

            if ($exception instanceof ValidationException) {
                Log::info("transaction game 8");
                Log::debug($exception->getMessage());

                if (str_contains($exception->getMessage(), 'response check')) {

                //    CheckTopup::dispatch($this->paymentOrOrder, $this->Journal , 'GAME')->delay(now()->addSeconds(60));
                    return  1;
                }
                Log::debug("validateee");
                Log::debug($exception->getMessage());
            } else {
                $this->orderInfo->SubNote =$response ==null ?  $exception->getMessage() :   json_encode(["provider" => $response?->json() != null ? json_encode($response?->json()) : '']);
                $this->orderInfo->save();
            }
            Log::info("transaction game 9");

            $this->Journal->Status = 0;
            $this->Journal->Debited = 0;
            $this->Journal->save();
            Log::info("transaction game 10");
           // CheckTopup::dispatch($this->paymentOrOrder, $this->Journal , 'GAME')->delay(now()->addSeconds(10));

            // CheckTopup::dispatch($this->paymentOrOrder, $this->Journal , 'GAME');
            Log::info("transaction game 11");
            return  0;
        }
    }

    public function checkGame()
    {
        Log::info("checkGame");
        Log::info("transaction19");
        $this->orderInfo = OrderInfo::where('ID',$this->paymentOrOrder->ParentID)->first();
        $this->cardType = CardType::where('ID',$this->paymentOrOrder->CardTypeID)->first();
        $phone =  $this->paymentOrOrder->Phone;
        Log::info("transaction19#.");

        // $amount = $this->topup->ProviderPrice;
        $myRefID = $this->orderInfo->EntryID;
        
        $cardFaction = $this->paymentOrOrder->cardFaction; 
        if($this->paymentOrOrder instanceof CardOrder){
           $playerid=$this->paymentOrOrder->Username ??'';
        }
        else{
            $playerid='';
        }

        $response = null;
        try {
           
           // $myRefID = $this->topup->EntryID;

            $url = "https://wkala.yemoney.net/api/yr/" .
                "info?" .
                "&userid=" .  '5284'.
                "&mobile=" .  $phone .
                "&transid=" .  $myRefID .
                "&token=" . $this->encoderPassAndToken($myRefID, $phone, true) .
                "&action=status";
           Log::info('check game 1');

            $response = self::confirmProcess($this->orderInfo,  $url);
            // $this->orderInfo->UsageTime = date('Y-m-d H:i:s.u');
            Log::info('check game 2');
            if (!($response && $response->ok())) {
               // $this->orderInfo->ResponseInfo = 'timeout'; // غير موجود
                // $this->orderInfo->ResponseStatus = 0;// غير موجود
                $this->orderInfo->Status = 0;
                // $this->orderInfo->Debited = 0;// غير موجود
                $this->orderInfo->SubNote = "العملية فاشلة";  
                $this->orderInfo->RejectReason = "فشل في اجراء العملية"; 
                $this->orderInfo->save();

                Log::info("transaction20");
                throw ValidationException::withMessages([
                    'exception' => ['TimeOut'],
                ]);
            }

            if ($response && $response->json("isBan") == "1") {
                Log::info('check game 3');
                $this->orderInfo->Status = 0;
                // $this->orderInfo->Debited = 0;// غير موجود
                $this->orderInfo->Note = "العملية فاشلة";  
                $this->orderInfo->RejectReason = "فشل في اجراء العملية"; 
                $this->orderInfo->save(); 
                throw new Exception('assertion failed');
            }


            if ($response && Str::contains($response->body(), 'under')) {

                throw ValidationException::withMessages([
                    'exception' => ['response check'],
                ]);
            }

            if ($response && $response->json("isDone") == "1") {

                Log::info('check game 4');

                $this->orderInfo->Status = 1;
                // $this->orderInfo->BillState = 0;
                $this->orderInfo->RefNumber = $response->json('sequenceId');
                $this->orderInfo->Description =json_encode([$response->json()]);
                // $this->orderInfo->ResponseStatus = 1;
                $this->orderInfo->SubNote = "العملية ناجحة";
               
    
                $this->orderInfo->save();


                
                
                return  1;
            }
        } catch (\Throwable $exception) {

            if ($exception instanceof ValidationException) {

                Log::debug($exception->getMessage());

                if (str_contains($exception->getMessage(), 'response check')) {
                    CheckTopup::dispatch($this->paymentOrOrder, $this->Journal , 'GAME')->delay(now()->addSeconds(10));
                    return  1;
                }
                Log::debug("validateee");
               
            } else {
                $this->orderInfo->SubNote =$response ==null ?  $exception->getMessage() :   json_encode(["provider" => $response?->json() != null ? json_encode($response?->json()) : '']);

                $this->orderInfo->save();
            }


            $this->Journal->Status = 0;
            $this->Journal->Debited = 0;
            $this->Journal->save();
            return  0;
        }
    }



}
