[2023-03-31 00:43:10] local.INFO: header  
[2023-03-31 00:43:10] local.INFO: header after fliter  
[2023-03-31 00:43:10] local.INFO: Body  after fliter  
[2023-03-31 00:43:10] local.INFO: array (
)  
[2023-03-31 00:43:10] local.INFO: transaction14  
[2023-03-31 00:43:10] local.INFO: first inquery phone = 106333522  
[2023-03-31 00:43:13] local.DEBUG: response querySubBalance  
[2023-03-31 00:43:13] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#8,000.00#8,000.00#0.00 B#07-04-2023#0#0##.00#4G 60</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-31 00:43:13] local.DEBUG: array (
  0 => 'OK',
  1 => '8,000.00',
  2 => '8,000.00',
  3 => '0.00 B',
  4 => '07-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 60',
)  
[2023-03-31 00:43:13] local.DEBUG: print  before faction by provider price  
[2023-03-31 00:43:13] local.DEBUG: print  after faction by provider price  
[2023-03-31 00:43:13] local.DEBUG: فئة 60 جيجا 8000 ريال  
[2023-03-31 00:43:13] local.DEBUG: print1  
[2023-03-31 00:43:13] local.DEBUG: print  2  
[2023-03-31 00:43:54] local.INFO: header  
[2023-03-31 00:43:54] local.CRITICAL: ****************************1  
[2023-03-31 00:43:54] local.ALERT: reach here  
[2023-03-31 00:43:54] local.ERROR: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '2400',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => '2.28',
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '5472.00',
  ),
  3 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'خمسة ألف وأربعمائة وإثنان وسبعون  ر.ي.',
  ),
)  
[2023-03-31 00:43:54] local.ERROR: الكمية  
[2023-03-31 00:43:54] local.ERROR: سعر الوحدة  
[2023-03-31 00:43:54] local.ERROR: المبلغ  
[2023-03-31 00:43:54] local.ERROR: 5,472.00  
[2023-03-31 00:43:54] local.ERROR: 2,400.00  
[2023-03-31 00:44:08] local.INFO: header  
[2023-03-31 00:44:08] local.INFO: header after fliter  
[2023-03-31 00:44:08] local.INFO: Body  after fliter  
[2023-03-31 00:44:08] local.INFO: array (
)  
[2023-03-31 00:44:08] local.INFO: transaction14  
[2023-03-31 00:44:08] local.INFO: first inquery phone = 106333522  
[2023-03-31 00:44:11] local.DEBUG: response querySubBalance  
[2023-03-31 00:44:11] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#8,000.00#8,000.00#0.00 B#07-04-2023#0#0##.00#4G 60</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-31 00:44:11] local.DEBUG: array (
  0 => 'OK',
  1 => '8,000.00',
  2 => '8,000.00',
  3 => '0.00 B',
  4 => '07-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 60',
)  
[2023-03-31 00:44:11] local.DEBUG: print  before faction by provider price  
[2023-03-31 00:44:11] local.DEBUG: print  after faction by provider price  
[2023-03-31 00:44:11] local.DEBUG: فئة 60 جيجا 8000 ريال  
[2023-03-31 00:44:11] local.DEBUG: print1  
[2023-03-31 00:44:11] local.DEBUG: print  2  
[2023-03-31 00:44:19] local.INFO: header  
[2023-03-31 00:44:19] local.CRITICAL: ****************************1  
[2023-03-31 00:44:19] local.ALERT: reach here  
[2023-03-31 00:44:19] local.ERROR: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '2400',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => '2.28',
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '5472.00',
  ),
  3 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'خمسة ألف وأربعمائة وإثنان وسبعون  ر.ي.',
  ),
)  
[2023-03-31 00:44:19] local.ERROR: الكمية  
[2023-03-31 00:44:19] local.ERROR: سعر الوحدة  
[2023-03-31 00:44:19] local.ERROR: المبلغ  
[2023-03-31 00:44:19] local.ERROR: 5,472.00  
[2023-03-31 00:44:19] local.ERROR: 2,400.00  
[2023-03-31 00:44:27] local.INFO: header  
[2023-03-31 00:44:27] local.INFO: header after fliter  
[2023-03-31 00:44:27] local.INFO: Body  after fliter  
[2023-03-31 00:44:27] local.INFO: array (
)  
[2023-03-31 00:44:27] local.INFO: transaction14  
[2023-03-31 00:44:27] local.INFO: first inquery phone = 106333522  
[2023-03-31 00:44:33] local.DEBUG: response querySubBalance  
[2023-03-31 00:44:33] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#8,000.00#8,000.00#0.00 B#07-04-2023#0#0##.00#4G 60</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-31 00:44:33] local.DEBUG: array (
  0 => 'OK',
  1 => '8,000.00',
  2 => '8,000.00',
  3 => '0.00 B',
  4 => '07-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 60',
)  
[2023-03-31 00:44:33] local.DEBUG: print  before faction by provider price  
[2023-03-31 00:44:33] local.DEBUG: print  after faction by provider price  
[2023-03-31 00:44:33] local.DEBUG: فئة 60 جيجا 8000 ريال  
[2023-03-31 00:44:33] local.DEBUG: print1  
[2023-03-31 00:44:33] local.DEBUG: print  2  
[2023-03-31 00:44:48] local.INFO: header  
[2023-03-31 00:44:48] local.INFO: header after fliter  
[2023-03-31 00:44:48] local.INFO: Body  after fliter  
[2023-03-31 00:44:48] local.INFO: array (
)  
[2023-03-31 00:44:48] local.INFO: transaction14  
[2023-03-31 00:44:48] local.INFO: first inquery phone = 106333522  
[2023-03-31 00:44:50] local.DEBUG: response querySubBalance  
[2023-03-31 00:44:50] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#8,000.00#8,000.00#0.00 B#07-04-2023#0#0##.00#4G 60</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-31 00:44:50] local.DEBUG: array (
  0 => 'OK',
  1 => '8,000.00',
  2 => '8,000.00',
  3 => '0.00 B',
  4 => '07-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 60',
)  
[2023-03-31 00:44:50] local.DEBUG: print  before faction by provider price  
[2023-03-31 00:44:50] local.DEBUG: print  after faction by provider price  
[2023-03-31 00:44:50] local.DEBUG: فئة 60 جيجا 8000 ريال  
[2023-03-31 00:44:50] local.DEBUG: print1  
[2023-03-31 00:44:50] local.DEBUG: print  2  
[2023-03-31 00:44:59] local.INFO: header  
[2023-03-31 00:44:59] local.CRITICAL: ****************************1  
[2023-03-31 00:44:59] local.ALERT: reach here  
[2023-03-31 00:44:59] local.ERROR: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '2400',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => '2.28',
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '5472.00',
  ),
  3 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'خمسة ألف وأربعمائة وإثنان وسبعون  ر.ي.',
  ),
)  
[2023-03-31 00:44:59] local.ERROR: الكمية  
[2023-03-31 00:44:59] local.ERROR: سعر الوحدة  
[2023-03-31 00:44:59] local.ERROR: المبلغ  
[2023-03-31 00:44:59] local.ERROR: 5,472.00  
[2023-03-31 00:44:59] local.ERROR: 2,400.00  
[2023-03-31 00:54:18] local.INFO: header  
[2023-03-31 00:54:18] local.INFO: header after fliter  
[2023-03-31 00:54:18] local.INFO: Body  after fliter  
[2023-03-31 00:54:18] local.INFO: array (
)  
[2023-03-31 00:54:18] local.INFO: transaction14  
[2023-03-31 00:54:18] local.INFO: first inquery phone = 106333522  
[2023-03-31 00:54:21] local.DEBUG: response querySubBalance  
[2023-03-31 00:54:21] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#8,000.00#8,000.00#0.00 B#07-04-2023#0#0##.00#4G 60</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-31 00:54:21] local.DEBUG: array (
  0 => 'OK',
  1 => '8,000.00',
  2 => '8,000.00',
  3 => '0.00 B',
  4 => '07-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 60',
)  
[2023-03-31 00:54:21] local.DEBUG: print  before faction by provider price  
[2023-03-31 00:54:21] local.DEBUG: print  after faction by provider price  
[2023-03-31 00:54:21] local.DEBUG: فئة 60 جيجا 8000 ريال  
[2023-03-31 00:54:21] local.DEBUG: print1  
[2023-03-31 00:54:21] local.DEBUG: print  2  
[2023-03-31 00:54:26] local.INFO: header  
[2023-03-31 00:54:26] local.CRITICAL: ****************************1  
[2023-03-31 00:54:26] local.ALERT: reach here  
[2023-03-31 00:54:26] local.ERROR: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '2400',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => '2.28',
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '5472.00',
  ),
  3 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'خمسة ألف وأربعمائة وإثنان وسبعون  ر.ي.',
  ),
)  
[2023-03-31 00:54:26] local.ERROR: الكمية  
[2023-03-31 00:54:26] local.ERROR: سعر الوحدة  
[2023-03-31 00:54:26] local.ERROR: المبلغ  
[2023-03-31 00:54:26] local.ERROR: 5,472.00  
[2023-03-31 00:54:26] local.ERROR: 2,400.00  
[2023-03-31 00:54:31] local.INFO: header  
[2023-03-31 00:54:31] local.CRITICAL: ****************************1  
[2023-03-31 00:54:31] local.ALERT: reach here  
[2023-03-31 00:54:31] local.ERROR: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '2400',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => '2.28',
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '5472.00',
  ),
  3 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'خمسة ألف وأربعمائة وإثنان وسبعون  ر.ي.',
  ),
)  
[2023-03-31 00:54:31] local.ERROR: الكمية  
[2023-03-31 00:54:31] local.ERROR: سعر الوحدة  
[2023-03-31 00:54:31] local.ERROR: المبلغ  
[2023-03-31 00:54:31] local.ERROR: 5,472.00  
[2023-03-31 00:54:31] local.ERROR: 2,400.00  
[2023-03-31 00:54:35] local.INFO: header  
[2023-03-31 00:54:35] local.CRITICAL: ****************************1  
[2023-03-31 00:54:35] local.ALERT: reach here  
[2023-03-31 00:54:35] local.ERROR: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '2400',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => '2.28',
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '5472.00',
  ),
  3 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'خمسة ألف وأربعمائة وإثنان وسبعون  ر.ي.',
  ),
)  
[2023-03-31 00:54:35] local.ERROR: الكمية  
[2023-03-31 00:54:35] local.ERROR: سعر الوحدة  
[2023-03-31 00:54:35] local.ERROR: المبلغ  
[2023-03-31 00:54:35] local.ERROR: 5,472.00  
[2023-03-31 00:54:35] local.ERROR: 2,400.00  
[2023-03-31 00:54:41] local.INFO: header  
[2023-03-31 00:54:41] local.CRITICAL: ****************************1  
[2023-03-31 00:54:41] local.ALERT: reach here  
[2023-03-31 00:54:41] local.ERROR: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '2400',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => '2.28',
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '5472.00',
  ),
  3 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'خمسة ألف وأربعمائة وإثنان وسبعون  ر.ي.',
  ),
)  
[2023-03-31 00:54:41] local.ERROR: الكمية  
[2023-03-31 00:54:41] local.ERROR: سعر الوحدة  
[2023-03-31 00:54:41] local.ERROR: المبلغ  
[2023-03-31 00:54:41] local.ERROR: 5,472.00  
[2023-03-31 00:54:41] local.ERROR: 2,400.00  
[2023-03-31 00:55:16] local.INFO: header  
[2023-03-31 00:55:16] local.CRITICAL: ****************************1  
[2023-03-31 00:55:16] local.ALERT: reach here  
[2023-03-31 00:55:16] local.ERROR: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '2400',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => '2.28',
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '5472.00',
  ),
  3 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'خمسة ألف وأربعمائة وإثنان وسبعون  ر.ي.',
  ),
)  
[2023-03-31 00:55:16] local.ERROR: الكمية  
[2023-03-31 00:55:16] local.ERROR: سعر الوحدة  
[2023-03-31 00:55:16] local.ERROR: المبلغ  
[2023-03-31 00:55:16] local.ERROR: 5,472.00  
[2023-03-31 00:55:16] local.ERROR: 2,400.00  
[2023-03-31 00:55:20] local.INFO: header  
[2023-03-31 00:55:20] local.INFO: header after fliter  
[2023-03-31 00:55:20] local.INFO: Body  after fliter  
[2023-03-31 00:55:20] local.INFO: array (
)  
[2023-03-31 00:55:20] local.INFO: transaction14  
[2023-03-31 00:55:20] local.INFO: first inquery phone = 106333522  
[2023-03-31 00:55:27] local.DEBUG: response querySubBalance  
[2023-03-31 00:55:27] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#8,000.00#8,000.00#0.00 B#07-04-2023#0#0##.00#4G 60</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-31 00:55:27] local.DEBUG: array (
  0 => 'OK',
  1 => '8,000.00',
  2 => '8,000.00',
  3 => '0.00 B',
  4 => '07-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 60',
)  
[2023-03-31 00:55:27] local.DEBUG: print  before faction by provider price  
[2023-03-31 00:55:27] local.DEBUG: print  after faction by provider price  
[2023-03-31 00:55:27] local.DEBUG: فئة 60 جيجا 8000 ريال  
[2023-03-31 00:55:27] local.DEBUG: print1  
[2023-03-31 00:55:27] local.DEBUG: print  2  
[2023-03-31 00:55:33] local.INFO: header  
[2023-03-31 00:55:33] local.CRITICAL: ****************************1  
[2023-03-31 00:55:33] local.ALERT: reach here  
[2023-03-31 00:55:33] local.ERROR: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '2400',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => '2.28',
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '5472.00',
  ),
  3 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'خمسة ألف وأربعمائة وإثنان وسبعون  ر.ي.',
  ),
)  
[2023-03-31 00:55:33] local.ERROR: الكمية  
[2023-03-31 00:55:33] local.ERROR: سعر الوحدة  
[2023-03-31 00:55:33] local.ERROR: المبلغ  
[2023-03-31 00:55:33] local.ERROR: 5,472.00  
[2023-03-31 00:55:33] local.ERROR: 2,400.00  
[2023-03-31 02:56:46] local.INFO: header  
[2023-03-31 02:56:46] local.INFO: header after fliter  
[2023-03-31 02:56:46] local.INFO: Body  after fliter  
[2023-03-31 02:56:46] local.INFO: array (
)  
[2023-03-31 02:56:46] local.INFO: transaction14  
[2023-03-31 02:56:46] local.INFO: first inquery phone = 101034508  
[2023-03-31 02:56:48] local.DEBUG: response querySubBalance  
[2023-03-31 02:56:48] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#14.57 GB#11-04-2023#0#0##1500.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-31 02:56:48] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '14.57 GB',
  4 => '11-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '1500.00',
  9 => '4G 25',
)  
[2023-03-31 02:56:48] local.DEBUG: print  before faction by provider price  
[2023-03-31 02:56:49] local.DEBUG: print  after faction by provider price  
[2023-03-31 02:56:49] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-31 02:56:49] local.DEBUG: print1  
[2023-03-31 02:56:49] local.DEBUG: print  2  
[2023-03-31 19:37:47] local.INFO: header  
[2023-03-31 19:37:47] local.INFO: header after fliter  
[2023-03-31 19:37:47] local.INFO: Body  after fliter  
[2023-03-31 19:37:47] local.INFO: array (
)  
[2023-03-31 19:37:47] local.INFO: transaction14  
[2023-03-31 19:37:47] local.INFO: first inquery phone = 107676678  
[2023-03-31 19:37:51] local.DEBUG: response querySubBalance  
[2023-03-31 19:37:51] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#15.21 GB#26-04-2023#0#0##1930.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-31 19:37:51] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '15.21 GB',
  4 => '26-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '1930.00',
  9 => '4G 25',
)  
[2023-03-31 19:37:51] local.DEBUG: print  before faction by provider price  
[2023-03-31 19:37:51] local.DEBUG: print  after faction by provider price  
[2023-03-31 19:37:51] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-31 19:37:51] local.DEBUG: print1  
[2023-03-31 19:37:51] local.DEBUG: print  2  
[2023-03-31 19:40:48] local.INFO: header  
[2023-03-31 19:40:48] local.INFO: header after fliter  
[2023-03-31 19:40:48] local.INFO: Body  after fliter  
[2023-03-31 19:40:48] local.INFO: array (
)  
[2023-03-31 19:40:48] local.INFO: transaction14  
[2023-03-31 19:40:48] local.INFO: first inquery phone = 107676678  
[2023-03-31 19:40:51] local.DEBUG: response querySubBalance  
[2023-03-31 19:40:51] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#14.92 GB#26-04-2023#0#0##1930.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-31 19:40:51] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '14.92 GB',
  4 => '26-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '1930.00',
  9 => '4G 25',
)  
[2023-03-31 19:40:51] local.DEBUG: print  before faction by provider price  
[2023-03-31 19:40:51] local.DEBUG: print  after faction by provider price  
[2023-03-31 19:40:51] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-31 19:40:51] local.DEBUG: print1  
[2023-03-31 19:40:51] local.DEBUG: print  2  
[2023-03-31 19:41:39] local.INFO: header  
[2023-03-31 19:41:39] local.CRITICAL: ****************************1  
[2023-03-31 19:41:39] local.ALERT: reach here  
[2023-03-31 19:41:39] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '4000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'أربعة ألف  ر.ي.',
  ),
)  
[2023-03-31 19:41:39] local.ERROR: المبلغ  
[2023-03-31 19:41:39] local.ERROR: 4,000.00  
[2023-03-31 19:41:39] local.ERROR: مبلغ وقدرة  
[2023-03-31 19:41:39] local.ALERT: 200  
[2023-03-31 19:41:39] local.ALERT: 3  
[2023-03-31 19:41:39] local.ALERT: 200  
[2023-03-31 19:41:39] local.ALERT: 3  
[2023-03-31 19:41:39] local.ALERT: 200  
[2023-03-31 19:41:39] local.ALERT: 3  
[2023-03-31 19:41:39] local.ALERT: 200  
[2023-03-31 19:41:39] local.ALERT: 3  
[2023-03-31 19:41:39] local.ALERT: 200  
[2023-03-31 19:41:39] local.ALERT: 3  
[2023-03-31 19:41:39] local.ALERT: 200  
[2023-03-31 19:41:39] local.ALERT: 3  
[2023-03-31 19:41:39] local.ALERT: 200  
[2023-03-31 19:41:39] local.ALERT: 3  
[2023-03-31 19:41:39] local.ALERT: 200  
[2023-03-31 19:41:39] local.ALERT: 3  
[2023-03-31 19:41:39] local.ALERT: 200  
[2023-03-31 19:41:39] local.ALERT: 3  
[2023-03-31 19:41:39] local.ALERT: 200  
[2023-03-31 19:41:39] local.ALERT: 2  
[2023-03-31 19:41:39] local.ALERT: 200  
[2023-03-31 19:41:39] local.ALERT: 2  
[2023-03-31 19:41:39] local.ALERT: 200  
[2023-03-31 19:41:39] local.ALERT: 2  
[2023-03-31 19:41:39] local.ALERT: 200  
[2023-03-31 19:41:39] local.ALERT: 2  
[2023-03-31 19:41:39] local.ALERT: 200  
[2023-03-31 19:41:39] local.ALERT: 2  
[2023-03-31 19:41:39] local.ALERT: 200  
[2023-03-31 19:41:39] local.ALERT: 2  
[2023-03-31 19:41:39] local.ALERT: 200  
[2023-03-31 19:41:39] local.ALERT: 2  
[2023-03-31 19:41:39] local.ALERT: 200  
[2023-03-31 19:41:39] local.ALERT: 1  
[2023-03-31 19:41:39] local.ALERT: 200  
[2023-03-31 19:41:39] local.ALERT: 1  
[2023-03-31 19:41:39] local.ALERT: 200  
[2023-03-31 19:41:39] local.ALERT: 1  
[2023-03-31 19:41:39] local.ALERT: 200  
[2023-03-31 19:41:39] local.ALERT: 1  
[2023-03-31 19:41:39] local.ALERT: 200  
[2023-03-31 19:41:39] local.ALERT: 1  
[2023-03-31 19:41:39] local.ALERT: 200  
[2023-03-31 19:41:39] local.ALERT: 2  
[2023-03-31 19:41:39] local.ALERT: 200  
[2023-03-31 19:41:39] local.ALERT: 10013  
[2023-03-31 19:41:39] local.ALERT: 200  
[2023-03-31 19:41:39] local.ALERT: 10013  
[2023-03-31 19:41:39] local.ALERT: 200  
[2023-03-31 19:41:39] local.ALERT: 10013  
[2023-03-31 19:41:39] local.ALERT: 200  
[2023-03-31 19:41:39] local.ALERT: 10013  
[2023-03-31 19:41:39] local.ALERT: 200  
[2023-03-31 19:41:39] local.ALERT: 1  
[2023-03-31 19:41:39] local.ALERT: 200  
[2023-03-31 19:41:39] local.ALERT: 3  
[2023-03-31 19:41:39] local.ALERT: 200  
[2023-03-31 19:41:39] local.ALERT: 3  
[2023-03-31 19:41:39] local.ALERT: 200  
[2023-03-31 19:41:39] local.ALERT: 40  
[2023-03-31 19:41:39] local.ALERT: 200  
[2023-03-31 19:41:39] local.ALERT: 40  
[2023-03-31 19:41:39] local.ALERT: 200  
[2023-03-31 19:41:39] local.ALERT: 40  
[2023-03-31 19:41:39] local.ALERT: 200  
[2023-03-31 19:41:39] local.ALERT: 40  
[2023-03-31 19:41:39] local.ALERT: 200  
[2023-03-31 19:41:39] local.ALERT: 200  
[2023-03-31 19:41:39] local.WARNING: 1  
[2023-03-31 19:41:39] local.WARNING: array (
  'ID' => 86,
  'Name' => 'فئة 25 جيجا 4000 ريال',
  'ServiceID' => 200,
  'Price' => 4000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 4000.0,
)  
[2023-03-31 19:41:39] local.ALERT: 200  
[2023-03-31 19:41:39] local.ALERT: 200  
[2023-03-31 19:41:39] local.ALERT: 200  
[2023-03-31 19:41:39] local.ALERT: 200  
[2023-03-31 19:41:39] local.ALERT: 200  
[2023-03-31 19:41:39] local.ALERT: 200  
[2023-03-31 19:41:39] local.ALERT: 200  
[2023-03-31 19:41:39] local.ALERT: 200  
[2023-03-31 19:41:39] local.ALERT: 200  
[2023-03-31 19:41:39] local.ALERT: 200  
[2023-03-31 19:41:39] local.ALERT: 200  
[2023-03-31 19:41:39] local.ALERT: 40  
[2023-03-31 19:41:39] local.CRITICAL: ****************************2  
[2023-03-31 19:41:39] local.CRITICAL: ****************************  
[2023-03-31 19:41:39] local.CRITICAL:   
[2023-03-31 19:41:39] local.CRITICAL: ****************************  
[2023-03-31 19:41:40] local.INFO: {
  "ClientBalanceResult": "21413.7050"
}  
[2023-03-31 19:41:40] local.INFO: array (
  'ClientBalanceResult' => '21413.7050',
)  
[2023-03-31 19:41:40] local.DEBUG: lattttef  
[2023-03-31 19:41:40] local.DEBUG: array (
  'ClientBalanceResult' => '21413.7050',
)  
[2023-03-31 19:41:40] local.INFO: transaction14  
[2023-03-31 19:41:40] local.INFO: first inquery phone = 103377745  
[2023-03-31 19:41:43] local.DEBUG: response querySubBalance  
[2023-03-31 19:41:43] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#2.00 GB#21-04-2023#0#0##2396.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-31 19:41:43] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '2.00 GB',
  4 => '21-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '2396.00',
  9 => '4G 25',
)  
[2023-03-31 19:41:43] local.DEBUG: print  before faction by provider price  
[2023-03-31 19:41:43] local.DEBUG: print  after faction by provider price  
[2023-03-31 19:41:43] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-31 19:41:43] local.DEBUG: print1  
[2023-03-31 19:41:43] local.DEBUG: print  2  
[2023-03-31 19:41:43] local.INFO: transaction1  
[2023-03-31 19:41:43] local.INFO: transaction2  
[2023-03-31 19:41:44] local.INFO: transaction3  
[2023-03-31 19:41:44] local.INFO: transaction4  
[2023-03-31 19:41:44] local.INFO: transaction4  
[2023-03-31 19:41:44] local.INFO: transaction5  
[2023-03-31 19:41:44] local.INFO: transaction6  
[2023-03-31 19:41:44] local.INFO: transaction7  
[2023-03-31 19:41:44] local.DEBUG: array (
  'AMT' => 4000.0,
  'CType' => 0,
  'FID' => 86,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '103377745',
  'State' => 0,
  'lateflog' => '584291',
)  
[2023-03-31 19:41:44] local.INFO: transaction8  
[2023-03-31 19:41:44] local.INFO: transaction9  
[2023-03-31 19:41:44] local.INFO: transaction10  
[2023-03-31 19:41:44] local.INFO: transaction11  
[2023-03-31 19:41:44] local.INFO: 12  
[2023-03-31 19:41:44] local.INFO: transaction13  
[2023-03-31 19:41:44] local.INFO: transaction14  
[2023-03-31 19:41:44] local.INFO: transaction19  
[2023-03-31 19:41:44] local.INFO: transaction15  
[2023-03-31 19:41:44] local.INFO: transaction16  
[2023-03-31 19:41:44] local.INFO: 98#103377745#4000.00#0  
[2023-03-31 19:41:57] local.INFO: transaction18  
[2023-03-31 19:41:57] local.INFO: array (
  0 => 'OK',
  1 => '3,169,206.83',
  2 => 'NONE',
  3 => '61811254',
  4 => '4,000.00',
)  
[2023-03-31 19:51:04] local.INFO: header  
[2023-03-31 19:51:04] local.INFO: header after fliter  
[2023-03-31 19:51:04] local.INFO: Body  after fliter  
[2023-03-31 19:51:04] local.INFO: array (
)  
[2023-03-31 19:51:04] local.INFO: transaction14  
[2023-03-31 19:51:04] local.INFO: first inquery phone = 103335881  
[2023-03-31 19:51:07] local.DEBUG: response querySubBalance  
[2023-03-31 19:51:07] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#2.13 MB#06-04-2023#0#0##500.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-31 19:51:07] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '2.13 MB',
  4 => '06-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '500.00',
  9 => '4G 15',
)  
[2023-03-31 19:51:07] local.DEBUG: print  before faction by provider price  
[2023-03-31 19:51:07] local.DEBUG: print  after faction by provider price  
[2023-03-31 19:51:07] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-31 19:51:07] local.DEBUG: print1  
[2023-03-31 19:51:07] local.DEBUG: print  2  
[2023-03-31 19:52:27] local.INFO: header  
[2023-03-31 19:52:27] local.CRITICAL: ****************************1  
[2023-03-31 19:52:27] local.ALERT: reach here  
[2023-03-31 19:52:27] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '2400.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ألفين وأربعمائة  ر.ي.',
  ),
)  
[2023-03-31 19:52:27] local.ERROR: المبلغ  
[2023-03-31 19:52:27] local.ERROR: 2,400.00  
[2023-03-31 19:52:27] local.ERROR: مبلغ وقدرة  
[2023-03-31 19:52:27] local.ALERT: 200  
[2023-03-31 19:52:27] local.ALERT: 3  
[2023-03-31 19:52:27] local.ALERT: 200  
[2023-03-31 19:52:27] local.ALERT: 3  
[2023-03-31 19:52:27] local.ALERT: 200  
[2023-03-31 19:52:27] local.ALERT: 3  
[2023-03-31 19:52:27] local.ALERT: 200  
[2023-03-31 19:52:27] local.ALERT: 3  
[2023-03-31 19:52:27] local.ALERT: 200  
[2023-03-31 19:52:27] local.ALERT: 3  
[2023-03-31 19:52:27] local.ALERT: 200  
[2023-03-31 19:52:27] local.ALERT: 3  
[2023-03-31 19:52:27] local.ALERT: 200  
[2023-03-31 19:52:27] local.ALERT: 3  
[2023-03-31 19:52:27] local.ALERT: 200  
[2023-03-31 19:52:27] local.ALERT: 3  
[2023-03-31 19:52:27] local.ALERT: 200  
[2023-03-31 19:52:27] local.ALERT: 3  
[2023-03-31 19:52:27] local.ALERT: 200  
[2023-03-31 19:52:27] local.ALERT: 2  
[2023-03-31 19:52:27] local.ALERT: 200  
[2023-03-31 19:52:27] local.ALERT: 2  
[2023-03-31 19:52:27] local.ALERT: 200  
[2023-03-31 19:52:27] local.ALERT: 2  
[2023-03-31 19:52:27] local.ALERT: 200  
[2023-03-31 19:52:27] local.ALERT: 2  
[2023-03-31 19:52:27] local.ALERT: 200  
[2023-03-31 19:52:27] local.ALERT: 2  
[2023-03-31 19:52:27] local.ALERT: 200  
[2023-03-31 19:52:27] local.ALERT: 2  
[2023-03-31 19:52:27] local.ALERT: 200  
[2023-03-31 19:52:27] local.ALERT: 2  
[2023-03-31 19:52:27] local.ALERT: 200  
[2023-03-31 19:52:27] local.ALERT: 1  
[2023-03-31 19:52:27] local.ALERT: 200  
[2023-03-31 19:52:27] local.ALERT: 1  
[2023-03-31 19:52:27] local.ALERT: 200  
[2023-03-31 19:52:27] local.ALERT: 1  
[2023-03-31 19:52:27] local.ALERT: 200  
[2023-03-31 19:52:27] local.ALERT: 1  
[2023-03-31 19:52:27] local.ALERT: 200  
[2023-03-31 19:52:27] local.ALERT: 1  
[2023-03-31 19:52:27] local.ALERT: 200  
[2023-03-31 19:52:27] local.ALERT: 2  
[2023-03-31 19:52:27] local.ALERT: 200  
[2023-03-31 19:52:27] local.ALERT: 10013  
[2023-03-31 19:52:27] local.ALERT: 200  
[2023-03-31 19:52:27] local.ALERT: 10013  
[2023-03-31 19:52:27] local.ALERT: 200  
[2023-03-31 19:52:27] local.ALERT: 10013  
[2023-03-31 19:52:27] local.ALERT: 200  
[2023-03-31 19:52:27] local.ALERT: 10013  
[2023-03-31 19:52:27] local.ALERT: 200  
[2023-03-31 19:52:27] local.ALERT: 1  
[2023-03-31 19:52:27] local.ALERT: 200  
[2023-03-31 19:52:27] local.ALERT: 3  
[2023-03-31 19:52:27] local.ALERT: 200  
[2023-03-31 19:52:27] local.ALERT: 3  
[2023-03-31 19:52:27] local.ALERT: 200  
[2023-03-31 19:52:27] local.ALERT: 40  
[2023-03-31 19:52:27] local.ALERT: 200  
[2023-03-31 19:52:27] local.ALERT: 40  
[2023-03-31 19:52:27] local.ALERT: 200  
[2023-03-31 19:52:27] local.ALERT: 40  
[2023-03-31 19:52:27] local.ALERT: 200  
[2023-03-31 19:52:27] local.ALERT: 40  
[2023-03-31 19:52:27] local.WARNING: 1  
[2023-03-31 19:52:27] local.WARNING: array (
  'ID' => 85,
  'Name' => 'فئة 15 جيجا 2400 ريال',
  'ServiceID' => 200,
  'Price' => 2400.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 2400.0,
)  
[2023-03-31 19:52:27] local.ALERT: 200  
[2023-03-31 19:52:27] local.ALERT: 200  
[2023-03-31 19:52:27] local.ALERT: 200  
[2023-03-31 19:52:27] local.ALERT: 200  
[2023-03-31 19:52:27] local.ALERT: 200  
[2023-03-31 19:52:27] local.ALERT: 200  
[2023-03-31 19:52:27] local.ALERT: 200  
[2023-03-31 19:52:27] local.ALERT: 200  
[2023-03-31 19:52:27] local.ALERT: 200  
[2023-03-31 19:52:27] local.ALERT: 200  
[2023-03-31 19:52:27] local.ALERT: 200  
[2023-03-31 19:52:27] local.ALERT: 200  
[2023-03-31 19:52:27] local.ALERT: 200  
[2023-03-31 19:52:27] local.ALERT: 40  
[2023-03-31 19:52:27] local.CRITICAL: ****************************2  
[2023-03-31 19:52:27] local.CRITICAL: ****************************  
[2023-03-31 19:52:27] local.CRITICAL:   
[2023-03-31 19:52:27] local.CRITICAL: ****************************  
[2023-03-31 19:52:28] local.INFO: {
  "ClientBalanceResult": "17413.7050"
}  
[2023-03-31 19:52:28] local.INFO: array (
  'ClientBalanceResult' => '17413.7050',
)  
[2023-03-31 19:52:28] local.DEBUG: lattttef  
[2023-03-31 19:52:28] local.DEBUG: array (
  'ClientBalanceResult' => '17413.7050',
)  
[2023-03-31 19:52:28] local.INFO: transaction14  
[2023-03-31 19:52:28] local.INFO: first inquery phone = 103335881  
[2023-03-31 19:52:31] local.DEBUG: response querySubBalance  
[2023-03-31 19:52:31] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#2.13 MB#06-04-2023#0#0##500.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-31 19:52:31] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '2.13 MB',
  4 => '06-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '500.00',
  9 => '4G 15',
)  
[2023-03-31 19:52:31] local.DEBUG: print  before faction by provider price  
[2023-03-31 19:52:31] local.DEBUG: print  after faction by provider price  
[2023-03-31 19:52:31] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-31 19:52:31] local.DEBUG: print1  
[2023-03-31 19:52:31] local.DEBUG: print  2  
[2023-03-31 19:52:31] local.INFO: transaction1  
[2023-03-31 19:52:31] local.INFO: transaction2  
[2023-03-31 19:52:31] local.INFO: transaction3  
[2023-03-31 19:52:31] local.INFO: transaction4  
[2023-03-31 19:52:31] local.INFO: transaction4  
[2023-03-31 19:52:31] local.INFO: transaction5  
[2023-03-31 19:52:31] local.INFO: transaction6  
[2023-03-31 19:52:32] local.INFO: transaction7  
[2023-03-31 19:52:32] local.DEBUG: array (
  'AMT' => 2400.0,
  'CType' => 0,
  'FID' => 85,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '103335881',
  'State' => 0,
  'lateflog' => '584291',
)  
[2023-03-31 19:52:32] local.INFO: transaction8  
[2023-03-31 19:52:32] local.INFO: transaction9  
[2023-03-31 19:52:32] local.INFO: transaction10  
[2023-03-31 19:52:32] local.INFO: transaction11  
[2023-03-31 19:52:32] local.INFO: 12  
[2023-03-31 19:52:32] local.INFO: transaction13  
[2023-03-31 19:52:32] local.INFO: transaction14  
[2023-03-31 19:52:32] local.INFO: transaction19  
[2023-03-31 19:52:32] local.INFO: transaction15  
[2023-03-31 19:52:32] local.INFO: transaction16  
[2023-03-31 19:52:32] local.INFO: 98#103335881#2400.00#0  
[2023-03-31 19:52:44] local.INFO: transaction18  
[2023-03-31 19:52:44] local.INFO: array (
  0 => 'OK',
  1 => '3,145,881.83',
  2 => 'NONE',
  3 => '61812732',
  4 => '2,400.00',
)  
[2023-03-31 19:52:47] local.INFO: header  
[2023-03-31 19:52:47] local.INFO: header after fliter  
[2023-03-31 19:52:47] local.INFO: Body  after fliter  
[2023-03-31 19:52:47] local.INFO: array (
)  
[2023-03-31 19:52:47] local.INFO: transaction14  
[2023-03-31 19:52:47] local.INFO: first inquery phone = 107676678  
[2023-03-31 19:52:50] local.DEBUG: response querySubBalance  
[2023-03-31 19:52:50] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#14.63 GB#26-04-2023#0#0##1930.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-31 19:52:50] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '14.63 GB',
  4 => '26-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '1930.00',
  9 => '4G 25',
)  
[2023-03-31 19:52:50] local.DEBUG: print  before faction by provider price  
[2023-03-31 19:52:50] local.DEBUG: print  after faction by provider price  
[2023-03-31 19:52:50] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-31 19:52:50] local.DEBUG: print1  
[2023-03-31 19:52:50] local.DEBUG: print  2  
[2023-03-31 19:52:50] local.INFO: header  
[2023-03-31 19:52:50] local.INFO: header after fliter  
[2023-03-31 19:52:50] local.INFO: Body  after fliter  
[2023-03-31 19:52:50] local.INFO: array (
)  
[2023-03-31 19:52:50] local.INFO: transaction14  
[2023-03-31 19:52:50] local.INFO: first inquery phone = 107676678  
[2023-03-31 19:52:53] local.DEBUG: response querySubBalance  
[2023-03-31 19:52:53] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#14.63 GB#26-04-2023#0#0##1930.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-31 19:52:53] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '14.63 GB',
  4 => '26-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '1930.00',
  9 => '4G 25',
)  
[2023-03-31 19:52:53] local.DEBUG: print  before faction by provider price  
[2023-03-31 19:52:53] local.DEBUG: print  after faction by provider price  
[2023-03-31 19:52:53] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-31 19:52:53] local.DEBUG: print1  
[2023-03-31 19:52:53] local.DEBUG: print  2  
[2023-03-31 19:53:22] local.INFO: header  
[2023-03-31 19:53:22] local.INFO: header after fliter  
[2023-03-31 19:53:22] local.INFO: Body  after fliter  
[2023-03-31 19:53:22] local.INFO: array (
)  
[2023-03-31 19:53:22] local.INFO: transaction14  
[2023-03-31 19:53:22] local.INFO: first inquery phone = 103335881  
[2023-03-31 19:53:25] local.DEBUG: response querySubBalance  
[2023-03-31 19:53:25] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#15.00 GB#01-05-2023#0#0##500.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-31 19:53:25] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '15.00 GB',
  4 => '01-05-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '500.00',
  9 => '4G 15',
)  
[2023-03-31 19:53:25] local.DEBUG: print  before faction by provider price  
[2023-03-31 19:53:25] local.DEBUG: print  after faction by provider price  
[2023-03-31 19:53:25] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-31 19:53:25] local.DEBUG: print1  
[2023-03-31 19:53:25] local.DEBUG: print  2  
[2023-03-31 19:54:05] local.INFO: header  
[2023-03-31 19:54:05] local.INFO: header after fliter  
[2023-03-31 19:54:05] local.INFO: Body  after fliter  
[2023-03-31 19:54:05] local.INFO: array (
)  
[2023-03-31 19:54:05] local.INFO: transaction14  
[2023-03-31 19:54:05] local.INFO: first inquery phone = 103335881  
[2023-03-31 19:54:09] local.DEBUG: response querySubBalance  
[2023-03-31 19:54:09] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#15.00 GB#01-05-2023#0#0##500.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-31 19:54:09] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '15.00 GB',
  4 => '01-05-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '500.00',
  9 => '4G 15',
)  
[2023-03-31 19:54:09] local.DEBUG: print  before faction by provider price  
[2023-03-31 19:54:09] local.DEBUG: print  after faction by provider price  
[2023-03-31 19:54:09] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-31 19:54:09] local.DEBUG: print1  
[2023-03-31 19:54:09] local.DEBUG: print  2  
[2023-03-31 19:57:52] local.INFO: header  
[2023-03-31 19:57:52] local.INFO: header after fliter  
[2023-03-31 19:57:52] local.INFO: Body  after fliter  
[2023-03-31 19:57:52] local.INFO: array (
)  
[2023-03-31 19:57:52] local.INFO: transaction14  
[2023-03-31 19:57:52] local.INFO: first inquery phone = 107676678  
[2023-03-31 19:57:56] local.DEBUG: response querySubBalance  
[2023-03-31 19:57:56] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#14.53 GB#26-04-2023#0#0##1930.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-31 19:57:56] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '14.53 GB',
  4 => '26-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '1930.00',
  9 => '4G 25',
)  
[2023-03-31 19:57:56] local.DEBUG: print  before faction by provider price  
[2023-03-31 19:57:56] local.DEBUG: print  after faction by provider price  
[2023-03-31 19:57:56] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-31 19:57:56] local.DEBUG: print1  
[2023-03-31 19:57:56] local.DEBUG: print  2  
[2023-03-31 19:58:46] local.INFO: header  
[2023-03-31 19:58:46] local.CRITICAL: ****************************1  
[2023-03-31 19:58:46] local.ALERT: reach here  
[2023-03-31 19:58:46] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '2400.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ألفين وأربعمائة  ر.ي.',
  ),
)  
[2023-03-31 19:58:46] local.ERROR: المبلغ  
[2023-03-31 19:58:46] local.ERROR: 2,400.00  
[2023-03-31 19:58:46] local.ERROR: مبلغ وقدرة  
[2023-03-31 19:58:46] local.ALERT: 200  
[2023-03-31 19:58:46] local.ALERT: 3  
[2023-03-31 19:58:46] local.ALERT: 200  
[2023-03-31 19:58:46] local.ALERT: 3  
[2023-03-31 19:58:46] local.ALERT: 200  
[2023-03-31 19:58:46] local.ALERT: 3  
[2023-03-31 19:58:46] local.ALERT: 200  
[2023-03-31 19:58:46] local.ALERT: 3  
[2023-03-31 19:58:46] local.ALERT: 200  
[2023-03-31 19:58:46] local.ALERT: 3  
[2023-03-31 19:58:46] local.ALERT: 200  
[2023-03-31 19:58:46] local.ALERT: 3  
[2023-03-31 19:58:46] local.ALERT: 200  
[2023-03-31 19:58:46] local.ALERT: 3  
[2023-03-31 19:58:46] local.ALERT: 200  
[2023-03-31 19:58:46] local.ALERT: 3  
[2023-03-31 19:58:46] local.ALERT: 200  
[2023-03-31 19:58:46] local.ALERT: 3  
[2023-03-31 19:58:46] local.ALERT: 200  
[2023-03-31 19:58:46] local.ALERT: 2  
[2023-03-31 19:58:46] local.ALERT: 200  
[2023-03-31 19:58:46] local.ALERT: 2  
[2023-03-31 19:58:46] local.ALERT: 200  
[2023-03-31 19:58:46] local.ALERT: 2  
[2023-03-31 19:58:46] local.ALERT: 200  
[2023-03-31 19:58:46] local.ALERT: 2  
[2023-03-31 19:58:46] local.ALERT: 200  
[2023-03-31 19:58:46] local.ALERT: 2  
[2023-03-31 19:58:46] local.ALERT: 200  
[2023-03-31 19:58:46] local.ALERT: 2  
[2023-03-31 19:58:46] local.ALERT: 200  
[2023-03-31 19:58:46] local.ALERT: 2  
[2023-03-31 19:58:46] local.ALERT: 200  
[2023-03-31 19:58:46] local.ALERT: 1  
[2023-03-31 19:58:46] local.ALERT: 200  
[2023-03-31 19:58:46] local.ALERT: 1  
[2023-03-31 19:58:46] local.ALERT: 200  
[2023-03-31 19:58:46] local.ALERT: 1  
[2023-03-31 19:58:46] local.ALERT: 200  
[2023-03-31 19:58:46] local.ALERT: 1  
[2023-03-31 19:58:46] local.ALERT: 200  
[2023-03-31 19:58:46] local.ALERT: 1  
[2023-03-31 19:58:46] local.ALERT: 200  
[2023-03-31 19:58:46] local.ALERT: 2  
[2023-03-31 19:58:46] local.ALERT: 200  
[2023-03-31 19:58:46] local.ALERT: 10013  
[2023-03-31 19:58:46] local.ALERT: 200  
[2023-03-31 19:58:46] local.ALERT: 10013  
[2023-03-31 19:58:46] local.ALERT: 200  
[2023-03-31 19:58:46] local.ALERT: 10013  
[2023-03-31 19:58:46] local.ALERT: 200  
[2023-03-31 19:58:46] local.ALERT: 10013  
[2023-03-31 19:58:46] local.ALERT: 200  
[2023-03-31 19:58:46] local.ALERT: 1  
[2023-03-31 19:58:46] local.ALERT: 200  
[2023-03-31 19:58:46] local.ALERT: 3  
[2023-03-31 19:58:46] local.ALERT: 200  
[2023-03-31 19:58:46] local.ALERT: 3  
[2023-03-31 19:58:46] local.ALERT: 200  
[2023-03-31 19:58:46] local.ALERT: 40  
[2023-03-31 19:58:46] local.ALERT: 200  
[2023-03-31 19:58:46] local.ALERT: 40  
[2023-03-31 19:58:46] local.ALERT: 200  
[2023-03-31 19:58:46] local.ALERT: 40  
[2023-03-31 19:58:46] local.ALERT: 200  
[2023-03-31 19:58:46] local.ALERT: 40  
[2023-03-31 19:58:46] local.WARNING: 1  
[2023-03-31 19:58:46] local.WARNING: array (
  'ID' => 85,
  'Name' => 'فئة 15 جيجا 2400 ريال',
  'ServiceID' => 200,
  'Price' => 2400.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 2400.0,
)  
[2023-03-31 19:58:46] local.ALERT: 200  
[2023-03-31 19:58:46] local.ALERT: 200  
[2023-03-31 19:58:46] local.ALERT: 200  
[2023-03-31 19:58:46] local.ALERT: 200  
[2023-03-31 19:58:46] local.ALERT: 200  
[2023-03-31 19:58:46] local.ALERT: 200  
[2023-03-31 19:58:46] local.ALERT: 200  
[2023-03-31 19:58:46] local.ALERT: 200  
[2023-03-31 19:58:46] local.ALERT: 200  
[2023-03-31 19:58:46] local.ALERT: 200  
[2023-03-31 19:58:46] local.ALERT: 200  
[2023-03-31 19:58:46] local.ALERT: 200  
[2023-03-31 19:58:46] local.ALERT: 200  
[2023-03-31 19:58:46] local.ALERT: 40  
[2023-03-31 19:58:46] local.CRITICAL: ****************************2  
[2023-03-31 19:58:46] local.CRITICAL: ****************************  
[2023-03-31 19:58:46] local.CRITICAL:   
[2023-03-31 19:58:46] local.CRITICAL: ****************************  
[2023-03-31 19:58:47] local.INFO: {
  "ClientBalanceResult": "3497.3800"
}  
[2023-03-31 19:58:47] local.INFO: array (
  'ClientBalanceResult' => '3497.3800',
)  
[2023-03-31 19:58:47] local.DEBUG: lattttef  
[2023-03-31 19:58:47] local.DEBUG: array (
  'ClientBalanceResult' => '3497.3800',
)  
[2023-03-31 19:58:47] local.INFO: transaction14  
[2023-03-31 19:58:47] local.INFO: first inquery phone = 101027329  
[2023-03-31 19:58:50] local.DEBUG: response querySubBalance  
[2023-03-31 19:58:50] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#1.46 GB#28-04-2023#0#0##.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-31 19:58:50] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '1.46 GB',
  4 => '28-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 15',
)  
[2023-03-31 19:58:50] local.DEBUG: print  before faction by provider price  
[2023-03-31 19:58:50] local.DEBUG: print  after faction by provider price  
[2023-03-31 19:58:50] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-31 19:58:50] local.DEBUG: print1  
[2023-03-31 19:58:50] local.DEBUG: print  2  
[2023-03-31 19:58:50] local.INFO: transaction1  
[2023-03-31 19:58:50] local.INFO: transaction2  
[2023-03-31 19:58:50] local.INFO: transaction3  
[2023-03-31 19:58:50] local.INFO: transaction4  
[2023-03-31 19:58:50] local.INFO: transaction4  
[2023-03-31 19:58:50] local.INFO: transaction5  
[2023-03-31 19:58:50] local.INFO: transaction6  
[2023-03-31 19:58:50] local.INFO: transaction7  
[2023-03-31 19:58:50] local.DEBUG: array (
  'AMT' => 2400.0,
  'CType' => 0,
  'FID' => 85,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '101027329',
  'State' => 0,
  'lateflog' => '530552',
)  
[2023-03-31 19:58:50] local.INFO: transaction8  
[2023-03-31 19:58:50] local.INFO: transaction9  
[2023-03-31 19:58:50] local.INFO: transaction10  
[2023-03-31 19:58:50] local.INFO: transaction11  
[2023-03-31 19:58:50] local.INFO: 12  
[2023-03-31 19:58:50] local.INFO: transaction13  
[2023-03-31 19:58:50] local.INFO: transaction14  
[2023-03-31 19:58:50] local.INFO: transaction19  
[2023-03-31 19:58:50] local.INFO: transaction15  
[2023-03-31 19:58:50] local.INFO: transaction16  
[2023-03-31 19:58:50] local.INFO: 98#101027329#2400.00#0  
[2023-03-31 19:59:02] local.INFO: transaction18  
[2023-03-31 19:59:02] local.INFO: array (
  0 => 'OK',
  1 => '3,122,669.83',
  2 => 'NONE',
  3 => '61813633',
  4 => '2,400.00',
)  
[2023-03-31 20:07:03] local.INFO: header  
[2023-03-31 20:07:03] local.INFO: header after fliter  
[2023-03-31 20:07:03] local.INFO: Body  after fliter  
[2023-03-31 20:07:03] local.INFO: array (
)  
[2023-03-31 20:07:03] local.INFO: transaction14  
[2023-03-31 20:07:03] local.INFO: first inquery phone = 107676678  
[2023-03-31 20:07:07] local.DEBUG: response querySubBalance  
[2023-03-31 20:07:07] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#14.48 GB#26-04-2023#0#0##1930.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-31 20:07:07] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '14.48 GB',
  4 => '26-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '1930.00',
  9 => '4G 25',
)  
[2023-03-31 20:07:07] local.DEBUG: print  before faction by provider price  
[2023-03-31 20:07:07] local.DEBUG: print  after faction by provider price  
[2023-03-31 20:07:07] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-31 20:07:07] local.DEBUG: print1  
[2023-03-31 20:07:07] local.DEBUG: print  2  
[2023-03-31 20:13:14] local.INFO: header  
[2023-03-31 20:13:14] local.INFO: header after fliter  
[2023-03-31 20:13:14] local.INFO: Body  after fliter  
[2023-03-31 20:13:14] local.INFO: array (
)  
[2023-03-31 20:13:14] local.INFO: transaction14  
[2023-03-31 20:13:14] local.INFO: first inquery phone = 107676678  
[2023-03-31 20:13:17] local.DEBUG: response querySubBalance  
[2023-03-31 20:13:17] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#14.24 GB#26-04-2023#0#0##1930.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-31 20:13:17] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '14.24 GB',
  4 => '26-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '1930.00',
  9 => '4G 25',
)  
[2023-03-31 20:13:17] local.DEBUG: print  before faction by provider price  
[2023-03-31 20:13:17] local.DEBUG: print  after faction by provider price  
[2023-03-31 20:13:17] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-31 20:13:17] local.DEBUG: print1  
[2023-03-31 20:13:17] local.DEBUG: print  2  
[2023-03-31 20:41:15] local.INFO: header  
[2023-03-31 20:41:15] local.CRITICAL: ****************************1  
[2023-03-31 20:41:15] local.ALERT: reach here  
[2023-03-31 20:41:16] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '12000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'إثنا عشر ألف  ر.ي.',
  ),
)  
[2023-03-31 20:41:16] local.ERROR: المبلغ  
[2023-03-31 20:41:16] local.ERROR: 12,000.00  
[2023-03-31 20:41:16] local.ERROR: مبلغ وقدرة  
[2023-03-31 20:41:16] local.ALERT: 40  
[2023-03-31 20:41:16] local.ALERT: 3  
[2023-03-31 20:41:16] local.ALERT: 40  
[2023-03-31 20:41:16] local.ALERT: 3  
[2023-03-31 20:41:16] local.ALERT: 40  
[2023-03-31 20:41:16] local.ALERT: 3  
[2023-03-31 20:41:16] local.ALERT: 40  
[2023-03-31 20:41:16] local.ALERT: 3  
[2023-03-31 20:41:16] local.ALERT: 40  
[2023-03-31 20:41:16] local.ALERT: 3  
[2023-03-31 20:41:16] local.ALERT: 40  
[2023-03-31 20:41:16] local.ALERT: 3  
[2023-03-31 20:41:16] local.ALERT: 40  
[2023-03-31 20:41:16] local.ALERT: 3  
[2023-03-31 20:41:16] local.ALERT: 40  
[2023-03-31 20:41:16] local.ALERT: 3  
[2023-03-31 20:41:16] local.ALERT: 40  
[2023-03-31 20:41:16] local.ALERT: 3  
[2023-03-31 20:41:16] local.ALERT: 40  
[2023-03-31 20:41:16] local.ALERT: 2  
[2023-03-31 20:41:16] local.ALERT: 40  
[2023-03-31 20:41:16] local.ALERT: 2  
[2023-03-31 20:41:16] local.ALERT: 40  
[2023-03-31 20:41:16] local.ALERT: 2  
[2023-03-31 20:41:16] local.ALERT: 40  
[2023-03-31 20:41:16] local.ALERT: 2  
[2023-03-31 20:41:16] local.ALERT: 40  
[2023-03-31 20:41:16] local.ALERT: 2  
[2023-03-31 20:41:16] local.ALERT: 40  
[2023-03-31 20:41:16] local.ALERT: 2  
[2023-03-31 20:41:16] local.ALERT: 40  
[2023-03-31 20:41:16] local.ALERT: 2  
[2023-03-31 20:41:16] local.ALERT: 40  
[2023-03-31 20:41:16] local.ALERT: 1  
[2023-03-31 20:41:16] local.ALERT: 40  
[2023-03-31 20:41:16] local.ALERT: 1  
[2023-03-31 20:41:16] local.ALERT: 40  
[2023-03-31 20:41:16] local.ALERT: 1  
[2023-03-31 20:41:16] local.ALERT: 40  
[2023-03-31 20:41:16] local.ALERT: 1  
[2023-03-31 20:41:16] local.ALERT: 40  
[2023-03-31 20:41:16] local.ALERT: 1  
[2023-03-31 20:41:16] local.ALERT: 40  
[2023-03-31 20:41:16] local.ALERT: 2  
[2023-03-31 20:41:16] local.ALERT: 40  
[2023-03-31 20:41:16] local.ALERT: 10013  
[2023-03-31 20:41:16] local.ALERT: 40  
[2023-03-31 20:41:16] local.ALERT: 10013  
[2023-03-31 20:41:16] local.ALERT: 40  
[2023-03-31 20:41:16] local.ALERT: 10013  
[2023-03-31 20:41:16] local.ALERT: 40  
[2023-03-31 20:41:16] local.ALERT: 10013  
[2023-03-31 20:41:16] local.ALERT: 40  
[2023-03-31 20:41:16] local.ALERT: 1  
[2023-03-31 20:41:16] local.ALERT: 40  
[2023-03-31 20:41:16] local.ALERT: 3  
[2023-03-31 20:41:16] local.ALERT: 40  
[2023-03-31 20:41:16] local.ALERT: 3  
[2023-03-31 20:41:16] local.ALERT: 40  
[2023-03-31 20:41:16] local.ALERT: 40  
[2023-03-31 20:41:16] local.ALERT: 40  
[2023-03-31 20:41:16] local.ALERT: 40  
[2023-03-31 20:41:16] local.ALERT: 40  
[2023-03-31 20:41:16] local.ALERT: 40  
[2023-03-31 20:41:16] local.WARNING: 1  
[2023-03-31 20:41:16] local.WARNING: array (
  'ID' => 84,
  'Name' => 'باقة 80جيجا 12000 ريال',
  'ServiceID' => 40,
  'Price' => 12000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '12000',
  'PersonnalPrice' => 12000.0,
)  
[2023-03-31 20:41:16] local.ALERT: 40  
[2023-03-31 20:41:16] local.ALERT: 40  
[2023-03-31 20:41:16] local.ALERT: 40  
[2023-03-31 20:41:16] local.ALERT: 200  
[2023-03-31 20:41:16] local.ALERT: 40  
[2023-03-31 20:41:16] local.ALERT: 200  
[2023-03-31 20:41:16] local.ALERT: 40  
[2023-03-31 20:41:16] local.ALERT: 200  
[2023-03-31 20:41:16] local.ALERT: 40  
[2023-03-31 20:41:16] local.ALERT: 200  
[2023-03-31 20:41:16] local.ALERT: 40  
[2023-03-31 20:41:16] local.ALERT: 200  
[2023-03-31 20:41:16] local.ALERT: 40  
[2023-03-31 20:41:16] local.ALERT: 200  
[2023-03-31 20:41:16] local.ALERT: 40  
[2023-03-31 20:41:16] local.ALERT: 40  
[2023-03-31 20:41:16] local.CRITICAL: ****************************2  
[2023-03-31 20:41:16] local.CRITICAL: ****************************  
[2023-03-31 20:41:16] local.CRITICAL:   
[2023-03-31 20:41:16] local.CRITICAL: ****************************  
[2023-03-31 20:41:18] local.INFO: {
  "ClientBalanceResult": "91812.3625"
}  
[2023-03-31 20:41:18] local.INFO: array (
  'ClientBalanceResult' => '91812.3625',
)  
[2023-03-31 20:41:18] local.DEBUG: lattttef  
[2023-03-31 20:41:18] local.DEBUG: array (
  'ClientBalanceResult' => '91812.3625',
)  
[2023-03-31 20:41:18] local.INFO: transaction1  
[2023-03-31 20:41:18] local.INFO: transaction2  
[2023-03-31 20:41:18] local.INFO: transaction3  
[2023-03-31 20:41:18] local.INFO: transaction4  
[2023-03-31 20:41:18] local.INFO: transaction4  
[2023-03-31 20:41:18] local.INFO: transaction5  
[2023-03-31 20:41:18] local.INFO: transaction6  
[2023-03-31 20:41:18] local.INFO: transaction7  
[2023-03-31 20:41:18] local.DEBUG: array (
  'AMT' => 12000.0,
  'CType' => 0,
  'FID' => 84,
  'LType' => '1',
  'SID' => 40,
  'SNO' => '798716841',
  'State' => 0,
  'lateflog' => '306541',
)  
[2023-03-31 20:41:18] local.INFO: transaction8  
[2023-03-31 20:41:18] local.INFO: transaction9  
[2023-03-31 20:41:18] local.INFO: transaction10  
[2023-03-31 20:41:18] local.INFO: transaction11  
[2023-03-31 20:41:18] local.INFO: 12  
[2023-03-31 20:41:18] local.INFO: transaction13  
[2023-03-31 20:41:18] local.INFO: transaction14  
[2023-03-31 20:41:18] local.INFO: transaction19  
[2023-03-31 20:41:18] local.INFO: transaction15  
[2023-03-31 20:41:24] local.INFO: array (
  'resultCode' => '0',
  'resultDesc' => 'success',
  'price' => '12060',
  'remainAmount' => 23808603,
  'mallrem' => -26191397,
  'transid' => '4439106',
  'ref_id' => 26434829,
)  
[2023-03-31 22:22:49] local.INFO: header  
[2023-03-31 22:22:50] local.INFO: header after fliter  
[2023-03-31 22:22:50] local.INFO: Body  after fliter  
[2023-03-31 22:22:50] local.INFO: array (
)  
[2023-03-31 22:22:50] local.INFO: transaction14  
[2023-03-31 22:22:50] local.INFO: first inquery phone = 107666946  
[2023-03-31 22:22:54] local.DEBUG: response querySubBalance  
[2023-03-31 22:22:54] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#8,000.00#8,000.00#9.85 MB#13-04-2023#0#0##4183.00#4G 60</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-31 22:22:54] local.DEBUG: array (
  0 => 'OK',
  1 => '8,000.00',
  2 => '8,000.00',
  3 => '9.85 MB',
  4 => '13-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '4183.00',
  9 => '4G 60',
)  
[2023-03-31 22:22:54] local.DEBUG: print  before faction by provider price  
[2023-03-31 22:22:55] local.DEBUG: print  after faction by provider price  
[2023-03-31 22:22:55] local.DEBUG: فئة 60 جيجا 8000 ريال  
[2023-03-31 22:22:55] local.DEBUG: print1  
[2023-03-31 22:22:55] local.DEBUG: print  2  
[2023-03-31 22:23:15] local.INFO: header  
[2023-03-31 22:23:15] local.CRITICAL: ****************************1  
[2023-03-31 22:23:15] local.ALERT: reach here  
[2023-03-31 22:23:16] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '8000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ثمانية ألف  ر.ي.',
  ),
)  
[2023-03-31 22:23:16] local.ERROR: المبلغ  
[2023-03-31 22:23:16] local.ERROR: 8,000.00  
[2023-03-31 22:23:16] local.ERROR: مبلغ وقدرة  
[2023-03-31 22:23:16] local.ALERT: 200  
[2023-03-31 22:23:16] local.ALERT: 3  
[2023-03-31 22:23:16] local.ALERT: 200  
[2023-03-31 22:23:16] local.ALERT: 3  
[2023-03-31 22:23:16] local.ALERT: 200  
[2023-03-31 22:23:16] local.ALERT: 3  
[2023-03-31 22:23:16] local.ALERT: 200  
[2023-03-31 22:23:16] local.ALERT: 3  
[2023-03-31 22:23:16] local.ALERT: 200  
[2023-03-31 22:23:16] local.ALERT: 3  
[2023-03-31 22:23:16] local.ALERT: 200  
[2023-03-31 22:23:16] local.ALERT: 3  
[2023-03-31 22:23:16] local.ALERT: 200  
[2023-03-31 22:23:16] local.ALERT: 3  
[2023-03-31 22:23:16] local.ALERT: 200  
[2023-03-31 22:23:16] local.ALERT: 3  
[2023-03-31 22:23:16] local.ALERT: 200  
[2023-03-31 22:23:16] local.ALERT: 3  
[2023-03-31 22:23:16] local.ALERT: 200  
[2023-03-31 22:23:16] local.ALERT: 2  
[2023-03-31 22:23:16] local.ALERT: 200  
[2023-03-31 22:23:16] local.ALERT: 2  
[2023-03-31 22:23:16] local.ALERT: 200  
[2023-03-31 22:23:16] local.ALERT: 2  
[2023-03-31 22:23:16] local.ALERT: 200  
[2023-03-31 22:23:16] local.ALERT: 2  
[2023-03-31 22:23:16] local.ALERT: 200  
[2023-03-31 22:23:16] local.ALERT: 2  
[2023-03-31 22:23:16] local.ALERT: 200  
[2023-03-31 22:23:16] local.ALERT: 2  
[2023-03-31 22:23:16] local.ALERT: 200  
[2023-03-31 22:23:16] local.ALERT: 2  
[2023-03-31 22:23:16] local.ALERT: 200  
[2023-03-31 22:23:16] local.ALERT: 1  
[2023-03-31 22:23:16] local.ALERT: 200  
[2023-03-31 22:23:16] local.ALERT: 1  
[2023-03-31 22:23:16] local.ALERT: 200  
[2023-03-31 22:23:16] local.ALERT: 1  
[2023-03-31 22:23:16] local.ALERT: 200  
[2023-03-31 22:23:16] local.ALERT: 1  
[2023-03-31 22:23:16] local.ALERT: 200  
[2023-03-31 22:23:16] local.ALERT: 1  
[2023-03-31 22:23:16] local.ALERT: 200  
[2023-03-31 22:23:16] local.ALERT: 2  
[2023-03-31 22:23:16] local.ALERT: 200  
[2023-03-31 22:23:16] local.ALERT: 10013  
[2023-03-31 22:23:16] local.ALERT: 200  
[2023-03-31 22:23:16] local.ALERT: 10013  
[2023-03-31 22:23:16] local.ALERT: 200  
[2023-03-31 22:23:16] local.ALERT: 10013  
[2023-03-31 22:23:16] local.ALERT: 200  
[2023-03-31 22:23:16] local.ALERT: 10013  
[2023-03-31 22:23:16] local.ALERT: 200  
[2023-03-31 22:23:16] local.ALERT: 1  
[2023-03-31 22:23:16] local.ALERT: 200  
[2023-03-31 22:23:16] local.ALERT: 3  
[2023-03-31 22:23:16] local.ALERT: 200  
[2023-03-31 22:23:16] local.ALERT: 3  
[2023-03-31 22:23:16] local.ALERT: 200  
[2023-03-31 22:23:16] local.ALERT: 40  
[2023-03-31 22:23:16] local.ALERT: 200  
[2023-03-31 22:23:16] local.ALERT: 40  
[2023-03-31 22:23:16] local.ALERT: 200  
[2023-03-31 22:23:16] local.ALERT: 40  
[2023-03-31 22:23:16] local.ALERT: 200  
[2023-03-31 22:23:16] local.ALERT: 40  
[2023-03-31 22:23:16] local.ALERT: 200  
[2023-03-31 22:23:16] local.ALERT: 200  
[2023-03-31 22:23:16] local.ALERT: 200  
[2023-03-31 22:23:16] local.ALERT: 200  
[2023-03-31 22:23:16] local.WARNING: 1  
[2023-03-31 22:23:16] local.WARNING: array (
  'ID' => 87,
  'Name' => 'فئة 60 جيجا 8000 ريال',
  'ServiceID' => 200,
  'Price' => 8000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 8000.0,
)  
[2023-03-31 22:23:16] local.ALERT: 200  
[2023-03-31 22:23:16] local.ALERT: 200  
[2023-03-31 22:23:16] local.ALERT: 200  
[2023-03-31 22:23:16] local.ALERT: 200  
[2023-03-31 22:23:16] local.ALERT: 200  
[2023-03-31 22:23:16] local.ALERT: 200  
[2023-03-31 22:23:16] local.ALERT: 200  
[2023-03-31 22:23:16] local.ALERT: 200  
[2023-03-31 22:23:16] local.ALERT: 200  
[2023-03-31 22:23:16] local.ALERT: 40  
[2023-03-31 22:23:16] local.CRITICAL: ****************************2  
[2023-03-31 22:23:16] local.CRITICAL: ****************************  
[2023-03-31 22:23:16] local.CRITICAL:   
[2023-03-31 22:23:16] local.CRITICAL: ****************************  
[2023-03-31 22:23:17] local.INFO: {
  "ClientBalanceResult": "110432.3750"
}  
[2023-03-31 22:23:17] local.INFO: array (
  'ClientBalanceResult' => '110432.3750',
)  
[2023-03-31 22:23:17] local.DEBUG: lattttef  
[2023-03-31 22:23:17] local.DEBUG: array (
  'ClientBalanceResult' => '110432.3750',
)  
[2023-03-31 22:23:17] local.INFO: transaction14  
[2023-03-31 22:23:17] local.INFO: first inquery phone = 107666946  
[2023-03-31 22:23:20] local.DEBUG: response querySubBalance  
[2023-03-31 22:23:20] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#8,000.00#8,000.00#9.85 MB#13-04-2023#0#0##4183.00#4G 60</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-31 22:23:20] local.DEBUG: array (
  0 => 'OK',
  1 => '8,000.00',
  2 => '8,000.00',
  3 => '9.85 MB',
  4 => '13-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '4183.00',
  9 => '4G 60',
)  
[2023-03-31 22:23:20] local.DEBUG: print  before faction by provider price  
[2023-03-31 22:23:20] local.DEBUG: print  after faction by provider price  
[2023-03-31 22:23:20] local.DEBUG: فئة 60 جيجا 8000 ريال  
[2023-03-31 22:23:20] local.DEBUG: print1  
[2023-03-31 22:23:20] local.DEBUG: print  2  
[2023-03-31 22:23:20] local.INFO: transaction1  
[2023-03-31 22:23:20] local.INFO: transaction2  
[2023-03-31 22:23:20] local.INFO: transaction3  
[2023-03-31 22:23:20] local.INFO: transaction4  
[2023-03-31 22:23:20] local.INFO: transaction4  
[2023-03-31 22:23:20] local.INFO: transaction5  
[2023-03-31 22:23:20] local.INFO: transaction6  
[2023-03-31 22:23:20] local.INFO: transaction7  
[2023-03-31 22:23:20] local.DEBUG: array (
  'AMT' => 8000.0,
  'CType' => 0,
  'FID' => 87,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '107666946',
  'State' => 0,
  'lateflog' => '389078',
)  
[2023-03-31 22:23:20] local.INFO: transaction8  
[2023-03-31 22:23:21] local.INFO: transaction9  
[2023-03-31 22:23:21] local.INFO: transaction10  
[2023-03-31 22:23:21] local.INFO: transaction11  
[2023-03-31 22:23:21] local.INFO: 12  
[2023-03-31 22:23:21] local.INFO: transaction13  
[2023-03-31 22:23:21] local.INFO: transaction14  
[2023-03-31 22:23:21] local.INFO: transaction19  
[2023-03-31 22:23:21] local.INFO: transaction15  
[2023-03-31 22:23:21] local.INFO: transaction16  
[2023-03-31 22:23:21] local.INFO: 98#107666946#8000.00#0  
[2023-03-31 22:23:30] local.INFO: transaction18  
[2023-03-31 22:23:30] local.INFO: array (
  0 => 'OK',
  1 => '2,681,613.83',
  2 => 'NONE',
  3 => '61838368',
  4 => '8,000.00',
)  
[2023-03-31 22:33:35] local.INFO: header  
[2023-03-31 22:33:35] local.INFO: header after fliter  
[2023-03-31 22:33:35] local.INFO: Body  after fliter  
[2023-03-31 22:33:35] local.INFO: array (
)  
[2023-03-31 22:33:35] local.INFO: transaction14  
[2023-03-31 22:33:35] local.INFO: first inquery phone = 103330532  
[2023-03-31 22:33:38] local.DEBUG: response querySubBalance  
[2023-03-31 22:33:38] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#8,000.00#8,000.00#39.28 GB#28-04-2023#0#0##14265.00#4G 60</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-31 22:33:38] local.DEBUG: array (
  0 => 'OK',
  1 => '8,000.00',
  2 => '8,000.00',
  3 => '39.28 GB',
  4 => '28-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '14265.00',
  9 => '4G 60',
)  
[2023-03-31 22:33:38] local.DEBUG: print  before faction by provider price  
[2023-03-31 22:33:39] local.DEBUG: print  after faction by provider price  
[2023-03-31 22:33:39] local.DEBUG: فئة 60 جيجا 8000 ريال  
[2023-03-31 22:33:39] local.DEBUG: print1  
[2023-03-31 22:33:39] local.DEBUG: print  2  
