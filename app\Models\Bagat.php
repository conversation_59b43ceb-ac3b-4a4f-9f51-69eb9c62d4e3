<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Bagat extends Model
{
    use HasFactory;

    protected $table = 'Bagat';
    public $timestamps = false;

    protected $fillable = [
        'RowVersion',
        'Name',
        'Code',
        'LineType',
        'Mode',
        'Price',
        'CreatedBy',
        'BranchID',
        'CreatedTime',
        'SimType',
        'ServiceID',
        'TelPrice',
        'ProviderPrice',
        'PersonnalPrice',
        'OrderNo',
        'Number',
        'Quantity',
        'Units',
        'Active',
        'Balance',
        'Command',
        'Description',
        'CategoryID',
        'ProviderID',
        'ByProvider',
    ];

    public function branch()
    {
        return $this->belongsTo(Branch::class, 'BranchID');
    }

    public function serviceInfo()
    {
        return $this->belongsTo(ServiceInfo::class, 'ServiceID');
    }

    public function createdByUser()
    {
        return $this->belongsTo(UserInfo::class, 'CreatedBy');
    }
}
