<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Faction extends Model
{
    use HasFactory;

    protected $table = 'Faction';

    public $timestamps = false;

    protected $fillable = [
        'ID',
        'RowVersion',
        'Name',
        'Price',
        'Type',
        'CreatedBy',
        'BranchID',
        'CreatedTime',
        'ServiceID',
        'ProviderPrice',
        'PersonnalPrice',
        'OrderNo',
        'ProviderCode',
        'RefID',
        'Note',
        'Number',
        'CurrencyID',
        'CategoryID',
        'ProviderID',
        'Description',
        'Status',
        'ClassType',
        'RefNumber',
        'LineType',
        'Quantity',
        'Units',
        'Active',
        'Balance',
        'Command',
        'Code'
    ];
}
