<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class JournalEntry extends Model
{
    use HasFactory;
    protected $table = 'JournalEntry';
    public $timestamps = false;

    protected $fillable = [
        'ID',
        'ParentID',
        'Amount',
        'CurrencyID',
        'DCAmount',
        'AccountID',
        'CostCenterID',
        'Note',
        'Datestamp',
        'ExchangeRate',
        'SyncEntryID'
    ];
}
