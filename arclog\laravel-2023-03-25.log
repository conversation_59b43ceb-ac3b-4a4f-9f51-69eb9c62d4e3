[2023-03-25 00:20:29] local.INFO: header  
[2023-03-25 00:20:29] local.INFO: header after fliter  
[2023-03-25 00:20:29] local.INFO: Body  after fliter  
[2023-03-25 00:20:29] local.INFO: array (
)  
[2023-03-25 00:20:29] local.INFO: transaction14  
[2023-03-25 00:20:29] local.INFO: first inquery phone = 106400860  
[2023-03-25 00:20:34] local.DEBUG: response querySubBalance  
[2023-03-25 00:20:34] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#8.80 GB#24-04-2023#0#0##.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-25 00:20:34] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '8.80 GB',
  4 => '24-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 15',
)  
[2023-03-25 00:20:34] local.DEBUG: print  before faction by provider price  
[2023-03-25 00:20:34] local.DEBUG: print  after faction by provider price  
[2023-03-25 00:20:34] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-25 00:20:34] local.DEBUG: print1  
[2023-03-25 00:20:34] local.DEBUG: print  2  
[2023-03-25 00:20:47] local.INFO: header  
[2023-03-25 00:20:47] local.INFO: header after fliter  
[2023-03-25 00:20:47] local.INFO: Body  after fliter  
[2023-03-25 00:20:47] local.INFO: array (
)  
[2023-03-25 00:20:47] local.INFO: transaction14  
[2023-03-25 00:20:47] local.INFO: first inquery phone = 106400976  
[2023-03-25 00:20:50] local.DEBUG: response querySubBalance  
[2023-03-25 00:20:50] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#59.22 GB#15-04-2023#0#0##10501.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-25 00:20:50] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '59.22 GB',
  4 => '15-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '10501.00',
  9 => '4G 250',
)  
[2023-03-25 00:20:50] local.DEBUG: print  before faction by provider price  
[2023-03-25 00:20:50] local.DEBUG: print  after faction by provider price  
[2023-03-25 00:20:50] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-25 00:20:50] local.DEBUG: print1  
[2023-03-25 00:20:50] local.DEBUG: print  2  
[2023-03-25 00:21:12] local.INFO: header  
[2023-03-25 00:21:12] local.INFO: header after fliter  
[2023-03-25 00:21:12] local.INFO: Body  after fliter  
[2023-03-25 00:21:12] local.INFO: array (
)  
[2023-03-25 00:21:12] local.INFO: transaction14  
[2023-03-25 00:21:12] local.INFO: first inquery phone = 106400598  
[2023-03-25 00:21:15] local.DEBUG: response querySubBalance  
[2023-03-25 00:21:15] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#139.29 GB#21-04-2023#0#0##9500.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-25 00:21:15] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '139.29 GB',
  4 => '21-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '9500.00',
  9 => '4G 250',
)  
[2023-03-25 00:21:15] local.DEBUG: print  before faction by provider price  
[2023-03-25 00:21:15] local.DEBUG: print  after faction by provider price  
[2023-03-25 00:21:15] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-25 00:21:15] local.DEBUG: print1  
[2023-03-25 00:21:15] local.DEBUG: print  2  
[2023-03-25 00:22:23] local.INFO: header  
[2023-03-25 00:22:24] local.INFO: header after fliter  
[2023-03-25 00:22:24] local.INFO: Body  after fliter  
[2023-03-25 00:22:24] local.INFO: array (
)  
[2023-03-25 00:22:24] local.INFO: transaction14  
[2023-03-25 00:22:24] local.INFO: first inquery phone = 106400598  
[2023-03-25 00:22:27] local.DEBUG: response querySubBalance  
[2023-03-25 00:22:27] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#139.25 GB#21-04-2023#0#0##9500.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-25 00:22:27] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '139.25 GB',
  4 => '21-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '9500.00',
  9 => '4G 250',
)  
[2023-03-25 00:22:27] local.DEBUG: print  before faction by provider price  
[2023-03-25 00:22:27] local.DEBUG: print  after faction by provider price  
[2023-03-25 00:22:27] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-25 00:22:27] local.DEBUG: print1  
[2023-03-25 00:22:27] local.DEBUG: print  2  
[2023-03-25 01:06:13] local.INFO: header  
[2023-03-25 01:06:13] local.INFO: header after fliter  
[2023-03-25 01:06:13] local.INFO: Body  after fliter  
[2023-03-25 01:06:13] local.INFO: array (
)  
[2023-03-25 01:06:13] local.INFO: transaction14  
[2023-03-25 01:06:13] local.INFO: first inquery phone = 107666946  
[2023-03-25 01:06:15] local.DEBUG: response querySubBalance  
[2023-03-25 01:06:15] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#8,000.00#8,000.00#9.85 MB#13-04-2023#0#0##4183.00#4G 60</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-25 01:06:15] local.DEBUG: array (
  0 => 'OK',
  1 => '8,000.00',
  2 => '8,000.00',
  3 => '9.85 MB',
  4 => '13-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '4183.00',
  9 => '4G 60',
)  
[2023-03-25 01:06:15] local.DEBUG: print  before faction by provider price  
[2023-03-25 01:06:15] local.DEBUG: print  after faction by provider price  
[2023-03-25 01:06:15] local.DEBUG: فئة 60 جيجا 8000 ريال  
[2023-03-25 01:06:15] local.DEBUG: print1  
[2023-03-25 01:06:15] local.DEBUG: print  2  
[2023-03-25 01:10:25] local.INFO: header  
[2023-03-25 01:10:25] local.INFO: header after fliter  
[2023-03-25 01:10:25] local.INFO: Body  after fliter  
[2023-03-25 01:10:25] local.INFO: array (
)  
[2023-03-25 01:10:25] local.INFO: transaction14  
[2023-03-25 01:10:25] local.INFO: first inquery phone = 106400860  
[2023-03-25 01:10:29] local.DEBUG: response querySubBalance  
[2023-03-25 01:10:29] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#7.05 GB#24-04-2023#0#0##.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-25 01:10:29] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '7.05 GB',
  4 => '24-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 15',
)  
[2023-03-25 01:10:29] local.DEBUG: print  before faction by provider price  
[2023-03-25 01:10:29] local.DEBUG: print  after faction by provider price  
[2023-03-25 01:10:29] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-25 01:10:29] local.DEBUG: print1  
[2023-03-25 01:10:29] local.DEBUG: print  2  
[2023-03-25 01:12:10] local.INFO: header  
[2023-03-25 01:12:10] local.INFO: header after fliter  
[2023-03-25 01:12:10] local.INFO: Body  after fliter  
[2023-03-25 01:12:10] local.INFO: array (
)  
[2023-03-25 01:12:10] local.INFO: transaction14  
[2023-03-25 01:12:10] local.INFO: first inquery phone = 106400976  
[2023-03-25 01:12:13] local.DEBUG: response querySubBalance  
[2023-03-25 01:12:13] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#57.66 GB#15-04-2023#0#0##10501.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-25 01:12:13] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '57.66 GB',
  4 => '15-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '10501.00',
  9 => '4G 250',
)  
[2023-03-25 01:12:13] local.DEBUG: print  before faction by provider price  
[2023-03-25 01:12:13] local.DEBUG: print  after faction by provider price  
[2023-03-25 01:12:13] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-25 01:12:13] local.DEBUG: print1  
[2023-03-25 01:12:13] local.DEBUG: print  2  
[2023-03-25 01:12:31] local.INFO: header  
[2023-03-25 01:12:31] local.INFO: header after fliter  
[2023-03-25 01:12:31] local.INFO: Body  after fliter  
[2023-03-25 01:12:31] local.INFO: array (
)  
[2023-03-25 01:12:31] local.INFO: transaction14  
[2023-03-25 01:12:31] local.INFO: first inquery phone = 106400598  
[2023-03-25 01:12:36] local.DEBUG: response querySubBalance  
[2023-03-25 01:12:36] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#137.43 GB#21-04-2023#0#0##9500.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-25 01:12:36] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '137.43 GB',
  4 => '21-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '9500.00',
  9 => '4G 250',
)  
[2023-03-25 01:12:36] local.DEBUG: print  before faction by provider price  
[2023-03-25 01:12:36] local.DEBUG: print  after faction by provider price  
[2023-03-25 01:12:36] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-25 01:12:36] local.DEBUG: print1  
[2023-03-25 01:12:36] local.DEBUG: print  2  
[2023-03-25 01:13:20] local.INFO: header  
[2023-03-25 01:13:20] local.INFO: header after fliter  
[2023-03-25 01:13:20] local.INFO: Body  after fliter  
[2023-03-25 01:13:20] local.INFO: array (
)  
[2023-03-25 01:13:20] local.INFO: transaction14  
[2023-03-25 01:13:20] local.INFO: first inquery phone = 106400598  
[2023-03-25 01:13:23] local.DEBUG: response querySubBalance  
[2023-03-25 01:13:23] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#137.38 GB#21-04-2023#0#0##9500.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-25 01:13:23] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '137.38 GB',
  4 => '21-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '9500.00',
  9 => '4G 250',
)  
[2023-03-25 01:13:23] local.DEBUG: print  before faction by provider price  
[2023-03-25 01:13:23] local.DEBUG: print  after faction by provider price  
[2023-03-25 01:13:23] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-25 01:13:23] local.DEBUG: print1  
[2023-03-25 01:13:23] local.DEBUG: print  2  
[2023-03-25 01:14:01] local.INFO: header  
[2023-03-25 01:14:01] local.INFO: header after fliter  
[2023-03-25 01:14:01] local.INFO: Body  after fliter  
[2023-03-25 01:14:01] local.INFO: array (
)  
[2023-03-25 01:14:01] local.INFO: transaction14  
[2023-03-25 01:14:01] local.INFO: first inquery phone = 106400598  
[2023-03-25 01:14:04] local.DEBUG: response querySubBalance  
[2023-03-25 01:14:04] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#137.38 GB#21-04-2023#0#0##9500.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-25 01:14:04] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '137.38 GB',
  4 => '21-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '9500.00',
  9 => '4G 250',
)  
[2023-03-25 01:14:04] local.DEBUG: print  before faction by provider price  
[2023-03-25 01:14:04] local.DEBUG: print  after faction by provider price  
[2023-03-25 01:14:04] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-25 01:14:04] local.DEBUG: print1  
[2023-03-25 01:14:04] local.DEBUG: print  2  
[2023-03-25 01:14:52] local.INFO: header  
[2023-03-25 01:14:52] local.INFO: header after fliter  
[2023-03-25 01:14:52] local.INFO: Body  after fliter  
[2023-03-25 01:14:52] local.INFO: array (
)  
[2023-03-25 01:14:52] local.INFO: transaction14  
[2023-03-25 01:14:52] local.INFO: first inquery phone = 106400598  
[2023-03-25 01:14:55] local.DEBUG: response querySubBalance  
[2023-03-25 01:14:55] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#137.33 GB#21-04-2023#0#0##9500.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-25 01:14:55] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '137.33 GB',
  4 => '21-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '9500.00',
  9 => '4G 250',
)  
[2023-03-25 01:14:55] local.DEBUG: print  before faction by provider price  
[2023-03-25 01:14:55] local.DEBUG: print  after faction by provider price  
[2023-03-25 01:14:55] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-25 01:14:55] local.DEBUG: print1  
[2023-03-25 01:14:55] local.DEBUG: print  2  
[2023-03-25 01:15:20] local.INFO: header  
[2023-03-25 01:15:20] local.INFO: header after fliter  
[2023-03-25 01:15:20] local.INFO: Body  after fliter  
[2023-03-25 01:15:20] local.INFO: array (
)  
[2023-03-25 01:15:20] local.INFO: transaction14  
[2023-03-25 01:15:20] local.INFO: first inquery phone = 106400976  
[2023-03-25 01:15:23] local.DEBUG: response querySubBalance  
[2023-03-25 01:15:23] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#57.51 GB#15-04-2023#0#0##10501.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-25 01:15:23] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '57.51 GB',
  4 => '15-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '10501.00',
  9 => '4G 250',
)  
[2023-03-25 01:15:23] local.DEBUG: print  before faction by provider price  
[2023-03-25 01:15:23] local.DEBUG: print  after faction by provider price  
[2023-03-25 01:15:23] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-25 01:15:23] local.DEBUG: print1  
[2023-03-25 01:15:23] local.DEBUG: print  2  
[2023-03-25 01:15:37] local.INFO: header  
[2023-03-25 01:15:37] local.INFO: header after fliter  
[2023-03-25 01:15:37] local.INFO: Body  after fliter  
[2023-03-25 01:15:37] local.INFO: array (
)  
[2023-03-25 01:15:37] local.INFO: transaction14  
[2023-03-25 01:15:37] local.INFO: first inquery phone = 106400860  
[2023-03-25 01:15:41] local.DEBUG: response querySubBalance  
[2023-03-25 01:15:41] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#6.90 GB#24-04-2023#0#0##.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-25 01:15:41] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '6.90 GB',
  4 => '24-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 15',
)  
[2023-03-25 01:15:41] local.DEBUG: print  before faction by provider price  
[2023-03-25 01:15:41] local.DEBUG: print  after faction by provider price  
[2023-03-25 01:15:41] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-25 01:15:41] local.DEBUG: print1  
[2023-03-25 01:15:41] local.DEBUG: print  2  
[2023-03-25 01:16:19] local.INFO: header  
[2023-03-25 01:16:19] local.INFO: header after fliter  
[2023-03-25 01:16:19] local.INFO: Body  after fliter  
[2023-03-25 01:16:19] local.INFO: array (
)  
[2023-03-25 01:16:19] local.INFO: transaction14  
[2023-03-25 01:16:19] local.INFO: first inquery phone = 106400860  
[2023-03-25 01:16:25] local.DEBUG: response querySubBalance  
[2023-03-25 01:16:25] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#6.85 GB#24-04-2023#0#0##.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-25 01:16:25] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '6.85 GB',
  4 => '24-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 15',
)  
[2023-03-25 01:16:25] local.DEBUG: print  before faction by provider price  
[2023-03-25 01:16:25] local.DEBUG: print  after faction by provider price  
[2023-03-25 01:16:25] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-25 01:16:25] local.DEBUG: print1  
[2023-03-25 01:16:25] local.DEBUG: print  2  
[2023-03-25 01:16:29] local.INFO: header  
[2023-03-25 01:16:29] local.INFO: header after fliter  
[2023-03-25 01:16:29] local.INFO: Body  after fliter  
[2023-03-25 01:16:29] local.INFO: array (
)  
[2023-03-25 01:16:29] local.INFO: transaction14  
[2023-03-25 01:16:29] local.INFO: first inquery phone = 106400860  
[2023-03-25 01:16:31] local.DEBUG: response querySubBalance  
[2023-03-25 01:16:31] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#6.85 GB#24-04-2023#0#0##.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-25 01:16:31] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '6.85 GB',
  4 => '24-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 15',
)  
[2023-03-25 01:16:31] local.DEBUG: print  before faction by provider price  
[2023-03-25 01:16:31] local.DEBUG: print  after faction by provider price  
[2023-03-25 01:16:31] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-25 01:16:31] local.DEBUG: print1  
[2023-03-25 01:16:31] local.DEBUG: print  2  
[2023-03-25 01:16:53] local.INFO: header  
[2023-03-25 01:16:53] local.INFO: header after fliter  
[2023-03-25 01:16:53] local.INFO: Body  after fliter  
[2023-03-25 01:16:53] local.INFO: array (
)  
[2023-03-25 01:16:53] local.INFO: transaction14  
[2023-03-25 01:16:53] local.INFO: first inquery phone = 106400860  
[2023-03-25 01:16:56] local.DEBUG: response querySubBalance  
[2023-03-25 01:16:56] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#6.85 GB#24-04-2023#0#0##.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-25 01:16:56] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '6.85 GB',
  4 => '24-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 15',
)  
[2023-03-25 01:16:56] local.DEBUG: print  before faction by provider price  
[2023-03-25 01:16:57] local.DEBUG: print  after faction by provider price  
[2023-03-25 01:16:57] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-25 01:16:57] local.DEBUG: print1  
[2023-03-25 01:16:57] local.DEBUG: print  2  
[2023-03-25 01:17:42] local.INFO: header  
[2023-03-25 01:17:42] local.INFO: header after fliter  
[2023-03-25 01:17:42] local.INFO: Body  after fliter  
[2023-03-25 01:17:42] local.INFO: array (
)  
[2023-03-25 01:17:42] local.INFO: transaction14  
[2023-03-25 01:17:42] local.INFO: first inquery phone = 106400860  
[2023-03-25 01:17:46] local.DEBUG: response querySubBalance  
[2023-03-25 01:17:46] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#6.80 GB#24-04-2023#0#0##.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-25 01:17:46] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '6.80 GB',
  4 => '24-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 15',
)  
[2023-03-25 01:17:46] local.DEBUG: print  before faction by provider price  
[2023-03-25 01:17:46] local.DEBUG: print  after faction by provider price  
[2023-03-25 01:17:46] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-25 01:17:46] local.DEBUG: print1  
[2023-03-25 01:17:46] local.DEBUG: print  2  
[2023-03-25 01:18:01] local.INFO: header  
[2023-03-25 01:18:01] local.INFO: header after fliter  
[2023-03-25 01:18:01] local.INFO: Body  after fliter  
[2023-03-25 01:18:01] local.INFO: array (
)  
[2023-03-25 01:18:01] local.INFO: transaction14  
[2023-03-25 01:18:01] local.INFO: first inquery phone = 106400860  
[2023-03-25 01:18:07] local.DEBUG: response querySubBalance  
[2023-03-25 01:18:07] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#6.80 GB#24-04-2023#0#0##.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-25 01:18:07] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '6.80 GB',
  4 => '24-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 15',
)  
[2023-03-25 01:18:07] local.DEBUG: print  before faction by provider price  
[2023-03-25 01:18:07] local.DEBUG: print  after faction by provider price  
[2023-03-25 01:18:07] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-25 01:18:07] local.DEBUG: print1  
[2023-03-25 01:18:07] local.DEBUG: print  2  
[2023-03-25 01:20:43] local.INFO: header  
[2023-03-25 01:20:43] local.INFO: header after fliter  
[2023-03-25 01:20:43] local.INFO: Body  after fliter  
[2023-03-25 01:20:43] local.INFO: array (
)  
[2023-03-25 01:20:43] local.INFO: transaction14  
[2023-03-25 01:20:43] local.INFO: first inquery phone = 107666946  
[2023-03-25 01:20:47] local.DEBUG: response querySubBalance  
[2023-03-25 01:20:47] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#8,000.00#8,000.00#9.85 MB#13-04-2023#0#0##4183.00#4G 60</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-25 01:20:47] local.DEBUG: array (
  0 => 'OK',
  1 => '8,000.00',
  2 => '8,000.00',
  3 => '9.85 MB',
  4 => '13-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '4183.00',
  9 => '4G 60',
)  
[2023-03-25 01:20:47] local.DEBUG: print  before faction by provider price  
[2023-03-25 01:20:47] local.DEBUG: print  after faction by provider price  
[2023-03-25 01:20:47] local.DEBUG: فئة 60 جيجا 8000 ريال  
[2023-03-25 01:20:47] local.DEBUG: print1  
[2023-03-25 01:20:47] local.DEBUG: print  2  
[2023-03-25 01:22:39] local.INFO: header  
[2023-03-25 01:22:39] local.INFO: header after fliter  
[2023-03-25 01:22:39] local.INFO: Body  after fliter  
[2023-03-25 01:22:39] local.INFO: array (
)  
[2023-03-25 01:22:39] local.INFO: transaction14  
[2023-03-25 01:22:39] local.INFO: first inquery phone = 106400860  
[2023-03-25 01:22:44] local.DEBUG: response querySubBalance  
[2023-03-25 01:22:44] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#6.65 GB#24-04-2023#0#0##.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-25 01:22:44] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '6.65 GB',
  4 => '24-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 15',
)  
[2023-03-25 01:22:44] local.DEBUG: print  before faction by provider price  
[2023-03-25 01:22:44] local.DEBUG: print  after faction by provider price  
[2023-03-25 01:22:44] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-25 01:22:44] local.DEBUG: print1  
[2023-03-25 01:22:44] local.DEBUG: print  2  
[2023-03-25 01:23:25] local.INFO: header  
[2023-03-25 01:23:25] local.CRITICAL: ****************************1  
[2023-03-25 01:23:25] local.ALERT: reach here  
[2023-03-25 01:23:25] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '2400.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ألفين وأربعمائة  ر.ي.',
  ),
)  
[2023-03-25 01:23:25] local.ERROR: المبلغ  
[2023-03-25 01:23:25] local.ERROR: 2,400.00  
[2023-03-25 01:23:25] local.ERROR: مبلغ وقدرة  
[2023-03-25 01:23:25] local.ALERT: 200  
[2023-03-25 01:23:25] local.ALERT: 3  
[2023-03-25 01:23:25] local.ALERT: 200  
[2023-03-25 01:23:25] local.ALERT: 3  
[2023-03-25 01:23:25] local.ALERT: 200  
[2023-03-25 01:23:25] local.ALERT: 3  
[2023-03-25 01:23:25] local.ALERT: 200  
[2023-03-25 01:23:25] local.ALERT: 3  
[2023-03-25 01:23:25] local.ALERT: 200  
[2023-03-25 01:23:25] local.ALERT: 3  
[2023-03-25 01:23:25] local.ALERT: 200  
[2023-03-25 01:23:25] local.ALERT: 3  
[2023-03-25 01:23:25] local.ALERT: 200  
[2023-03-25 01:23:25] local.ALERT: 3  
[2023-03-25 01:23:25] local.ALERT: 200  
[2023-03-25 01:23:25] local.ALERT: 3  
[2023-03-25 01:23:25] local.ALERT: 200  
[2023-03-25 01:23:25] local.ALERT: 3  
[2023-03-25 01:23:25] local.ALERT: 200  
[2023-03-25 01:23:25] local.ALERT: 2  
[2023-03-25 01:23:25] local.ALERT: 200  
[2023-03-25 01:23:25] local.ALERT: 2  
[2023-03-25 01:23:25] local.ALERT: 200  
[2023-03-25 01:23:25] local.ALERT: 2  
[2023-03-25 01:23:25] local.ALERT: 200  
[2023-03-25 01:23:25] local.ALERT: 2  
[2023-03-25 01:23:25] local.ALERT: 200  
[2023-03-25 01:23:25] local.ALERT: 2  
[2023-03-25 01:23:25] local.ALERT: 200  
[2023-03-25 01:23:25] local.ALERT: 2  
[2023-03-25 01:23:25] local.ALERT: 200  
[2023-03-25 01:23:25] local.ALERT: 2  
[2023-03-25 01:23:25] local.ALERT: 200  
[2023-03-25 01:23:25] local.ALERT: 1  
[2023-03-25 01:23:25] local.ALERT: 200  
[2023-03-25 01:23:25] local.ALERT: 1  
[2023-03-25 01:23:25] local.ALERT: 200  
[2023-03-25 01:23:25] local.ALERT: 1  
[2023-03-25 01:23:25] local.ALERT: 200  
[2023-03-25 01:23:25] local.ALERT: 1  
[2023-03-25 01:23:25] local.ALERT: 200  
[2023-03-25 01:23:25] local.ALERT: 1  
[2023-03-25 01:23:25] local.ALERT: 200  
[2023-03-25 01:23:25] local.ALERT: 2  
[2023-03-25 01:23:25] local.ALERT: 200  
[2023-03-25 01:23:25] local.ALERT: 10013  
[2023-03-25 01:23:25] local.ALERT: 200  
[2023-03-25 01:23:25] local.ALERT: 10013  
[2023-03-25 01:23:25] local.ALERT: 200  
[2023-03-25 01:23:25] local.ALERT: 10013  
[2023-03-25 01:23:25] local.ALERT: 200  
[2023-03-25 01:23:25] local.ALERT: 10013  
[2023-03-25 01:23:25] local.ALERT: 200  
[2023-03-25 01:23:25] local.ALERT: 1  
[2023-03-25 01:23:25] local.ALERT: 200  
[2023-03-25 01:23:25] local.ALERT: 3  
[2023-03-25 01:23:25] local.ALERT: 200  
[2023-03-25 01:23:25] local.ALERT: 3  
[2023-03-25 01:23:25] local.ALERT: 200  
[2023-03-25 01:23:25] local.ALERT: 40  
[2023-03-25 01:23:25] local.ALERT: 200  
[2023-03-25 01:23:25] local.ALERT: 40  
[2023-03-25 01:23:25] local.ALERT: 200  
[2023-03-25 01:23:25] local.ALERT: 40  
[2023-03-25 01:23:25] local.ALERT: 200  
[2023-03-25 01:23:25] local.ALERT: 40  
[2023-03-25 01:23:25] local.WARNING: 1  
[2023-03-25 01:23:25] local.WARNING: array (
  'ID' => 85,
  'Name' => 'فئة 15 جيجا 2400 ريال',
  'ServiceID' => 200,
  'Price' => 2400.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 2400.0,
)  
[2023-03-25 01:23:25] local.ALERT: 200  
[2023-03-25 01:23:25] local.ALERT: 200  
[2023-03-25 01:23:25] local.ALERT: 200  
[2023-03-25 01:23:25] local.ALERT: 200  
[2023-03-25 01:23:25] local.ALERT: 200  
[2023-03-25 01:23:25] local.ALERT: 200  
[2023-03-25 01:23:25] local.ALERT: 200  
[2023-03-25 01:23:25] local.ALERT: 200  
[2023-03-25 01:23:25] local.ALERT: 200  
[2023-03-25 01:23:25] local.ALERT: 200  
[2023-03-25 01:23:25] local.ALERT: 200  
[2023-03-25 01:23:25] local.ALERT: 200  
[2023-03-25 01:23:25] local.ALERT: 200  
[2023-03-25 01:23:25] local.ALERT: 40  
[2023-03-25 01:23:25] local.CRITICAL: ****************************2  
[2023-03-25 01:23:25] local.CRITICAL: ****************************  
[2023-03-25 01:23:25] local.CRITICAL:   
[2023-03-25 01:23:25] local.CRITICAL: ****************************  
[2023-03-25 01:23:27] local.INFO: {
  "ClientBalanceResult": "54008.3750"
}  
[2023-03-25 01:23:27] local.INFO: array (
  'ClientBalanceResult' => '54008.3750',
)  
[2023-03-25 01:23:27] local.DEBUG: lattttef  
[2023-03-25 01:23:27] local.DEBUG: array (
  'ClientBalanceResult' => '54008.3750',
)  
[2023-03-25 01:23:27] local.INFO: transaction14  
[2023-03-25 01:23:27] local.INFO: first inquery phone = 107666946  
[2023-03-25 01:23:30] local.DEBUG: response querySubBalance  
[2023-03-25 01:23:30] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#8,000.00#8,000.00#9.85 MB#13-04-2023#0#0##4183.00#4G 60</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-25 01:23:30] local.DEBUG: array (
  0 => 'OK',
  1 => '8,000.00',
  2 => '8,000.00',
  3 => '9.85 MB',
  4 => '13-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '4183.00',
  9 => '4G 60',
)  
[2023-03-25 01:23:30] local.DEBUG: print  before faction by provider price  
[2023-03-25 01:23:30] local.DEBUG: print  after faction by provider price  
[2023-03-25 01:23:30] local.DEBUG: فئة 60 جيجا 8000 ريال  
[2023-03-25 01:23:30] local.DEBUG: print1  
[2023-03-25 01:23:30] local.DEBUG: print  2  
[2023-03-25 01:23:50] local.INFO: header  
[2023-03-25 01:23:50] local.INFO: header after fliter  
[2023-03-25 01:23:50] local.INFO: Body  after fliter  
[2023-03-25 01:23:50] local.INFO: array (
)  
[2023-03-25 01:23:50] local.INFO: transaction14  
[2023-03-25 01:23:50] local.INFO: first inquery phone = 106400976  
[2023-03-25 01:23:54] local.DEBUG: response querySubBalance  
[2023-03-25 01:23:54] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#57.31 GB#15-04-2023#0#0##10501.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-25 01:23:54] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '57.31 GB',
  4 => '15-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '10501.00',
  9 => '4G 250',
)  
[2023-03-25 01:23:54] local.DEBUG: print  before faction by provider price  
[2023-03-25 01:23:54] local.DEBUG: print  after faction by provider price  
[2023-03-25 01:23:54] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-25 01:23:54] local.DEBUG: print1  
[2023-03-25 01:23:54] local.DEBUG: print  2  
[2023-03-25 01:24:15] local.INFO: header  
[2023-03-25 01:24:15] local.INFO: header after fliter  
[2023-03-25 01:24:15] local.INFO: Body  after fliter  
[2023-03-25 01:24:15] local.INFO: array (
)  
[2023-03-25 01:24:15] local.INFO: transaction14  
[2023-03-25 01:24:15] local.INFO: first inquery phone = 106400860  
[2023-03-25 01:24:18] local.DEBUG: response querySubBalance  
[2023-03-25 01:24:18] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#6.65 GB#24-04-2023#0#0##.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-25 01:24:18] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '6.65 GB',
  4 => '24-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 15',
)  
[2023-03-25 01:24:18] local.DEBUG: print  before faction by provider price  
[2023-03-25 01:24:19] local.DEBUG: print  after faction by provider price  
[2023-03-25 01:24:19] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-25 01:24:19] local.DEBUG: print1  
[2023-03-25 01:24:19] local.DEBUG: print  2  
[2023-03-25 01:24:47] local.INFO: header  
[2023-03-25 01:24:47] local.INFO: header after fliter  
[2023-03-25 01:24:47] local.INFO: Body  after fliter  
[2023-03-25 01:24:47] local.INFO: array (
)  
[2023-03-25 01:24:47] local.INFO: transaction14  
[2023-03-25 01:24:47] local.INFO: first inquery phone = 106400860  
[2023-03-25 01:24:50] local.DEBUG: response querySubBalance  
[2023-03-25 01:24:50] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#6.65 GB#24-04-2023#0#0##.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-25 01:24:50] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '6.65 GB',
  4 => '24-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 15',
)  
[2023-03-25 01:24:50] local.DEBUG: print  before faction by provider price  
[2023-03-25 01:24:50] local.DEBUG: print  after faction by provider price  
[2023-03-25 01:24:50] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-25 01:24:50] local.DEBUG: print1  
[2023-03-25 01:24:50] local.DEBUG: print  2  
[2023-03-25 01:25:21] local.INFO: header  
[2023-03-25 01:25:22] local.INFO: header after fliter  
[2023-03-25 01:25:22] local.INFO: Body  after fliter  
[2023-03-25 01:25:22] local.INFO: array (
)  
[2023-03-25 01:25:22] local.INFO: transaction14  
[2023-03-25 01:25:22] local.INFO: first inquery phone = 106400860  
[2023-03-25 01:25:25] local.DEBUG: response querySubBalance  
[2023-03-25 01:25:25] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#6.61 GB#24-04-2023#0#0##.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-25 01:25:25] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '6.61 GB',
  4 => '24-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 15',
)  
[2023-03-25 01:25:25] local.DEBUG: print  before faction by provider price  
[2023-03-25 01:25:25] local.DEBUG: print  after faction by provider price  
[2023-03-25 01:25:25] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-25 01:25:25] local.DEBUG: print1  
[2023-03-25 01:25:25] local.DEBUG: print  2  
[2023-03-25 01:25:57] local.INFO: header  
[2023-03-25 01:25:57] local.INFO: header after fliter  
[2023-03-25 01:25:57] local.INFO: Body  after fliter  
[2023-03-25 01:25:57] local.INFO: array (
)  
[2023-03-25 01:25:57] local.INFO: transaction14  
[2023-03-25 01:25:57] local.INFO: first inquery phone = 106400860  
[2023-03-25 01:26:01] local.DEBUG: response querySubBalance  
[2023-03-25 01:26:01] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#6.61 GB#24-04-2023#0#0##.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-25 01:26:01] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '6.61 GB',
  4 => '24-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 15',
)  
[2023-03-25 01:26:01] local.DEBUG: print  before faction by provider price  
[2023-03-25 01:26:01] local.DEBUG: print  after faction by provider price  
[2023-03-25 01:26:01] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-25 01:26:01] local.DEBUG: print1  
[2023-03-25 01:26:01] local.DEBUG: print  2  
[2023-03-25 02:18:27] local.INFO: header  
[2023-03-25 02:18:27] local.INFO: header after fliter  
[2023-03-25 02:18:27] local.INFO: Body  after fliter  
[2023-03-25 02:18:27] local.INFO: array (
)  
[2023-03-25 02:18:27] local.INFO: transaction14  
[2023-03-25 02:18:27] local.INFO: first inquery phone = 106488408  
[2023-03-25 02:18:36] local.DEBUG: response querySubBalance  
[2023-03-25 02:18:36] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#3.04 GB#21-04-2023#0#0##14500.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-25 02:18:36] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '3.04 GB',
  4 => '21-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '14500.00',
  9 => '4G 25',
)  
[2023-03-25 02:18:36] local.DEBUG: print  before faction by provider price  
[2023-03-25 02:18:36] local.DEBUG: print  after faction by provider price  
[2023-03-25 02:18:36] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-25 02:18:36] local.DEBUG: print1  
[2023-03-25 02:18:36] local.DEBUG: print  2  
[2023-03-25 03:48:18] local.INFO: header  
[2023-03-25 03:48:19] local.INFO: header after fliter  
[2023-03-25 03:48:19] local.INFO: Body  after fliter  
[2023-03-25 03:48:19] local.INFO: array (
)  
[2023-03-25 03:48:19] local.INFO: transaction14  
[2023-03-25 03:48:19] local.INFO: first inquery phone = 106323109  
[2023-03-25 03:48:22] local.DEBUG: response querySubBalance  
[2023-03-25 03:48:22] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#8,000.00#8,000.00#43.64 GB#15-04-2023#0#0##2500.00#4G 60</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-25 03:48:22] local.DEBUG: array (
  0 => 'OK',
  1 => '8,000.00',
  2 => '8,000.00',
  3 => '43.64 GB',
  4 => '15-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '2500.00',
  9 => '4G 60',
)  
[2023-03-25 03:48:22] local.DEBUG: print  before faction by provider price  
[2023-03-25 03:48:22] local.DEBUG: print  after faction by provider price  
[2023-03-25 03:48:22] local.DEBUG: فئة 60 جيجا 8000 ريال  
[2023-03-25 03:48:22] local.DEBUG: print1  
[2023-03-25 03:48:22] local.DEBUG: print  2  
[2023-03-25 05:36:35] local.INFO: header  
[2023-03-25 05:36:35] local.INFO: header after fliter  
[2023-03-25 05:36:35] local.INFO: Body  after fliter  
[2023-03-25 05:36:35] local.INFO: array (
)  
[2023-03-25 05:36:35] local.INFO: transaction14  
[2023-03-25 05:36:35] local.INFO: first inquery phone = 101034508  
[2023-03-25 05:36:38] local.DEBUG: response querySubBalance  
[2023-03-25 05:36:38] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#26.06 GB#11-04-2023#0#0##1500.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-25 05:36:38] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '26.06 GB',
  4 => '11-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '1500.00',
  9 => '4G 25',
)  
[2023-03-25 05:36:38] local.DEBUG: print  before faction by provider price  
[2023-03-25 05:36:38] local.DEBUG: print  after faction by provider price  
[2023-03-25 05:36:38] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-25 05:36:38] local.DEBUG: print1  
[2023-03-25 05:36:38] local.DEBUG: print  2  
[2023-03-25 21:52:51] local.INFO: header  
[2023-03-25 21:52:52] local.CRITICAL: ****************************1  
[2023-03-25 21:52:52] local.ALERT: reach here  
[2023-03-25 21:52:53] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '4000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'أربعة ألف  ر.ي.',
  ),
)  
[2023-03-25 21:52:53] local.ERROR: المبلغ  
[2023-03-25 21:52:53] local.ERROR: 4,000.00  
[2023-03-25 21:52:53] local.ERROR: مبلغ وقدرة  
[2023-03-25 21:52:53] local.ALERT: 200  
[2023-03-25 21:52:53] local.ALERT: 3  
[2023-03-25 21:52:53] local.ALERT: 200  
[2023-03-25 21:52:53] local.ALERT: 3  
[2023-03-25 21:52:53] local.ALERT: 200  
[2023-03-25 21:52:53] local.ALERT: 3  
[2023-03-25 21:52:53] local.ALERT: 200  
[2023-03-25 21:52:53] local.ALERT: 3  
[2023-03-25 21:52:53] local.ALERT: 200  
[2023-03-25 21:52:53] local.ALERT: 3  
[2023-03-25 21:52:53] local.ALERT: 200  
[2023-03-25 21:52:53] local.ALERT: 3  
[2023-03-25 21:52:53] local.ALERT: 200  
[2023-03-25 21:52:53] local.ALERT: 3  
[2023-03-25 21:52:53] local.ALERT: 200  
[2023-03-25 21:52:53] local.ALERT: 3  
[2023-03-25 21:52:53] local.ALERT: 200  
[2023-03-25 21:52:53] local.ALERT: 3  
[2023-03-25 21:52:53] local.ALERT: 200  
[2023-03-25 21:52:53] local.ALERT: 2  
[2023-03-25 21:52:53] local.ALERT: 200  
[2023-03-25 21:52:53] local.ALERT: 2  
[2023-03-25 21:52:53] local.ALERT: 200  
[2023-03-25 21:52:53] local.ALERT: 2  
[2023-03-25 21:52:53] local.ALERT: 200  
[2023-03-25 21:52:53] local.ALERT: 2  
[2023-03-25 21:52:53] local.ALERT: 200  
[2023-03-25 21:52:53] local.ALERT: 2  
[2023-03-25 21:52:53] local.ALERT: 200  
[2023-03-25 21:52:53] local.ALERT: 2  
[2023-03-25 21:52:53] local.ALERT: 200  
[2023-03-25 21:52:53] local.ALERT: 2  
[2023-03-25 21:52:53] local.ALERT: 200  
[2023-03-25 21:52:53] local.ALERT: 1  
[2023-03-25 21:52:53] local.ALERT: 200  
[2023-03-25 21:52:53] local.ALERT: 1  
[2023-03-25 21:52:53] local.ALERT: 200  
[2023-03-25 21:52:53] local.ALERT: 1  
[2023-03-25 21:52:53] local.ALERT: 200  
[2023-03-25 21:52:53] local.ALERT: 1  
[2023-03-25 21:52:53] local.ALERT: 200  
[2023-03-25 21:52:53] local.ALERT: 1  
[2023-03-25 21:52:53] local.ALERT: 200  
[2023-03-25 21:52:53] local.ALERT: 2  
[2023-03-25 21:52:53] local.ALERT: 200  
[2023-03-25 21:52:53] local.ALERT: 10013  
[2023-03-25 21:52:53] local.ALERT: 200  
[2023-03-25 21:52:53] local.ALERT: 10013  
[2023-03-25 21:52:53] local.ALERT: 200  
[2023-03-25 21:52:53] local.ALERT: 10013  
[2023-03-25 21:52:53] local.ALERT: 200  
[2023-03-25 21:52:53] local.ALERT: 10013  
[2023-03-25 21:52:53] local.ALERT: 200  
[2023-03-25 21:52:53] local.ALERT: 1  
[2023-03-25 21:52:53] local.ALERT: 200  
[2023-03-25 21:52:53] local.ALERT: 3  
[2023-03-25 21:52:53] local.ALERT: 200  
[2023-03-25 21:52:53] local.ALERT: 3  
[2023-03-25 21:52:53] local.ALERT: 200  
[2023-03-25 21:52:53] local.ALERT: 40  
[2023-03-25 21:52:53] local.ALERT: 200  
[2023-03-25 21:52:53] local.ALERT: 40  
[2023-03-25 21:52:53] local.ALERT: 200  
[2023-03-25 21:52:53] local.ALERT: 40  
[2023-03-25 21:52:53] local.ALERT: 200  
[2023-03-25 21:52:53] local.ALERT: 40  
[2023-03-25 21:52:53] local.ALERT: 200  
[2023-03-25 21:52:53] local.ALERT: 200  
[2023-03-25 21:52:53] local.WARNING: 1  
[2023-03-25 21:52:53] local.WARNING: array (
  'ID' => 86,
  'Name' => 'فئة 25 جيجا 4000 ريال',
  'ServiceID' => 200,
  'Price' => 4000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 4000.0,
)  
[2023-03-25 21:52:53] local.ALERT: 200  
[2023-03-25 21:52:53] local.ALERT: 200  
[2023-03-25 21:52:53] local.ALERT: 200  
[2023-03-25 21:52:53] local.ALERT: 200  
[2023-03-25 21:52:53] local.ALERT: 200  
[2023-03-25 21:52:53] local.ALERT: 200  
[2023-03-25 21:52:53] local.ALERT: 200  
[2023-03-25 21:52:53] local.ALERT: 200  
[2023-03-25 21:52:53] local.ALERT: 200  
[2023-03-25 21:52:53] local.ALERT: 200  
[2023-03-25 21:52:53] local.ALERT: 200  
[2023-03-25 21:52:53] local.ALERT: 40  
[2023-03-25 21:52:53] local.CRITICAL: ****************************2  
[2023-03-25 21:52:53] local.CRITICAL: ****************************  
[2023-03-25 21:52:53] local.CRITICAL:   
[2023-03-25 21:52:53] local.CRITICAL: ****************************  
[2023-03-25 21:52:53] local.INFO: {
  "ClientBalanceResult": "2536.7850"
}  
[2023-03-25 21:52:54] local.INFO: array (
  'ClientBalanceResult' => '2536.7850',
)  
[2023-03-25 21:52:54] local.INFO: price less than Balance  
[2023-03-25 21:53:06] local.INFO: header  
[2023-03-25 21:53:06] local.CRITICAL: ****************************1  
[2023-03-25 21:53:06] local.ALERT: reach here  
[2023-03-25 21:53:06] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '4000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'أربعة ألف  ر.ي.',
  ),
)  
[2023-03-25 21:53:06] local.ERROR: المبلغ  
[2023-03-25 21:53:06] local.ERROR: 4,000.00  
[2023-03-25 21:53:06] local.ERROR: مبلغ وقدرة  
[2023-03-25 21:53:06] local.ALERT: 200  
[2023-03-25 21:53:06] local.ALERT: 3  
[2023-03-25 21:53:06] local.ALERT: 200  
[2023-03-25 21:53:06] local.ALERT: 3  
[2023-03-25 21:53:06] local.ALERT: 200  
[2023-03-25 21:53:06] local.ALERT: 3  
[2023-03-25 21:53:06] local.ALERT: 200  
[2023-03-25 21:53:06] local.ALERT: 3  
[2023-03-25 21:53:06] local.ALERT: 200  
[2023-03-25 21:53:06] local.ALERT: 3  
[2023-03-25 21:53:06] local.ALERT: 200  
[2023-03-25 21:53:06] local.ALERT: 3  
[2023-03-25 21:53:06] local.ALERT: 200  
[2023-03-25 21:53:06] local.ALERT: 3  
[2023-03-25 21:53:06] local.ALERT: 200  
[2023-03-25 21:53:06] local.ALERT: 3  
[2023-03-25 21:53:06] local.ALERT: 200  
[2023-03-25 21:53:06] local.ALERT: 3  
[2023-03-25 21:53:06] local.ALERT: 200  
[2023-03-25 21:53:06] local.ALERT: 2  
[2023-03-25 21:53:06] local.ALERT: 200  
[2023-03-25 21:53:06] local.ALERT: 2  
[2023-03-25 21:53:06] local.ALERT: 200  
[2023-03-25 21:53:06] local.ALERT: 2  
[2023-03-25 21:53:06] local.ALERT: 200  
[2023-03-25 21:53:06] local.ALERT: 2  
[2023-03-25 21:53:06] local.ALERT: 200  
[2023-03-25 21:53:06] local.ALERT: 2  
[2023-03-25 21:53:06] local.ALERT: 200  
[2023-03-25 21:53:06] local.ALERT: 2  
[2023-03-25 21:53:06] local.ALERT: 200  
[2023-03-25 21:53:06] local.ALERT: 2  
[2023-03-25 21:53:06] local.ALERT: 200  
[2023-03-25 21:53:06] local.ALERT: 1  
[2023-03-25 21:53:06] local.ALERT: 200  
[2023-03-25 21:53:06] local.ALERT: 1  
[2023-03-25 21:53:06] local.ALERT: 200  
[2023-03-25 21:53:06] local.ALERT: 1  
[2023-03-25 21:53:06] local.ALERT: 200  
[2023-03-25 21:53:06] local.ALERT: 1  
[2023-03-25 21:53:06] local.ALERT: 200  
[2023-03-25 21:53:06] local.ALERT: 1  
[2023-03-25 21:53:06] local.ALERT: 200  
[2023-03-25 21:53:06] local.ALERT: 2  
[2023-03-25 21:53:06] local.ALERT: 200  
[2023-03-25 21:53:06] local.ALERT: 10013  
[2023-03-25 21:53:06] local.ALERT: 200  
[2023-03-25 21:53:06] local.ALERT: 10013  
[2023-03-25 21:53:06] local.ALERT: 200  
[2023-03-25 21:53:06] local.ALERT: 10013  
[2023-03-25 21:53:06] local.ALERT: 200  
[2023-03-25 21:53:06] local.ALERT: 10013  
[2023-03-25 21:53:06] local.ALERT: 200  
[2023-03-25 21:53:06] local.ALERT: 1  
[2023-03-25 21:53:06] local.ALERT: 200  
[2023-03-25 21:53:06] local.ALERT: 3  
[2023-03-25 21:53:06] local.ALERT: 200  
[2023-03-25 21:53:06] local.ALERT: 3  
[2023-03-25 21:53:06] local.ALERT: 200  
[2023-03-25 21:53:06] local.ALERT: 40  
[2023-03-25 21:53:06] local.ALERT: 200  
[2023-03-25 21:53:06] local.ALERT: 40  
[2023-03-25 21:53:06] local.ALERT: 200  
[2023-03-25 21:53:06] local.ALERT: 40  
[2023-03-25 21:53:06] local.ALERT: 200  
[2023-03-25 21:53:06] local.ALERT: 40  
[2023-03-25 21:53:06] local.ALERT: 200  
[2023-03-25 21:53:06] local.ALERT: 200  
[2023-03-25 21:53:06] local.WARNING: 1  
[2023-03-25 21:53:06] local.WARNING: array (
  'ID' => 86,
  'Name' => 'فئة 25 جيجا 4000 ريال',
  'ServiceID' => 200,
  'Price' => 4000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 4000.0,
)  
[2023-03-25 21:53:06] local.ALERT: 200  
[2023-03-25 21:53:06] local.ALERT: 200  
[2023-03-25 21:53:06] local.ALERT: 200  
[2023-03-25 21:53:06] local.ALERT: 200  
[2023-03-25 21:53:06] local.ALERT: 200  
[2023-03-25 21:53:06] local.ALERT: 200  
[2023-03-25 21:53:06] local.ALERT: 200  
[2023-03-25 21:53:06] local.ALERT: 200  
[2023-03-25 21:53:06] local.ALERT: 200  
[2023-03-25 21:53:06] local.ALERT: 200  
[2023-03-25 21:53:06] local.ALERT: 200  
[2023-03-25 21:53:06] local.ALERT: 40  
[2023-03-25 21:53:06] local.CRITICAL: ****************************2  
[2023-03-25 21:53:06] local.CRITICAL: ****************************  
[2023-03-25 21:53:06] local.CRITICAL:   
[2023-03-25 21:53:06] local.CRITICAL: ****************************  
[2023-03-25 21:53:06] local.INFO: {
  "ClientBalanceResult": "2536.7850"
}  
[2023-03-25 21:53:06] local.INFO: array (
  'ClientBalanceResult' => '2536.7850',
)  
[2023-03-25 21:53:07] local.INFO: price less than Balance  
[2023-03-25 21:53:12] local.INFO: header  
[2023-03-25 21:53:12] local.INFO: header after fliter  
[2023-03-25 21:53:12] local.INFO: Body  after fliter  
[2023-03-25 21:53:12] local.INFO: array (
)  
[2023-03-25 21:53:12] local.INFO: transaction14  
[2023-03-25 21:53:12] local.INFO: first inquery phone = 103366823  
[2023-03-25 21:53:17] local.DEBUG: response querySubBalance  
[2023-03-25 21:53:17] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#0.00 B#13-04-2023#0#0##2500.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-25 21:53:17] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '0.00 B',
  4 => '13-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '2500.00',
  9 => '4G 25',
)  
[2023-03-25 21:53:17] local.DEBUG: print  before faction by provider price  
[2023-03-25 21:53:17] local.DEBUG: print  after faction by provider price  
[2023-03-25 21:53:17] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-25 21:53:17] local.DEBUG: print1  
[2023-03-25 21:53:17] local.DEBUG: print  2  
[2023-03-25 21:58:25] local.INFO: header  
[2023-03-25 21:58:25] local.INFO: header after fliter  
[2023-03-25 21:58:25] local.INFO: Body  after fliter  
[2023-03-25 21:58:25] local.INFO: array (
)  
[2023-03-25 21:58:25] local.INFO: transaction14  
[2023-03-25 21:58:25] local.INFO: first inquery phone = 103377914  
[2023-03-25 21:58:29] local.DEBUG: response querySubBalance  
[2023-03-25 21:58:29] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#9.54 GB#28-03-2023#0#0##500.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-25 21:58:29] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '9.54 GB',
  4 => '28-03-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '500.00',
  9 => '4G 25',
)  
[2023-03-25 21:58:29] local.DEBUG: print  before faction by provider price  
[2023-03-25 21:58:29] local.DEBUG: print  after faction by provider price  
[2023-03-25 21:58:29] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-25 21:58:29] local.DEBUG: print1  
[2023-03-25 21:58:29] local.DEBUG: print  2  
[2023-03-25 21:59:14] local.INFO: header  
[2023-03-25 21:59:14] local.INFO: header after fliter  
[2023-03-25 21:59:14] local.INFO: Body  after fliter  
[2023-03-25 21:59:14] local.INFO: array (
)  
[2023-03-25 21:59:14] local.INFO: transaction14  
[2023-03-25 21:59:14] local.INFO: first inquery phone = 103377912  
[2023-03-25 21:59:18] local.DEBUG: response querySubBalance  
[2023-03-25 21:59:18] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#5.24 GB#02-04-2023#0#0##88.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-25 21:59:18] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '5.24 GB',
  4 => '02-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '88.00',
  9 => '4G 25',
)  
[2023-03-25 21:59:18] local.DEBUG: print  before faction by provider price  
[2023-03-25 21:59:18] local.DEBUG: print  after faction by provider price  
[2023-03-25 21:59:18] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-25 21:59:18] local.DEBUG: print1  
[2023-03-25 21:59:18] local.DEBUG: print  2  
[2023-03-25 21:59:56] local.INFO: header  
[2023-03-25 21:59:56] local.INFO: header after fliter  
[2023-03-25 21:59:56] local.INFO: Body  after fliter  
[2023-03-25 21:59:56] local.INFO: array (
)  
[2023-03-25 21:59:56] local.INFO: transaction14  
[2023-03-25 21:59:56] local.INFO: first inquery phone = 103377902  
[2023-03-25 22:00:00] local.DEBUG: response querySubBalance  
[2023-03-25 22:00:00] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00##14-03-2023#0#0##.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-25 22:00:00] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '',
  4 => '14-03-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 15',
)  
[2023-03-25 22:00:00] local.DEBUG: print  before faction by provider price  
[2023-03-25 22:00:00] local.DEBUG: print  after faction by provider price  
[2023-03-25 22:00:00] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-25 22:00:00] local.DEBUG: print1  
[2023-03-25 22:00:00] local.DEBUG: print  2  
[2023-03-25 22:00:41] local.INFO: header  
[2023-03-25 22:00:41] local.CRITICAL: ****************************1  
[2023-03-25 22:00:41] local.ALERT: reach here  
[2023-03-25 22:00:41] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '4000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'أربعة ألف  ر.ي.',
  ),
)  
[2023-03-25 22:00:41] local.ERROR: المبلغ  
[2023-03-25 22:00:41] local.ERROR: 4,000.00  
[2023-03-25 22:00:41] local.ERROR: مبلغ وقدرة  
[2023-03-25 22:00:41] local.ALERT: 200  
[2023-03-25 22:00:41] local.ALERT: 3  
[2023-03-25 22:00:41] local.ALERT: 200  
[2023-03-25 22:00:41] local.ALERT: 3  
[2023-03-25 22:00:41] local.ALERT: 200  
[2023-03-25 22:00:41] local.ALERT: 3  
[2023-03-25 22:00:41] local.ALERT: 200  
[2023-03-25 22:00:41] local.ALERT: 3  
[2023-03-25 22:00:41] local.ALERT: 200  
[2023-03-25 22:00:41] local.ALERT: 3  
[2023-03-25 22:00:41] local.ALERT: 200  
[2023-03-25 22:00:41] local.ALERT: 3  
[2023-03-25 22:00:41] local.ALERT: 200  
[2023-03-25 22:00:41] local.ALERT: 3  
[2023-03-25 22:00:41] local.ALERT: 200  
[2023-03-25 22:00:41] local.ALERT: 3  
[2023-03-25 22:00:41] local.ALERT: 200  
[2023-03-25 22:00:41] local.ALERT: 3  
[2023-03-25 22:00:41] local.ALERT: 200  
[2023-03-25 22:00:41] local.ALERT: 2  
[2023-03-25 22:00:41] local.ALERT: 200  
[2023-03-25 22:00:41] local.ALERT: 2  
[2023-03-25 22:00:41] local.ALERT: 200  
[2023-03-25 22:00:41] local.ALERT: 2  
[2023-03-25 22:00:41] local.ALERT: 200  
[2023-03-25 22:00:41] local.ALERT: 2  
[2023-03-25 22:00:41] local.ALERT: 200  
[2023-03-25 22:00:41] local.ALERT: 2  
[2023-03-25 22:00:41] local.ALERT: 200  
[2023-03-25 22:00:41] local.ALERT: 2  
[2023-03-25 22:00:41] local.ALERT: 200  
[2023-03-25 22:00:41] local.ALERT: 2  
[2023-03-25 22:00:41] local.ALERT: 200  
[2023-03-25 22:00:41] local.ALERT: 1  
[2023-03-25 22:00:41] local.ALERT: 200  
[2023-03-25 22:00:41] local.ALERT: 1  
[2023-03-25 22:00:41] local.ALERT: 200  
[2023-03-25 22:00:41] local.ALERT: 1  
[2023-03-25 22:00:41] local.ALERT: 200  
[2023-03-25 22:00:41] local.ALERT: 1  
[2023-03-25 22:00:41] local.ALERT: 200  
[2023-03-25 22:00:41] local.ALERT: 1  
[2023-03-25 22:00:41] local.ALERT: 200  
[2023-03-25 22:00:41] local.ALERT: 2  
[2023-03-25 22:00:41] local.ALERT: 200  
[2023-03-25 22:00:41] local.ALERT: 10013  
[2023-03-25 22:00:41] local.ALERT: 200  
[2023-03-25 22:00:41] local.ALERT: 10013  
[2023-03-25 22:00:41] local.ALERT: 200  
[2023-03-25 22:00:41] local.ALERT: 10013  
[2023-03-25 22:00:41] local.ALERT: 200  
[2023-03-25 22:00:41] local.ALERT: 10013  
[2023-03-25 22:00:41] local.ALERT: 200  
[2023-03-25 22:00:41] local.ALERT: 1  
[2023-03-25 22:00:41] local.ALERT: 200  
[2023-03-25 22:00:41] local.ALERT: 3  
[2023-03-25 22:00:41] local.ALERT: 200  
[2023-03-25 22:00:41] local.ALERT: 3  
[2023-03-25 22:00:41] local.ALERT: 200  
[2023-03-25 22:00:41] local.ALERT: 40  
[2023-03-25 22:00:41] local.ALERT: 200  
[2023-03-25 22:00:41] local.ALERT: 40  
[2023-03-25 22:00:41] local.ALERT: 200  
[2023-03-25 22:00:41] local.ALERT: 40  
[2023-03-25 22:00:41] local.ALERT: 200  
[2023-03-25 22:00:41] local.ALERT: 40  
[2023-03-25 22:00:41] local.ALERT: 200  
[2023-03-25 22:00:41] local.ALERT: 200  
[2023-03-25 22:00:41] local.WARNING: 1  
[2023-03-25 22:00:41] local.WARNING: array (
  'ID' => 86,
  'Name' => 'فئة 25 جيجا 4000 ريال',
  'ServiceID' => 200,
  'Price' => 4000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 4000.0,
)  
[2023-03-25 22:00:41] local.ALERT: 200  
[2023-03-25 22:00:41] local.ALERT: 200  
[2023-03-25 22:00:41] local.ALERT: 200  
[2023-03-25 22:00:41] local.ALERT: 200  
[2023-03-25 22:00:41] local.ALERT: 200  
[2023-03-25 22:00:41] local.ALERT: 200  
[2023-03-25 22:00:41] local.ALERT: 200  
[2023-03-25 22:00:41] local.ALERT: 200  
[2023-03-25 22:00:41] local.ALERT: 200  
[2023-03-25 22:00:41] local.ALERT: 200  
[2023-03-25 22:00:41] local.ALERT: 200  
[2023-03-25 22:00:41] local.ALERT: 40  
[2023-03-25 22:00:41] local.CRITICAL: ****************************2  
[2023-03-25 22:00:41] local.CRITICAL: ****************************  
[2023-03-25 22:00:41] local.CRITICAL:   
[2023-03-25 22:00:41] local.CRITICAL: ****************************  
[2023-03-25 22:00:41] local.INFO: {
  "ClientBalanceResult": "12536.7850"
}  
[2023-03-25 22:00:41] local.INFO: array (
  'ClientBalanceResult' => '12536.7850',
)  
[2023-03-25 22:00:41] local.DEBUG: lattttef  
[2023-03-25 22:00:41] local.DEBUG: array (
  'ClientBalanceResult' => '12536.7850',
)  
[2023-03-25 22:00:41] local.INFO: transaction14  
[2023-03-25 22:00:41] local.INFO: first inquery phone = 103366823  
[2023-03-25 22:00:46] local.DEBUG: response querySubBalance  
[2023-03-25 22:00:46] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#0.00 B#13-04-2023#0#0##2500.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-25 22:00:46] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '0.00 B',
  4 => '13-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '2500.00',
  9 => '4G 25',
)  
[2023-03-25 22:00:46] local.DEBUG: print  before faction by provider price  
[2023-03-25 22:00:46] local.DEBUG: print  after faction by provider price  
[2023-03-25 22:00:46] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-25 22:00:46] local.DEBUG: print1  
[2023-03-25 22:00:46] local.DEBUG: print  2  
[2023-03-25 22:00:46] local.INFO: transaction1  
[2023-03-25 22:00:46] local.INFO: transaction2  
[2023-03-25 22:00:46] local.INFO: transaction3  
[2023-03-25 22:00:46] local.INFO: transaction4  
[2023-03-25 22:00:46] local.INFO: transaction4  
[2023-03-25 22:00:46] local.INFO: transaction5  
[2023-03-25 22:00:46] local.INFO: transaction6  
[2023-03-25 22:00:46] local.INFO: transaction7  
[2023-03-25 22:00:46] local.DEBUG: array (
  'AMT' => 4000.0,
  'CType' => 0,
  'FID' => 86,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '103366823',
  'State' => 0,
  'lateflog' => '585546',
)  
[2023-03-25 22:00:46] local.INFO: transaction8  
[2023-03-25 22:00:47] local.INFO: transaction9  
[2023-03-25 22:00:47] local.INFO: transaction10  
[2023-03-25 22:00:47] local.INFO: transaction11  
[2023-03-25 22:00:47] local.INFO: 12  
[2023-03-25 22:00:47] local.INFO: transaction13  
[2023-03-25 22:00:47] local.INFO: transaction14  
[2023-03-25 22:00:47] local.INFO: transaction19  
[2023-03-25 22:00:47] local.INFO: transaction15  
[2023-03-25 22:00:47] local.INFO: transaction16  
[2023-03-25 22:00:47] local.INFO: 98#103366823#4000.00#0  
[2023-03-25 22:00:58] local.INFO: transaction18  
[2023-03-25 22:00:58] local.INFO: array (
  0 => 'OK',
  1 => '9,658,867.83',
  2 => 'NONE',
  3 => '61116458',
  4 => '4,000.00',
)  
[2023-03-25 23:11:55] local.INFO: header  
[2023-03-25 23:11:55] local.CRITICAL: ****************************1  
[2023-03-25 23:11:55] local.ALERT: reach here  
[2023-03-25 23:11:55] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '4000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'أربعة ألف  ر.ي.',
  ),
)  
[2023-03-25 23:11:55] local.ERROR: المبلغ  
[2023-03-25 23:11:55] local.ERROR: 4,000.00  
[2023-03-25 23:11:55] local.ERROR: مبلغ وقدرة  
[2023-03-25 23:11:55] local.ALERT: 200  
[2023-03-25 23:11:55] local.ALERT: 3  
[2023-03-25 23:11:55] local.ALERT: 200  
[2023-03-25 23:11:55] local.ALERT: 3  
[2023-03-25 23:11:55] local.ALERT: 200  
[2023-03-25 23:11:55] local.ALERT: 3  
[2023-03-25 23:11:55] local.ALERT: 200  
[2023-03-25 23:11:55] local.ALERT: 3  
[2023-03-25 23:11:55] local.ALERT: 200  
[2023-03-25 23:11:55] local.ALERT: 3  
[2023-03-25 23:11:55] local.ALERT: 200  
[2023-03-25 23:11:55] local.ALERT: 3  
[2023-03-25 23:11:55] local.ALERT: 200  
[2023-03-25 23:11:55] local.ALERT: 3  
[2023-03-25 23:11:55] local.ALERT: 200  
[2023-03-25 23:11:55] local.ALERT: 3  
[2023-03-25 23:11:55] local.ALERT: 200  
[2023-03-25 23:11:55] local.ALERT: 3  
[2023-03-25 23:11:55] local.ALERT: 200  
[2023-03-25 23:11:55] local.ALERT: 2  
[2023-03-25 23:11:55] local.ALERT: 200  
[2023-03-25 23:11:55] local.ALERT: 2  
[2023-03-25 23:11:55] local.ALERT: 200  
[2023-03-25 23:11:55] local.ALERT: 2  
[2023-03-25 23:11:55] local.ALERT: 200  
[2023-03-25 23:11:55] local.ALERT: 2  
[2023-03-25 23:11:55] local.ALERT: 200  
[2023-03-25 23:11:55] local.ALERT: 2  
[2023-03-25 23:11:55] local.ALERT: 200  
[2023-03-25 23:11:55] local.ALERT: 2  
[2023-03-25 23:11:55] local.ALERT: 200  
[2023-03-25 23:11:55] local.ALERT: 2  
[2023-03-25 23:11:55] local.ALERT: 200  
[2023-03-25 23:11:55] local.ALERT: 1  
[2023-03-25 23:11:55] local.ALERT: 200  
[2023-03-25 23:11:55] local.ALERT: 1  
[2023-03-25 23:11:55] local.ALERT: 200  
[2023-03-25 23:11:55] local.ALERT: 1  
[2023-03-25 23:11:55] local.ALERT: 200  
[2023-03-25 23:11:55] local.ALERT: 1  
[2023-03-25 23:11:55] local.ALERT: 200  
[2023-03-25 23:11:55] local.ALERT: 1  
[2023-03-25 23:11:55] local.ALERT: 200  
[2023-03-25 23:11:55] local.ALERT: 2  
[2023-03-25 23:11:55] local.ALERT: 200  
[2023-03-25 23:11:55] local.ALERT: 10013  
[2023-03-25 23:11:55] local.ALERT: 200  
[2023-03-25 23:11:55] local.ALERT: 10013  
[2023-03-25 23:11:55] local.ALERT: 200  
[2023-03-25 23:11:55] local.ALERT: 10013  
[2023-03-25 23:11:55] local.ALERT: 200  
[2023-03-25 23:11:55] local.ALERT: 10013  
[2023-03-25 23:11:55] local.ALERT: 200  
[2023-03-25 23:11:55] local.ALERT: 1  
[2023-03-25 23:11:55] local.ALERT: 200  
[2023-03-25 23:11:55] local.ALERT: 3  
[2023-03-25 23:11:55] local.ALERT: 200  
[2023-03-25 23:11:55] local.ALERT: 3  
[2023-03-25 23:11:55] local.ALERT: 200  
[2023-03-25 23:11:55] local.ALERT: 40  
[2023-03-25 23:11:55] local.ALERT: 200  
[2023-03-25 23:11:55] local.ALERT: 40  
[2023-03-25 23:11:55] local.ALERT: 200  
[2023-03-25 23:11:55] local.ALERT: 40  
[2023-03-25 23:11:55] local.ALERT: 200  
[2023-03-25 23:11:55] local.ALERT: 40  
[2023-03-25 23:11:55] local.ALERT: 200  
[2023-03-25 23:11:55] local.ALERT: 200  
[2023-03-25 23:11:55] local.WARNING: 1  
[2023-03-25 23:11:55] local.WARNING: array (
  'ID' => 86,
  'Name' => 'فئة 25 جيجا 4000 ريال',
  'ServiceID' => 200,
  'Price' => 4000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 4000.0,
)  
[2023-03-25 23:11:55] local.ALERT: 200  
[2023-03-25 23:11:55] local.ALERT: 200  
[2023-03-25 23:11:55] local.ALERT: 200  
[2023-03-25 23:11:55] local.ALERT: 200  
[2023-03-25 23:11:55] local.ALERT: 200  
[2023-03-25 23:11:55] local.ALERT: 200  
[2023-03-25 23:11:55] local.ALERT: 200  
[2023-03-25 23:11:55] local.ALERT: 200  
[2023-03-25 23:11:55] local.ALERT: 200  
[2023-03-25 23:11:55] local.ALERT: 200  
[2023-03-25 23:11:55] local.ALERT: 200  
[2023-03-25 23:11:55] local.ALERT: 40  
[2023-03-25 23:11:55] local.CRITICAL: ****************************2  
[2023-03-25 23:11:55] local.CRITICAL: ****************************  
[2023-03-25 23:11:55] local.CRITICAL:   
[2023-03-25 23:11:55] local.CRITICAL: ****************************  
[2023-03-25 23:11:55] local.INFO: {
  "ClientBalanceResult": "4940.0000"
}  
[2023-03-25 23:11:56] local.INFO: array (
  'ClientBalanceResult' => '4940.0000',
)  
[2023-03-25 23:11:56] local.DEBUG: lattttef  
[2023-03-25 23:11:56] local.DEBUG: array (
  'ClientBalanceResult' => '4940.0000',
)  
[2023-03-25 23:11:56] local.INFO: transaction14  
[2023-03-25 23:11:56] local.INFO: first inquery phone = 106400412  
[2023-03-25 23:15:29] local.INFO: header  
[2023-03-25 23:15:29] local.CRITICAL: ****************************1  
[2023-03-25 23:15:29] local.ALERT: reach here  
[2023-03-25 23:15:29] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '4000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'أربعة ألف  ر.ي.',
  ),
)  
[2023-03-25 23:15:29] local.ERROR: المبلغ  
[2023-03-25 23:15:29] local.ERROR: 4,000.00  
[2023-03-25 23:15:29] local.ERROR: مبلغ وقدرة  
[2023-03-25 23:15:29] local.ALERT: 200  
[2023-03-25 23:15:29] local.ALERT: 3  
[2023-03-25 23:15:29] local.ALERT: 200  
[2023-03-25 23:15:29] local.ALERT: 3  
[2023-03-25 23:15:29] local.ALERT: 200  
[2023-03-25 23:15:29] local.ALERT: 3  
[2023-03-25 23:15:29] local.ALERT: 200  
[2023-03-25 23:15:29] local.ALERT: 3  
[2023-03-25 23:15:29] local.ALERT: 200  
[2023-03-25 23:15:29] local.ALERT: 3  
[2023-03-25 23:15:29] local.ALERT: 200  
[2023-03-25 23:15:29] local.ALERT: 3  
[2023-03-25 23:15:29] local.ALERT: 200  
[2023-03-25 23:15:29] local.ALERT: 3  
[2023-03-25 23:15:29] local.ALERT: 200  
[2023-03-25 23:15:29] local.ALERT: 3  
[2023-03-25 23:15:29] local.ALERT: 200  
[2023-03-25 23:15:29] local.ALERT: 3  
[2023-03-25 23:15:29] local.ALERT: 200  
[2023-03-25 23:15:29] local.ALERT: 2  
[2023-03-25 23:15:29] local.ALERT: 200  
[2023-03-25 23:15:29] local.ALERT: 2  
[2023-03-25 23:15:29] local.ALERT: 200  
[2023-03-25 23:15:29] local.ALERT: 2  
[2023-03-25 23:15:29] local.ALERT: 200  
[2023-03-25 23:15:29] local.ALERT: 2  
[2023-03-25 23:15:29] local.ALERT: 200  
[2023-03-25 23:15:29] local.ALERT: 2  
[2023-03-25 23:15:29] local.ALERT: 200  
[2023-03-25 23:15:29] local.ALERT: 2  
[2023-03-25 23:15:29] local.ALERT: 200  
[2023-03-25 23:15:29] local.ALERT: 2  
[2023-03-25 23:15:29] local.ALERT: 200  
[2023-03-25 23:15:29] local.ALERT: 1  
[2023-03-25 23:15:29] local.ALERT: 200  
[2023-03-25 23:15:29] local.ALERT: 1  
[2023-03-25 23:15:29] local.ALERT: 200  
[2023-03-25 23:15:29] local.ALERT: 1  
[2023-03-25 23:15:29] local.ALERT: 200  
[2023-03-25 23:15:29] local.ALERT: 1  
[2023-03-25 23:15:29] local.ALERT: 200  
[2023-03-25 23:15:29] local.ALERT: 1  
[2023-03-25 23:15:29] local.ALERT: 200  
[2023-03-25 23:15:29] local.ALERT: 2  
[2023-03-25 23:15:29] local.ALERT: 200  
[2023-03-25 23:15:29] local.ALERT: 10013  
[2023-03-25 23:15:29] local.ALERT: 200  
[2023-03-25 23:15:29] local.ALERT: 10013  
[2023-03-25 23:15:29] local.ALERT: 200  
[2023-03-25 23:15:29] local.ALERT: 10013  
[2023-03-25 23:15:29] local.ALERT: 200  
[2023-03-25 23:15:29] local.ALERT: 10013  
[2023-03-25 23:15:29] local.ALERT: 200  
[2023-03-25 23:15:29] local.ALERT: 1  
[2023-03-25 23:15:29] local.ALERT: 200  
[2023-03-25 23:15:29] local.ALERT: 3  
[2023-03-25 23:15:29] local.ALERT: 200  
[2023-03-25 23:15:29] local.ALERT: 3  
[2023-03-25 23:15:29] local.ALERT: 200  
[2023-03-25 23:15:29] local.ALERT: 40  
[2023-03-25 23:15:29] local.ALERT: 200  
[2023-03-25 23:15:29] local.ALERT: 40  
[2023-03-25 23:15:29] local.ALERT: 200  
[2023-03-25 23:15:29] local.ALERT: 40  
[2023-03-25 23:15:29] local.ALERT: 200  
[2023-03-25 23:15:29] local.ALERT: 40  
[2023-03-25 23:15:29] local.ALERT: 200  
[2023-03-25 23:15:29] local.ALERT: 200  
[2023-03-25 23:15:29] local.WARNING: 1  
[2023-03-25 23:15:29] local.WARNING: array (
  'ID' => 86,
  'Name' => 'فئة 25 جيجا 4000 ريال',
  'ServiceID' => 200,
  'Price' => 4000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 4000.0,
)  
[2023-03-25 23:15:29] local.ALERT: 200  
[2023-03-25 23:15:29] local.ALERT: 200  
[2023-03-25 23:15:29] local.ALERT: 200  
[2023-03-25 23:15:29] local.ALERT: 200  
[2023-03-25 23:15:29] local.ALERT: 200  
[2023-03-25 23:15:29] local.ALERT: 200  
[2023-03-25 23:15:29] local.ALERT: 200  
[2023-03-25 23:15:29] local.ALERT: 200  
[2023-03-25 23:15:29] local.ALERT: 200  
[2023-03-25 23:15:29] local.ALERT: 200  
[2023-03-25 23:15:29] local.ALERT: 200  
[2023-03-25 23:15:29] local.ALERT: 40  
[2023-03-25 23:15:29] local.CRITICAL: ****************************2  
[2023-03-25 23:15:29] local.CRITICAL: ****************************  
[2023-03-25 23:15:29] local.CRITICAL:   
[2023-03-25 23:15:29] local.CRITICAL: ****************************  
[2023-03-25 23:15:29] local.INFO: {
  "ClientBalanceResult": "4940.0000"
}  
[2023-03-25 23:15:29] local.INFO: array (
  'ClientBalanceResult' => '4940.0000',
)  
[2023-03-25 23:15:29] local.DEBUG: lattttef  
[2023-03-25 23:15:29] local.DEBUG: array (
  'ClientBalanceResult' => '4940.0000',
)  
[2023-03-25 23:15:29] local.INFO: transaction14  
[2023-03-25 23:15:29] local.INFO: first inquery phone = 106400412  
[2023-03-25 23:16:31] local.INFO: header  
[2023-03-25 23:16:31] local.INFO: header after fliter  
[2023-03-25 23:16:31] local.INFO: Body  after fliter  
[2023-03-25 23:16:31] local.INFO: array (
)  
[2023-03-25 23:16:31] local.INFO: transaction14  
[2023-03-25 23:16:31] local.INFO: first inquery phone = 103330532  
[2023-03-25 23:17:07] local.INFO: header  
[2023-03-25 23:17:07] local.INFO: header after fliter  
[2023-03-25 23:17:07] local.INFO: Body  after fliter  
[2023-03-25 23:17:07] local.INFO: array (
)  
[2023-03-25 23:17:07] local.INFO: transaction14  
[2023-03-25 23:17:07] local.INFO: first inquery phone = 103330532  
[2023-03-25 23:20:32] local.INFO: header  
[2023-03-25 23:20:33] local.INFO: header after fliter  
[2023-03-25 23:20:33] local.INFO: Body  after fliter  
[2023-03-25 23:20:33] local.INFO: array (
)  
[2023-03-25 23:20:33] local.INFO: transaction14  
[2023-03-25 23:20:33] local.INFO: first inquery phone = 106400412  
[2023-03-25 23:21:08] local.INFO: header  
[2023-03-25 23:21:08] local.INFO: header after fliter  
[2023-03-25 23:21:08] local.INFO: Body  after fliter  
[2023-03-25 23:21:08] local.INFO: array (
)  
[2023-03-25 23:21:08] local.INFO: transaction14  
[2023-03-25 23:21:08] local.INFO: first inquery phone = 106400412  
[2023-03-25 23:21:43] local.INFO: header  
[2023-03-25 23:21:43] local.INFO: header after fliter  
[2023-03-25 23:21:43] local.INFO: Body  after fliter  
[2023-03-25 23:21:43] local.INFO: array (
)  
[2023-03-25 23:21:43] local.INFO: transaction14  
[2023-03-25 23:21:43] local.INFO: first inquery phone = 106400412  
[2023-03-25 23:22:19] local.INFO: header  
[2023-03-25 23:22:19] local.CRITICAL: ****************************1  
[2023-03-25 23:22:19] local.ALERT: reach here  
[2023-03-25 23:22:19] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '4000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'أربعة ألف  ر.ي.',
  ),
)  
[2023-03-25 23:22:19] local.ERROR: المبلغ  
[2023-03-25 23:22:19] local.ERROR: 4,000.00  
[2023-03-25 23:22:19] local.ERROR: مبلغ وقدرة  
[2023-03-25 23:22:19] local.ALERT: 200  
[2023-03-25 23:22:19] local.ALERT: 3  
[2023-03-25 23:22:19] local.ALERT: 200  
[2023-03-25 23:22:19] local.ALERT: 3  
[2023-03-25 23:22:19] local.ALERT: 200  
[2023-03-25 23:22:19] local.ALERT: 3  
[2023-03-25 23:22:19] local.ALERT: 200  
[2023-03-25 23:22:19] local.ALERT: 3  
[2023-03-25 23:22:19] local.ALERT: 200  
[2023-03-25 23:22:19] local.ALERT: 3  
[2023-03-25 23:22:19] local.ALERT: 200  
[2023-03-25 23:22:19] local.ALERT: 3  
[2023-03-25 23:22:19] local.ALERT: 200  
[2023-03-25 23:22:19] local.ALERT: 3  
[2023-03-25 23:22:19] local.ALERT: 200  
[2023-03-25 23:22:19] local.ALERT: 3  
[2023-03-25 23:22:19] local.ALERT: 200  
[2023-03-25 23:22:19] local.ALERT: 3  
[2023-03-25 23:22:19] local.ALERT: 200  
[2023-03-25 23:22:19] local.ALERT: 2  
[2023-03-25 23:22:19] local.ALERT: 200  
[2023-03-25 23:22:19] local.ALERT: 2  
[2023-03-25 23:22:19] local.ALERT: 200  
[2023-03-25 23:22:19] local.ALERT: 2  
[2023-03-25 23:22:19] local.ALERT: 200  
[2023-03-25 23:22:19] local.ALERT: 2  
[2023-03-25 23:22:19] local.ALERT: 200  
[2023-03-25 23:22:19] local.ALERT: 2  
[2023-03-25 23:22:19] local.ALERT: 200  
[2023-03-25 23:22:19] local.ALERT: 2  
[2023-03-25 23:22:19] local.ALERT: 200  
[2023-03-25 23:22:19] local.ALERT: 2  
[2023-03-25 23:22:19] local.ALERT: 200  
[2023-03-25 23:22:19] local.ALERT: 1  
[2023-03-25 23:22:19] local.ALERT: 200  
[2023-03-25 23:22:19] local.ALERT: 1  
[2023-03-25 23:22:19] local.ALERT: 200  
[2023-03-25 23:22:19] local.ALERT: 1  
[2023-03-25 23:22:19] local.ALERT: 200  
[2023-03-25 23:22:19] local.ALERT: 1  
[2023-03-25 23:22:19] local.ALERT: 200  
[2023-03-25 23:22:19] local.ALERT: 1  
[2023-03-25 23:22:19] local.ALERT: 200  
[2023-03-25 23:22:19] local.ALERT: 2  
[2023-03-25 23:22:19] local.ALERT: 200  
[2023-03-25 23:22:19] local.ALERT: 10013  
[2023-03-25 23:22:19] local.ALERT: 200  
[2023-03-25 23:22:19] local.ALERT: 10013  
[2023-03-25 23:22:19] local.ALERT: 200  
[2023-03-25 23:22:19] local.ALERT: 10013  
[2023-03-25 23:22:19] local.ALERT: 200  
[2023-03-25 23:22:19] local.ALERT: 10013  
[2023-03-25 23:22:19] local.ALERT: 200  
[2023-03-25 23:22:19] local.ALERT: 1  
[2023-03-25 23:22:19] local.ALERT: 200  
[2023-03-25 23:22:19] local.ALERT: 3  
[2023-03-25 23:22:19] local.ALERT: 200  
[2023-03-25 23:22:19] local.ALERT: 3  
[2023-03-25 23:22:19] local.ALERT: 200  
[2023-03-25 23:22:19] local.ALERT: 40  
[2023-03-25 23:22:19] local.ALERT: 200  
[2023-03-25 23:22:19] local.ALERT: 40  
[2023-03-25 23:22:19] local.ALERT: 200  
[2023-03-25 23:22:19] local.ALERT: 40  
[2023-03-25 23:22:19] local.ALERT: 200  
[2023-03-25 23:22:19] local.ALERT: 40  
[2023-03-25 23:22:19] local.ALERT: 200  
[2023-03-25 23:22:19] local.ALERT: 200  
[2023-03-25 23:22:19] local.WARNING: 1  
[2023-03-25 23:22:19] local.WARNING: array (
  'ID' => 86,
  'Name' => 'فئة 25 جيجا 4000 ريال',
  'ServiceID' => 200,
  'Price' => 4000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 4000.0,
)  
[2023-03-25 23:22:19] local.ALERT: 200  
[2023-03-25 23:22:19] local.ALERT: 200  
[2023-03-25 23:22:19] local.ALERT: 200  
[2023-03-25 23:22:19] local.ALERT: 200  
[2023-03-25 23:22:19] local.ALERT: 200  
[2023-03-25 23:22:19] local.ALERT: 200  
[2023-03-25 23:22:19] local.ALERT: 200  
[2023-03-25 23:22:19] local.ALERT: 200  
[2023-03-25 23:22:19] local.ALERT: 200  
[2023-03-25 23:22:19] local.ALERT: 200  
[2023-03-25 23:22:19] local.ALERT: 200  
[2023-03-25 23:22:19] local.ALERT: 40  
[2023-03-25 23:22:19] local.CRITICAL: ****************************2  
[2023-03-25 23:22:19] local.CRITICAL: ****************************  
[2023-03-25 23:22:19] local.CRITICAL:   
[2023-03-25 23:22:19] local.CRITICAL: ****************************  
[2023-03-25 23:22:19] local.INFO: {
  "ClientBalanceResult": "4940.0000"
}  
[2023-03-25 23:22:19] local.INFO: array (
  'ClientBalanceResult' => '4940.0000',
)  
[2023-03-25 23:22:19] local.DEBUG: lattttef  
[2023-03-25 23:22:19] local.DEBUG: array (
  'ClientBalanceResult' => '4940.0000',
)  
[2023-03-25 23:22:19] local.INFO: transaction14  
[2023-03-25 23:22:19] local.INFO: first inquery phone = 106400412  
[2023-03-25 23:23:30] local.INFO: header  
[2023-03-25 23:23:30] local.INFO: header after fliter  
[2023-03-25 23:23:30] local.INFO: Body  after fliter  
[2023-03-25 23:23:30] local.INFO: array (
)  
[2023-03-25 23:23:30] local.INFO: transaction14  
[2023-03-25 23:23:30] local.INFO: first inquery phone = 106400412  
[2023-03-25 23:24:05] local.INFO: header  
[2023-03-25 23:24:05] local.INFO: header after fliter  
[2023-03-25 23:24:05] local.INFO: Body  after fliter  
[2023-03-25 23:24:05] local.INFO: array (
)  
[2023-03-25 23:24:05] local.INFO: transaction14  
[2023-03-25 23:24:05] local.INFO: first inquery phone = 106400412  
[2023-03-25 23:24:40] local.INFO: header  
[2023-03-25 23:24:40] local.INFO: header after fliter  
[2023-03-25 23:24:40] local.INFO: Body  after fliter  
[2023-03-25 23:24:40] local.INFO: array (
)  
[2023-03-25 23:24:40] local.INFO: transaction14  
[2023-03-25 23:24:40] local.INFO: first inquery phone = 106400412  
