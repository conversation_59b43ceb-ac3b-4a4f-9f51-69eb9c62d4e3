<?php

namespace App\Http\Controllers;

namespace App\Repositories;

use App\Models\Account;
use App\Models\AccountUser;
use App\Models\Agent;
use App\Models\ApiTransaction;
use App\Models\Bagat;
use App\Models\CardFaction;
use App\Models\CardOrder;
use App\Models\CardPayment;
use App\Models\CardType;
use App\Models\Client;
use App\Models\Faction;
use App\Models\Journal;
use App\Models\JournalEntry;
use App\Models\OrderInfo;
use App\Models\Topup;
use App\Models\Voucher;
use App\Services\NetCoinService;
use Carbon\Carbon;
use DateTime;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\DB;
use App\Services\TwasulService;
use Exception;
use Illuminate\Support\Facades\Crypt;
class TopupRepository
{
    public $header;
    public $body;
    public $journal;
    public $topup;
    public $accountUser;
    public $client;
    public $mtnamount;

    public $currencyRateAccounts;
    public $countCardFiction;
    public $providerCardFiction;
    public $cardfaction;
    public $cardOrderOrPayment;

    public function setHeader($header): self
    {
        $this->header = $header;
        return $this;
    }
    public function setBody($body): self
    {
        $this->body = $body;
        return $this;
    }
    public function amountmtn($amountmtn): self
    {
        $this->mtnamount = $amountmtn;
    }
    public function __construct()
    {
    }

    public function inquery($phone)
    {
        $netCoin = new NetCoinService($this->topup, $this->journal);
        $confrm = $netCoin->inquery($phone);
        return $confrm;
    }

    //

    public function checkUser()
    {
        Log::info("checkUser 1");


        $response = Http::withHeaders(
            $this->header
        )
            ->get('http://abuosama.ddns.net:7652/PaymentService/api/v1/balance/' . $this->body['lateflog']);
        Log::info($response);
        $mtnamount = 0.00;
        if (($response->ok() || $response->successful()) && $response->json('ClientBalanceResult')!=null) {
            if ($this->body['SID'] == 2) {
                Log::info("checkUser 2");

                Log::info($response->json());
                Log::info($this->body['AMT']);
                $mtnamount = $this->body['AMT'];
                Log::alert('reach here');
                $GetTopupQuota = Http::withHeaders(
                    $this->header
                )->post(
                    'http://abuosama.ddns.net:7652/PaymentService/api/v1/GetTopupQuota',
                    [
                        'Amount' => 1000,
                        "FactionID" => $this->body["FID"],
                        "LineType" => "2",
                        "LType" => $this->body["LType"],
                        "NetworkID" => 5,
                        "ServiceID" => $this->body['SID'],
                        "State" => 0

                    ]
                );
                Log::error($GetTopupQuota->json());
                foreach ($GetTopupQuota->json()['Items'] as $data) {
                    Log::error($data['Key']);
                    if ($data['Key'] == 'المبلغ') {
                        Log::error((number_format(floatval($data['Value']) / floatval(1000), 3, ".", "")));
                        if ($data['Value'] != null) {
                            if ($data['Value'] > 0) {
                                $val = floatval($data['Value']) / floatval(1000);
                                Log::info(number_format(floatval($val), 3, ".", ""));
                                $ratio = number_format(floatval($val), 3, ".", "");
                                if ($ratio > 1.21) {
                                    $quantity = number_format(floatval($this->body['AMT']) / floatval(1.21), 3, ".", "");
                                    Log::info($quantity);
                                    $amount = number_format((floatval($this->body['AMT']) / floatval(1.21)) * floatval($ratio), 2, ".", "");
                                    $this->body['LATEFnum']  = $quantity;
                                    $this->body['AMT']  = $amount;
                                    $mtnamount  = $amount;
                                } else {
                                    $quantity = number_format(floatval($this->body['AMT']) / $ratio, 3, ".", "");
                                    Log::info($quantity);
                                    $amount = $this->body['AMT'];
                                    $this->body['LATEFnum']  = $quantity;
                                    $this->body['AMT']  = $amount;
                                    $mtnamount  = $amount;
                                }
                            }

                            Log::error((number_format(floatval($this->body['AMT']), 2, ".", "")));


                            Log::info("checkUser 3");

                            Log::info("checkUser 3#");
                            if ($mtnamount == null) {

                                Log::info("pricing error");

                                throw ValidationException::withMessages([
                                    'user' => ['price less than Balance'],
                                ]);
                            } else {
                                Log::info("checkUser 4");


                                $AccountSlating = DB::table('AccountSlating')->where('AccountID', $this->body['lateflog'])->where('CurrencyID', 1)->first();
                                Log::info("checkUser 4#");

                                if ($AccountSlating) {
                                    Log::info((number_format(floatval(($AccountSlating->Amount + ($response->json('ClientBalanceResult')))), 2, ".", "")) < number_format(floatval($mtnamount), 4, ".", ""));

                                    if (($AccountSlating->Amount + ($response->json('ClientBalanceResult'))) < (($mtnamount))) {

                                        Log::info("checkUser -4");

                                        Log::info("price less than Balance");
                                        throw ValidationException::withMessages([
                                            'user' => ['price less than Balance'],
                                        ]);
                                    } else {
                                        Log::info("checkUser -4s");


                                        Log::info("checkUser 5");

                                        return $response->json();
                                    }
                                } else {
                                    if ((($mtnamount) > $response->json('ClientBalanceResult'))) {
                                        Log::info("checkUser -4");

                                        Log::info("price less than Balance");
                                        throw ValidationException::withMessages([
                                            'user' => ['price less than Balance'],
                                        ]);
                                    } else {

                                        Log::info("checkUser 5");

                                        return $response->json();
                                    }
                                }
                            }
                        }
                    }
                }
            } else {
               if($this->body['SID']==20033){
                $faction = Bagat::where('ServiceID', $this->body['SID'])->where('Number', $this->body['FID'])->first();

               }
               else {
                $faction = Faction::where('ServiceID', $this->body['SID'])->where('ID', $this->body['FID'])->first();
               }
                Log::info($response->json());

                $AccountSlating = DB::table('AccountSlating')->where('AccountID', $this->body['lateflog'])->where('CurrencyID', 1)->first();
                if ((!$AccountSlating) && (($faction->Price > $this->body['AMT'] ? $faction->Price : $this->body['AMT']) > $response->json('ClientBalanceResult'))) {



                    Log::info("price less than Balance");
                    throw ValidationException::withMessages([
                        'user' => ['price less than Balance'],
                    ]);
                } elseif ($AccountSlating ? ($AccountSlating->Amount + ($response->json('ClientBalanceResult'))) < (($faction->Price > $this->body['AMT'] ? $faction->Price : $this->body['AMT'])) : false) {

                    Log::info("price less than Balance");
                    throw ValidationException::withMessages([
                        'user' => ['price less than Balance'],
                    ]);
                } else {
                    return $response->json();
                }
            }
        } else {
            Log::info("ttttttttttf");
            throw ValidationException::withMessages([
                'user' => ['try again'],
            ]);
        }
    }

    public function checkFaction()
    {
        if ($this->body["SID"] != 2) {
            Log::alert('reach here');
            Log::alert($this->body);

            $GetTopupQuota = Http::withHeaders(
                $this->header
            )->post(
                'http://abuosama.ddns.net:7652/PaymentService/api/v1/GetTopupQuota',
                [
                    'Amount' => $this->body["AMT"],
                    "FactionID" => $this->body["FID"],
                    "LType" => $this->body["LType"],
                    "NetworkID" => 0,
                    "ServiceID" => $this->body['SID'],
                    "State" => 0

                ]
            );
            Log::error($GetTopupQuota->json()['Items']);
            foreach ($GetTopupQuota->json()['Items'] as $data) {
                Log::error($data['Key']);
                if ($data['Key'] == 'المبلغ') {
                    Log::error((number_format(floatval($data['Value']), 2, ".", "")));

                    if ((number_format(floatval($data['Value']), 2, ".", "")) != (number_format(floatval($this->body['AMT']), 2, ".", ""))) {
                        Log::error((number_format(floatval($this->body['AMT']), 2, ".", "")));

                        throw ValidationException::withMessages([
                            'try' => ["فشل في تنفيذ العملية"],
                        ]);
                    }
                }
            }
        } else {
        }

        //  $GetTopupQuota
        $response = Http::withHeaders(
            $this->header
        )->post(
            'http://abuosama.ddns.net:7652/PaymentService/api/v1/CacheList',
            [
                'CacheInfo' => []
            ]
        );

        // Log::warning($response->json()['CacheData'][3]); 
        if (($response->ok() || $response->successful())  && $response->json('CacheData') != Null) {
            $jj =  $response->collect();

            //   $jj=json_decode($response->getContent(),true);
            //    Log::alert($jj);
            //Log::alert('reach here2');
            //  Log::alert($jj);


            //  explode()
            //    implode('here wtire the text',$fList)
            if (count($jj) >= 3) {

                $fList = json_decode($jj["CacheData"][3], true);
                // Log::alert($fList[0]);
                //  Log::alert(count($fList));

                foreach ($fList as $data) {
                    if ($this->body['FID'] == $data["ID"] && $this->body['SID'] == $data["ServiceID"]) {
                        if (($data["Price"] > $data["PersonnalPrice"] ? $data["Price"] : $data["PersonnalPrice"]) <= $this->body["AMT"]) {
                            log::warning(1);
                        } else {
                            log::warning(0);
                            throw ValidationException::withMessages([
                                'user' => ['try again'],
                            ]);
                        }
                        log::warning($data);
                    }
                    log::alert($this->body['SID']);
                    log::alert($data["ServiceID"]);

                    //$collec=collect($data);
                    //  log::alert((string) $data);
                    // log::alert( $data ,Str::contains("\"ServiceID\":"));
                    // if($this->body['FID']!=Null && $this->body['SID']!=Null && $data['ServiceID'] !=Null)
                    // {

                    //     if($this->body['FID']==$data['id'])
                    //     {
                    //         log::warning($data);
                    //     }

                    // }

                }
            }
            //   Log::alert('reach here4');

        }
    }

    public function checkBalance(Request $request)
    {


        // $header['authorization'] = "Bearer " . $token->token;
        //http://abuosama.ddns.net:9802/api/v1/CheckYmLoan/1/1'

        // 'http://abuosama.ddns.net:7652/PaymentService/api/v1/balance/587445',

        //         > POST /PaymentService/api/v1/ExecuteTopup HTTP/1.1

        // working

        //  dd($this->header);
        $response = Http::withHeaders(
            [$this->header]
        )
            ->get(
                'http://abuosama.ddns.net:7652/PaymentService/api/v1/balance/587445'
            );
        //
        dd($response->json());
    }

    public function transaction(Request $request)
    {
        // here transaction and validate
        DB::transaction(function () {
            Log::info("transaction1");
            //User
            $this->accountUser = AccountUser::where('AccountID', $this->body['lateflog'])->first();
            Log::info("transaction2");
            $account = $this->accountUser->account()->first();
            Log::info("transaction3");
            $userInfo = $this->accountUser->userInfo()->first();
            Log::info("transaction4");

            $this->client = Client::where('AccountID', $this->body['lateflog'])->first();
            Log::info("transaction4");
            //Parent
            $agent = null;
            $AgentAccount = null;
            $tAccountID = $this->accountUser->AccountID;
            $tByChild = 0;
            if ($this->client) {
                $tAccountID = $this->client->ParentAccountID ?? $this->client->AccountID;
                $tByChild = $this->client->ParentAccountID ? 1 : 0;
                $agent = Agent::where("ID", $this->client->AgentID)->first();
                Log::info("transaction5");
                // dd($agent);
                $AgentAccount = $agent ?  Account::where("ID", $agent->AccountID)->first() :  null;
                Log::info("transaction6");
            }
            if($this->body['SID']==20033){
                $faction = Bagat::where('ServiceID', $this->body['SID'])->where('Number', $this->body['FID'])->first();

               } 
               else {
                $faction = Faction::where('ServiceID', $this->body['SID'])->where('ID', $this->body['FID'])->first();

               }


            
            Log::info("transaction7");


            // $parentAccountUser = AccountUser::where('UserID',$this->accountUser->ParentID)->first();

            // $parentUserInfo = $parentAccountUser->userInfo()->first() ?? null; //check
            // $parentAccount = $parentAccountUser->account()->first() ?? null; //check

            // get provider by serviec
            $provider = 2;
            Log::debug($this->body);

            $apiTransaction = ApiTransaction::create([
                'Number' => date('YmdHis'),
                'UserID' => $userInfo->ID,
                'UserType' => $userInfo->Type,
                'Identifier' => $this->header['latefidentifier'],
                'TransactionID' => date('YmdHis'),
                'Channel' => 2,
                'VC' => 5,
                'SessionID' => 0,
                'SessionNo' => null,
                'EntityName' => "Topup",
                'ApiType' => "Execute",
                'DeviceInfo' => json_encode($this->body),
                'UserAgent' => $this->header['user-agent'],
                'Host' => 'abuosama.ddns.net:9802',
                'IpAddress' => '0.0.0.0',
                'Type' => 0,
                'Status' => 0,
                'Location' => null,
                'Note' => null,
                'CreatedTime' => date('Y-m-d H:i:s')
            ]);
            Log::info("transaction8");

            // $voucher = Voucher::create([

            //     'Name' => "dddd",
            //     'Entity' => 1,
            //     'Note' => "ddd",
            //     'CreatedBy' => 1,
            //     'BranchID' => 1,
            //     'CreatedTime' => date('Y-m-d H:i:s'),
            //     'Active' => 1,
            //     'CrOrDr' => 1,
            //     'Module' => "11"


            // ]);



            $this->journal = Journal::create([
                'VoucherID' => 7,
                'Number' => Journal::max('Number') + 1,
                'EntryID' => 00,
                'Date' => date('Y-m-d H:i:s'),
                'CreatedBy' => $userInfo->ID,
                'Debited' => 1, // pending
                'CreatedTime' => date('Y-m-d H:i:s'),
                'BranchID' =>  $this->accountUser->BranchID,
                // 'Year' => null,
                'Status' => 1, // pending
                'TotalAmount' => null,
                'SyncJournalID' => null,
                'datestamb' => ((int) date('Ymd')),
                'EntrySource' => 1,
                'Depended' => 0,
                'RefNumber' => null,
                'CurrencyID' => null
            ]);
            Log::info("transaction9");
            Log::info($this->body['AMT']);
            $journalEntry1 = JournalEntry::create([
                'ParentID' => $this->journal->id,
                'Amount' => -1 * ($this->body['SID'] != 2 ? ($faction->Price > $this->body['AMT'] ? $faction->Price : $this->body['AMT']) : $this->body['AMT']),
                'CurrencyID' => 1,
                'DCAmount' => -1 * ($this->body['SID'] != 2 ? ($faction->Price > $this->body['AMT'] ? $faction->Price : $this->body['AMT']) : $this->body['AMT']),
                'AccountID' => $account->ID,
                'CostCenterID' => null,
                'Note' => $this->body['SID'] == 2 ? "ام تي ان مفتوح" : ( $this->body['SID'] == 20033 ? 'ام تي ان سعر موحد' : (in_array($faction->ServiceID, [40]) ? 'عدن نت' : 'يمن فورجي') ) . $this->body['SNO'],
                'Datestamp' => ((int) date('Ymd')),
                'ExchangeRate' => 1.00,
                'SyncEntryID' => null
            ]);
            Log::info("transaction10");
            if ($this->body['SID'] == 2) {
                Log::info($this->body['LATEFnum']);
                Log::info((number_format(floatval($this->body['LATEFnum'] * 1.21), 2, ".", "")));
            }

            $journalEntry2 = JournalEntry::create([
                'ParentID' => $this->journal->id,
                'Amount' => $this->body['SID'] == 2 ? (number_format(floatval($this->body['LATEFnum'] * 1.21), 2, ".", "")) : $faction->ProviderPrice,
                'CurrencyID' => 1,
                'DCAmount' => $this->body['SID'] == 2 ? (number_format(floatval($this->body['LATEFnum'] * 1.21), 2, ".", "")) : $faction->ProviderPrice,
                'AccountID' => ($this->body['SID'] == 2 ) ? 519814 : (($this->body['SID'] == 40|| $this->body['SID'] == 20033) ? 316581 : 379015),
                'CostCenterID' => null,
                'Note' => ($this->body['SID'] == 2 ? "ام تي ان مفتوح"  : ($this->body['SID'] == 20033 ? 'ام تي ان سعر موحد' :($this->body['SID'] == 40  ? 'عدن نت' : 'يمن فورجي'))) . $this->body['SNO'],
                'Datestamp' => ((int) date('Ymd')),
                'ExchangeRate' => 1.00,
                'SyncEntryID' => null
            ]);
            Log::info("transaction11");
            if ($this->body['SID'] == 2) {
                Log::info("12" . $this->body['LType']);

                $acounter = ($this->body['AMT']) - (number_format(floatval($this->body['LATEFnum'] * 1.21), 2, ".", ""));
            } else {
                $acounter = ($faction->Price > $this->body['AMT'] ? $faction->Price : $this->body['AMT']) - $faction->ProviderPrice;
            }
            if ($acounter > 0) {

                $journalEntry3 = JournalEntry::create([
                    'ParentID' => $this->journal->id,
                    'Amount' => $acounter,
                    'CurrencyID' => 1,
                    'DCAmount' => $acounter,
                    'AccountID' =>  222839,
                    'CostCenterID' => null,
                    'Note' => 'فارق تحصيل عملية رقم' . $journalEntry1->ID,
                    'Datestamp' => ((int) date('Ymd')),
                    'ExchangeRate' => 1.00,
                    'SyncEntryID' => null
                ]);
            }
            //	Log::info("".this->client? $this->client->ParentAccountID ? 1 : 0:0 );
            //  dd($faction->ServiceID.$Journal->Number);
            Log::info("topup1" . ($this->body['SID'] == 2 ? (number_format(floatval($this->body['LATEFnum'] * 1.21), 2, ".", "")) : $faction->ProviderPrice));
            Log::info("topup2" . ($this->body['SID'] == 2 ? (number_format(floatval(($this->body['AMT'] / $this->body['LATEFnum'])), 3, ".", "")) : ($faction->Price > $this->body['AMT'] ? $faction->Price : (number_format(floatval(($this->body['AMT']) / $faction->Quantity), 3, ".", "")))));
            Log::info("topup3" . ($this->body['SID'] == 2 ? (number_format(floatval($this->body['LATEFnum'] * 1.21), 2, ".", "")) : $faction->ProviderPrice));
            Log::info("topup4" . ($this->body['SID'] == 2 ? (number_format(floatval($this->body['LATEFnum'] * 1.21), 2, ".", "")) : ($faction->Price > $this->body['AMT'] ? number_format(floatval($faction->Price, 2, ".", "")) : number_format(floatval($this->body['AMT']), 2, ".", ""))));

            Log::info("topup5" . ($this->body['SID'] == 2 ? $this->body['AMT'] : ($faction->Price > $this->body['AMT'] ? $faction->Price : $this->body['AMT'])));
            Log::info("topup6" . ($this->body['SID'] == 2 ? ($this->body['AMT'] - (number_format(floatval($this->body['LATEFnum'] * 1.21), 2, ".", ""))) : ($faction->Price > $this->body['AMT'] ? $faction->Price : $this->body['AMT']) - $faction->ProviderPrice));
            $ProviderID = 0;
            if ($this->body['SID'] == 2  ) {
                $ProviderID = 10031;
            } else if ($this->body['SID'] == 40 ||  $this->body['SID'] == 20033) {
                $ProviderID = 10018;
            } else {
                $ProviderID = 10028;
            }
            try {
                //     Log::info("topup");
                //     Log::info(      'Number'                                            .                                                   $this->journal->Number);
                //     Log::info(      'ServiceID'                                            .                                                    $this->body['SID']);
                //     Log::info(      'NetworkID'                                            .                                                    "null");
                //     Log::info(      'SubscriberNumber'                                            .                                                    $this->body['SNO']);
                //     Log::info(      'Amount'                                            .                                                    $this->body['AMT']);
                //     Log::info(      'FactionID'                                            .                                                    $this->body['FID']);
                //     Log::info(      'RegionID'                                            .                                                    "null"); // default
                //     Log::info(      'LineType'                                            .                                                    $this->body['SID']==2?($this->body['mLtype']):'غير معروف'); // default
                //     Log::info(      'Date'                                            .                                                    date('Y-m-d H:i:s'));
                //     Log::info(      'Status'                                            .                                                    2);   // 0 faild  1 complited  // 2 hold // 5 time out // 6 reverse
                //     Log::info(      'Note'                                            .                                                    "null");
                //     Log::info(      'CreditorAccountID'                                            .                                                    $this->body['SID']==2? 519814  :( $this->body['SID']==40?316581 : 379015));
                //     Log::info(      'CurrencyID'                                            .                                                    1);
                //     Log::info(      'DebitorAccountID'                                            .                                                   $this->accountUser->AccountID );
                //     Log::info(      'AgentID'                                            .                                                    "null");
                //     Log::info(      'RefNumber'                                            .                                                    "null");
                //     Log::info(      'TransactionID'                                            .                                                    $this->journal->Number);
                //     Log::info ($ProviderID."ProviderID");
                //     Log::info(      'EntryID'                                            .                                                    $this->journal->id);
                //     Log::info(      'PaymentEntryID'                                            .                                                    "null");
                //     Log::info(      'Channel'                                            .                                                    2);
                //     Log::info(      'CreatedBy'                                            .                                                    $userInfo->ID );
                //     Log::info(      'BranchBy'                                            .                                                    "null");
                //     Log::info(      'CreatedTime'                                            .                                                     date('Y-m-d H:i:s'));
                //     Log::info(      'BranchID'                                            .                                                    $this->accountUser->BranchID);
                //    Log::info(number_format(floatval($this->body['LATEFnum']/1.21),2,".",""));
                //     Log::info(      'ProviderRM'                                            .                                                    '');
                //     // Log::info(      'ProviderPrice'                                            .                                                   $this->body['SID']==2?number_format(floatval($this->body['LATEFnum']/1.21),2,".",""): $faction->ProviderPrice);
                //     Log::info(      'SubNote'                                            .                                                    "null");
                //     Log::info(      'Datestamb'                                            .                                                    date('Ymd'));
                //     Log::info(      'UniqueNo'                                            .                                                     date('YmdHis'));
                //     Log::info(      'Quantity'                                            .                                                   $this->body['SID']==2? $this->body['LATEFnum']:$faction->Quantity);
                //     Log::info(      'UnitPrice'                                            .                                                   $this->body['SID']==2?(number_format(floatval(($this->body['AMT'] /$this->body['LATEFnum'])),3,".","")): ($faction->Price > $this->body['AMT'] ? $faction->Price : $this->body['AMT'])/$faction->Quantity);
                //     Log::info(      'UnitCost'                                            .                                                   $this->body['SID']==2?1.21 : $faction->ProviderPrice/$faction->Quantity);
                //     Log::info(      'CostAmount'                                            .                                                    $this->body['SID']==2?(number_format(floatval( $this->body['LATEFnum']*1.21),2,".","")): $faction->ProviderPrice);
                //     Log::info(      'DifferentialAmount'                                            .                                                    0.00);
                //     Log::info(      'CommissionAmount'                                            .                                                    0.00);
                //     Log::info(      'Discount'                                            .                                                    0.00);
                //     Log::info(      'TotalCost'                                            .                                                    ( $this->body['SID']==2?(number_format(floatval( $this->body['LATEFnum']*1.21),2,".","")): $faction->ProviderPrice));
                //     Log::info(      'TotalAmount'                                            .                                                    $this->body['SID']==2? $this->body['AMT'] : number_format(floatval(($faction->Price > $this->body['AMT'] ? $faction->Price : $this->body['AMT'])),2,".",""));
                //     Log::info(      'Profits'                                            .                                                    $this->body['SID']==2?( $this->body['AMT']-(number_format(floatval( $this->body['LATEFnum']*1.21),2,".",""))) :($faction->Price > $this->body['AMT'] ? $faction->Price : $this->body['AMT'])-$faction->ProviderPrice);
                //     Log::info(      'Method'                                            .                                                    2); // default
                //     Log::info(      'Type'                                            .                                                    0); // default
                //     Log::info(      'Class'                                            .                                                    0); // default
                //     Log::info(      'LType'                                            .                                                    $this->body['LType']); // default
                //     Log::info(      'OperatorID'                                            .                                                    1); // default
                //     Log::info(      'AppTechApi'                                            .                                                    0); // default
                //     Log::info(      'BillNumber'                                            .                                                    $this->body['SID'].'00'.$this->journal->Number);
                //     Log::info(      'BillState'                                            .                                                    0); // default
                //     Log::info(      'Debited'                                            .                                                    1); // default
                //     Log::info(      'ByChild'                                            .                                                   $tByChild);//this->client?( $this->client->ParentAccountID ? 1 : 0):0, // default
                //     Log::info(      'IsDirect'                                            .                                                    1); // default
                //     Log::info(      'BundleName'                                            .                                                    $this->body['SID']==2?"ام تي ان رصيد مفتوح": $faction->Name); // default
                //     Log::info(      'BundleCode'                                            .                                                    $this->body['SID']==2? $this->body['AMT'] : $faction->ProviderCode); // default
                //     Log::info(      'ExCode'                                            .                                                    "null"); // default
                //     Log::info(      'TransNumber'                                            .                                                    date('YmdHis')); // default
                //     Log::info(      'OperationID'                                            .                                                    0); // default
                //     Log::info(      'AccountID'                                            .                                                   $tAccountID);//this->client?($this->client->ParentAccountID ?? $this->client->AccountID ):$this->accountUser->AccountID,
                //     Log::info(      'State'                                            .                                                    0);
                //     Log::info(      'StateClass'                                            .                                                    ''); // default
                //     Log::info(      'Identifier'                                            .                                                    $this->header['latefidentifier']); // default
                //     Log::info(      'AdminNote'                                            .                                                    ''); // default
                //     Log::info(      'AccountNote'                                            .                                                    ''); // default
                //     Log::info(      'Description'                                            .                                                    "null"); // default
                //     Log::info(      'Responded'                                            .                                                    0); // default
                //     Log::info(      'RequestInfo'                                            .                                                    $this->body['SID'].'#'.$this->body['SNO'].'#'.$this->body['AMT'].'#'.$this->body['FID']); // default
                //     Log::info(      'ResponseTime'                                            .                                                   date('Y-m-d H:i:s') );
                //     Log::info(      'ResponseStatus'                                            .                                                    0); // default
                //     Log::info(      'ExecutionPeroid'                                            .                                                    date('s')); // default
                //     Log::info(      'FaildRequest'                                            .                                                    0); // default
                //     Log::info(      'FailedReason'                                            .                                                    "null"); // default
                //     Log::info(      'FailedType'                                            .                                                    0); // default
                //     Log::info(      'Cured'                                            .                                                    0); // default
                //     Log::info(      'CuredBy'                                            .                                                    "null"); // default
                //     Log::info(      'CuredInfo'                                            .                                                    "null"); // default
                //     Log::info(      'InspectInfo'                                            .                                                   "null");
                //     Log::info(      'Flag'                                            .                                                    2);
                //     Log::info(      'Action'                                            .                                                    0); // default
                //     Log::info(      'QuotaionID'                                            .                                                    0); // default
                //     Log::info(      'SyncID'                                            .                                                   0);  // default

                $toptest = [
                    'Number' => $this->journal->Number,
                    'ServiceID' => $this->body['SID'],
                    'NetworkID' => null,
                    'SubscriberNumber' => $this->body['SNO'],
                    'Amount' => $this->body['AMT'],
                    'FactionID' => $this->body['FID'],
                    'RegionID' => null, // default
                    'LineType' => ($this->body['SID'] == 2  || $this->body['SID'] == 20033 )? ($this->body['mLtype']) : 'غير معروف', // default
                    'Date' => date('Y-m-d H:i:s'),
                    // 'Year' => null,
                    'Status' => 2,   // 0 faild  1 complited  // 2 hold // 5 time out // 6 reverse
                    'Note' => null,
                    'CreditorAccountID' => ($this->body['SID'] == 2  || $this->body['SID'] == 20033 ) ? 519814  : ($this->body['SID'] == 40 ? 316581 : 379015),
                    'CurrencyID' => 1,
                    'DebitorAccountID' => $this->accountUser->AccountID,
                    'AgentID' => null,
                    'RefNumber' => null,
                    'TransactionID' => $this->journal->Number,
                    'ProviderID' => $ProviderID,
                    'EntryID' => $this->journal->id,
                    'PaymentEntryID' => null,
                    'Channel' => 2,
                    'CreatedBy' => $userInfo->ID,
                    'BranchBy' => null,
                    'CreatedTime' =>  date('Y-m-d H:i:s'),
                    // 'RowVersion' => null,
                    'BranchID' => $this->accountUser->BranchID,
                    'ProviderRM' => '',
                    'ProviderPrice' => $this->body['SID'] == 2 ? (number_format(floatval($this->body['LATEFnum'] * 1.21), 2, ".", "")) : $faction->ProviderPrice,
                    'SubNote' => null,
                    'Datestamb' => date('Ymd'),
                    'UniqueNo' =>  date('YmdHis'),
                    'Quantity' => $this->body['SID'] == 2 ? $this->body['LATEFnum'] : $faction->Quantity,
                    'UnitPrice' => $this->body['SID'] == 2 ? (number_format(floatval(($this->body['AMT'] / $this->body['LATEFnum'])), 3, ".", "")) : ($faction->Price > $this->body['AMT'] ? $faction->Price : $this->body['AMT']) / $faction->Quantity,
                    'UnitCost' => $this->body['SID'] == 2 ? 1.21 : $faction->ProviderPrice / $faction->Quantity,
                    'CostAmount' => $this->body['SID'] == 2 ? (number_format(floatval($this->body['LATEFnum'] * 1.21), 2, ".", "")) : $faction->ProviderPrice,
                    'DifferentialAmount' => 0.00,
                    'CommissionAmount' => 0.00,
                    'Discount' => 0.00,
                    'TotalCost' => ($this->body['SID'] == 2 ? (number_format(floatval($this->body['LATEFnum'] * 1.21), 2, ".", "")) : $faction->ProviderPrice),
                    'TotalAmount' => $this->body['SID'] == 2 ? $this->body['AMT'] : number_format(floatval(($faction->Price > $this->body['AMT'] ? $faction->Price : $this->body['AMT'])), 2, ".", ""),
                    'Profits' => $this->body['SID'] == 2 ? ($this->body['AMT'] - (number_format(floatval($this->body['LATEFnum'] * 1.21), 2, ".", ""))) : ($faction->Price > $this->body['AMT'] ? $faction->Price : $this->body['AMT']) - $faction->ProviderPrice,
                    'Method' => 2, // default
                    'Type' => 0, // default
                    'Class' => 0, // default
                    'LType' => $this->body['LType'], // default
                    'OperatorID' => 1, // default
                    'AppTechApi' => 0, // default
                    'BillNumber' => $this->body['SID'] . '00' . $this->journal->Number,
                    'BillState' => 0, // default
                    'Debited' => 1, // default
                    'ByChild' => $tByChild, //this->client?( $this->client->ParentAccountID ? 1 : 0):0, // default
                    'IsDirect' => 1, // default
                    'BundleName' => $this->body['SID'] == 2 ? "ام تي ان رصيد مفتوح" : $faction->Name, // default
                    'BundleCode' => $this->body['SID'] == 2 ? $this->body['AMT'] : ($this->body['SID'] != 20033 ? $faction->ProviderCode : $faction->Code), // default
                    'ExCode' => null, // default
                    'TransNumber' => date('YmdHis'), // default
                    'OperationID' => 0, // default
                    'AccountID' => $tAccountID, //this->client?($this->client->ParentAccountID ?? $this->client->AccountID ):$this->accountUser->AccountID,
                    'State' => 0,
                    'StateClass' => '', // default
                    'Identifier' => $this->header['latefidentifier'], // default
                    'AdminNote' => '', // default
                    'AccountNote' => '', // default
                    'Description' => null, // default
                    'Responded' => 0, // default
                    'RequestInfo' => $this->body['SID'] . '#' . $this->body['SNO'] . '#' . $this->body['AMT'] . '#' . $this->body['FID'], // default
                    // 'ResponseInfo' => 2, // default
                    'ResponseTime' => date('Y-m-d H:i:s'),
                    'ResponseStatus' => 0, // default
                    // 'ResponseReference' => 0, // default
                    'ExecutionPeroid' => date('s'), // default
                    'FaildRequest' => 0, // default
                    'FailedReason' => NULL, // default
                    'FailedType' => 0, // default
                    'Cured' => 0, // default
                    'CuredBy' => NULL, // default
                    'CuredInfo' => NULL, // default
                    'InspectInfo' => NULL,
                    'Flag' => 2,
                    'Action' => 0, // default
                    'QuotaionID' => 0, // default
                    'SyncID' => 0 // default
                ];
                Log::info($toptest);

                $this->topup = Topup::create($toptest);
            } catch (\Exception $exception) {
                Log::info("topup" + "where");

                // You can check get the details of the error using `errorInfo`:
                $errorInfo = $exception->getMessage();
                Log::info($exception->getMessage());
                Log::info($errorInfo);
                throw new Exception($exception->getMessage());
            }
            Log::info("transaction13");
            // change to journal  Debited and Status
        });
        if ($this->body['SID'] == 40 || $this->body['SID'] == 2 || $this->body['SID'] == 20033) {
            $twasulService = new TwasulService($this->topup, $this->journal,'TOPUP');
            $confrm = $twasulService->confirm();

            return $confrm;
        } else {
            $netCoin = new NetCoinService($this->topup, $this->journal);
            $confrm = $netCoin->confirm();
            return $confrm;
        }
    }

    public function checkUserGame()
    {
        Log::info("checkUser 1");


        $response = Http::withHeaders(
            $this->header
        )
            ->get('http://abuosama.ddns.net:7652/PaymentService/api/v1/balance/' . $this->body['Entity']['AccountID']);
        Log::info($response);
     //   $mtnamount = 0.00;
        if (($response->ok() || $response->successful()) && $response->json('ClientBalanceResult')!=null) {
             
                 
                
                Log::info($response->json());
                Log::info("price  AccountParent");
                Log::info($this->body['Entity']['AccountID']);
             //   .AccountParents where AccountID = 587445
                $AccountParent= DB::table('AccountParents')->where('AccountID', $this->body['Entity']['AccountID'] )->first();
                Log::info('AccountParent');
                
              //  $accountId = // your accountId value here;

                $groupAccountIds = DB::table('GroupItem')
                    ->where('ItemID',$AccountParent->ParentAccountID)
                    ->where('Type', 'Accounts')
                    ->first();
                    Log::info('groupAccountIds');
                   // Log::info($groupAccountIds);


                   
            //     SELECT *
            // FROM dbo.Quotation q
            // INNER JOIN dbo.A_Client_Acc a ON q.AccountGroupID = a.Group_id
            // WHERE ServiceID = 
        //       WHERE ItemID =587195
        //   AND Type = 'Accounts'
                
                $this->currencyRateAccounts = DB::table('Quotation')
                    ->where('ServiceID', $this->body['Target'] == 'CardOrder' ? 20029 : 2030)
                    ->where('AccountGroupID', $groupAccountIds->GroupID)
                    ->first();

                    $AccountSlating = DB::table('AccountSlating')->where('AccountID', $this->body['Entity']['AccountID'])->where('CurrencyID', 1)->first();
                    $CurrencyRate = DB::table('CurrencyRate')->where('SourceCurrencyID', 2)->where('TargetCurrencyID', 1)->first();

                    //$accountId = // your accountId value here;

                    Log::info('currencyRateAccounts');
                  

                $this->cardfaction = CardFaction::where('ID',  $this->body['Entity']['CardFactionID'])->where('CardTypeID', $this->body['Entity']['CardTypeID'] )->first();

                // public $currencyRateAccount;
                // public $countCardFiction;

            //     SELECT  [ID]
            //     ,[SourceCurrencyID]  =2
            //     ,[TargetCurrencyID]  =1
            //     ,[ExchangeRate]
            //  CurrencyRate]
            $this->countCardFiction =($this->cardfaction->SelePrice  *   $this->currencyRateAccounts->Price * $CurrencyRate-> ExchangeRate) ;
            $this->providerCardFiction =($this->cardfaction->SelePrice  *   $CurrencyRate-> ExchangeRate) ;
            if ((!$AccountSlating) && (( $this->countCardFiction  > $response->json('ClientBalanceResult')))) {
                    Log::info("price less than Balance");
                    throw ValidationException::withMessages([
                        'user' => ['price less than Balance'],
                    ]);
                }
                else if($AccountSlating ? ($AccountSlating->Amount + ($response->json('ClientBalanceResult'))) < (( $this->countCardFiction)) : false){
                    Log::info("price less than Balance");
                    throw ValidationException::withMessages([
                        'user' => ['price less than Balance'],
                    ]);
                }
                else {
                    return $response->json();
                } 
        } else {
            Log::info("ttttttttttf Game");
            throw ValidationException::withMessages([
                'user' => ['try again'],
            ]);
        }
    }
    public function checkFactionGame()
    {
       

        //  $GetTopupQuota
        $response = Http::withHeaders(
            $this->header
        )->post(
            'http://abuosama.ddns.net:7652/PaymentService/api/v1/CacheList',
            [
                'CacheInfo' => []
            ]
        );

        // Log::warning($response->json()['CacheData'][3]); 
        if (($response->ok() || $response->successful())  && $response->json('CacheData') != Null) {
            $jj =  $response->collect();
 
            if (count($jj) >= 3) {

                $fList = json_decode($jj["CacheData"][3], true);
                // Log::alert($fList[0]);
                //  Log::alert(count($fList));
                $found=true;
                foreach ($fList as $data) {
                    if ($this->body['Entity']['CardFactionID'] == $data["ID"] && $this->body['Entity']['CardTypeID'] == $data["CardTypeID"]) {
                        $found=true;
                       // log::warning($data);
                    }
                    else {
                       
                    }

                }
                if(!$found){
                    Log::info("fiction not found ");
                    throw ValidationException::withMessages([
                        'user' => ['try again'],
                    ]);
                }
            }
            //   Log::alert('reach here4');

        }
    }

    public function transactionGame(Request $request)
    {

        // here transaction and validate
       
          DB::transaction(function () {

            try {

            Log::info("transaction1");
            //User

             $this->accountUser = AccountUser::where('AccountID', $this->body['Entity']['AccountID'])->first(); //edit mohammed



            Log::info("transaction2");
             $account = $this->accountUser->account()->first();  //edit mohammed
            Log::info("transaction3");
              $userInfo = $this->accountUser->userInfo()->first(); //  edit mohammed
            Log::info("transaction4");

              $this->client = Client::where('AccountID', $this->body['Entity']['AccountID'])->first(); //edit mohammed
            Log::info("transaction4");
            //Parent
            $agent = null;
            $AgentAccount = null;
             $tAccountID = $this->accountUser->AccountID;  //edit mohammed
            $tByChild = 0;
            if ($this->client) {
                $tAccountID = $this->client->ParentAccountID ?? $this->client->AccountID;

                $tByChild = $this->client->ParentAccountID ? 1 : 0;
                $agent = Agent::where("ID", $this->client->AgentID)->first();
                Log::info("transaction5");
                // dd($agent);
                $AgentAccount = $agent ?  Account::where("ID", $agent->AccountID)->first() :  null;
                Log::info("transaction6");
            }

            // $faction = Faction::where('ServiceID', $this->body['SID'])->where('ID', $this->body['FID'])->first(); //edit mohammed
            Log::info("transaction7");

            $provider = 2;
            Log::debug($this->body);


            $apiTransaction = ApiTransaction::create([
                'Number' => date('YmdHis'),
                  'UserID' => $userInfo->ID,  
                'UserType' => $userInfo->Type,  
                'Identifier' => $this->header['latefidentifier'],
                'TransactionID' => date('YmdHis'),
                'Channel' => 2,
                'VC' => 5,
                'SessionID' => 0,
                'SessionNo' => null,
                'EntityName' => $this->body['Target'] ,
                'ApiType' => "Execute",
                'DeviceInfo' => json_encode($this->body),
                'UserAgent' => $this->header['user-agent'],
                'Host' => 'abuosama.ddns.net:9802',
                'IpAddress' => '0.0.0.0',
                'Type' => 0,
                'Status' => 0,
                'Location' => null,
                'Note' => null,
                'CreatedTime' => date('Y-m-d H:i:s')
            ]);
            Log::info("transaction8");




            $this->journal = Journal::create([
                'VoucherID' => 7,
                'Number' => Journal::max('Number') + 1,
                'EntryID' => 00,
                'Date' => date('Y-m-d H:i:s'),
                'CreatedBy' => $userInfo->ID,    
                'Debited' => 1, // pending
                'CreatedTime' => date('Y-m-d H:i:s'),
                 'BranchID' =>  $this->accountUser->BranchID,  
                // 'Year' => null,
                'Status' => 1, // pending
                'TotalAmount' => null,
                'SyncJournalID' => null,
                'datestamb' => ((int) date('Ymd')),
                'EntrySource' => 1,
                'Depended' => 0,
                'RefNumber' => null,
                'CurrencyID' => null
            ]); 

            
            Log::info("transaction9");
            Log::info($this->countCardFiction);
            Log::info(([
                'ParentID' => $this->journal->id,            
                'CurrencyID' => 1,
                'Amount' => -1 * $this->countCardFiction ,  
                'DCAmount' =>-1 * $this->countCardFiction ,
                 'AccountID' => $account->ID,  
                'CostCenterID' => null, 
                'Note' => $this->body['Target'] .' ' .$this->cardfaction->Name, 
                'Datestamp' => ((int) date('Ymd')),
                'ExchangeRate' => 1.00,
                'SyncEntryID' => null
            ]));


            $journalEntry1 = JournalEntry::create([
                'ParentID' => $this->journal->id,            
                'CurrencyID' => 1,
                'Amount' =>-1 * $this->countCardFiction ,  
                'DCAmount' =>-1 * $this->countCardFiction ,
                 'AccountID' => $account->ID,  
                'CostCenterID' => null, 
                'Note' => $this->body['Target'] .' ' .$this->cardfaction->Name, 
                'Datestamp' => ((int) date('Ymd')),
                'ExchangeRate' => 1.00,
                'SyncEntryID' => null
            ]);
            Log::info("transaction10");
 


            $journalEntry2 = JournalEntry::create([
                'ParentID' => $this->journal->id,  
                'Amount' => $this->providerCardFiction ,  
                'DCAmount' =>$this->providerCardFiction ,         
                'CurrencyID' => 1, 
                'AccountID' =>519814,  
                'CostCenterID' => null,
                'Note' => $this->body['Target'] .' ' .$this->cardfaction->Name, 
                'Datestamp' => ((int) date('Ymd')),
                'ExchangeRate' => 1.00,
                'SyncEntryID' => null
            ]);

            Log::info($this->journal->id);
            
            Log::info("transaction11");
            if ($this->countCardFiction - $this->providerCardFiction != 0) {

                $journalEntry3 = JournalEntry::create([
                    'ParentID' => $this->journal->id,
                    'Amount' => $this->countCardFiction - $this->providerCardFiction,
                    'CurrencyID' => 1,
                    'DCAmount' =>  $this->countCardFiction - $this->providerCardFiction,
                    'AccountID' =>  222839,
                    'CostCenterID' => null,
                    'Note' => 'فارق تحصيل عملية رقم' . $journalEntry1->ID,
                    'Datestamp' => ((int) date('Ymd')),
                    'ExchangeRate' => 1.00,
                    'SyncEntryID' => null
                ]);
            }
           
             Log::info("transaction11.1");
           //  Log::info($userInfo);


           
              //game = 32 payment 33

                // هنا يتم تقييد اللعبة  
               $orderInfo = OrderInfo::create([ 
                'Number'=>$this->journal->Number, 
                'ServiceCategoryID'=> 9,
                'ServiceID'=>$this->body['Target'] == 'CardOrder' ? 20029 : 2030 ,
                'OrderType'=>$this->body['Target'] == 'CardOrder' ? 'CardOrder' : 'CardPayment',
                'Amount'=>$this->providerCardFiction  , // edit mohammed
                'CurrencyID'=>1, // edit mohammed
                'CreditorAccountID'=>316581, // edit mohammed
                'MCAmount'=> $this->countCardFiction , // edit mohammed
                'DebitorAccountID'=>$this->body['Entity']['AccountID'], // edit mohammed
                'Description'=>($this->body['Target'] ). ' ' . $this->cardfaction->Name,
                'Date'=>date('Y-m-d H:i:s'), 
                'Channel'=>'Mobile',
                'Note'=>($this->body['Target'] ). ' ' . $this->cardfaction->Name, // edit mohammed
                'ExtraAmount'=>0.00, // edit mohammed
                'RefNumber'=>null,
                'IsAmountModified'=>null, // edit mohammed
                'AmountModifiedBy'=>null, // edit mohammed
                'State'=>'غير جاهز',
                'InUse'=>0,  // edit mohammed  ServiceID = this.Record.ServiceID,
                'UsageTime'=>date('Y-m-d H:i:s'),  // edit mohammed
                'BindBy'=>null, // edit mohammed
                // 'IsDebited'=>0,   // edit mohammed
                // 'DebitedBy'=>0,   // edit mohammed
                // 'IsRejected'=>null, // edit mohammed
                // 'RejectReason'=>0, 
                // 'RejectedBy'=>0,
                'EntryID'=>$this->journal->id,
                'ServiceEntryID'=>null,  // edit mohammed
                'CreatedBy'=> $userInfo->ID ,   // edit mohammed
                'BranchID'=>  $this->accountUser->BranchID,  
                'Status'=>0,
                'AccountID'=>  $tAccountID ,  // edit mohammed
                'SubNote'=>null,
                'CommissionAmount'=>0.00,
                'CommissionCurrencyID'=> 1 ,   // edit mohammed
               ]);
            
               Log::info("transaction11.2");
               $card = [ 
                'Number'=> $this->journal->Number,  // edit mohammed
                'CardFactionID'=>$this->body['Entity']['CardFactionID'],   // edit mohammed
                'CardTypeID'=>$this->body['Entity']['CardTypeID'],
                'Amount'=> $this->countCardFiction, // edit mohammed
                'CurrencyID'=>1 , // edit mohammed
                'Date'=>date('Y-m-d H:i:s'),
                'Note'=>($this->body['Target'] ). ' ' . $this->cardfaction->Name,
                'Username'=>$this->body['Entity']['Username'] ?? null,
                'Password'=>$this->body['Entity']['Password']??null,
                'Email'=>$this->body['Entity']['Email'] ??null,
                'Phone'=>$this->body['Entity']['Phone'] ?? null,
                'Channel'=>$this->body['Channel']??2,
                'EntryID'=>$this->journal->id,
                'CreditorAccountID'=>316581,
                'DebitorAccountID'=>$this->body['Entity']['AccountID'],
                'ParentID'=>$orderInfo->id,
                'ServiceID'=>$this->body['Target'] == 'CardOrder' ? 20029 : 2030,
                'AccountID'=> $tAccountID ,  
                'Status'=>0,
                'BranchID'=>  $this->accountUser->BranchID  , 
                'CreatedBy'=>  $userInfo->ID  , 
               ];
               if($this->body['Target'] == 'CardOrder'){
                $this->cardOrderOrPayment = CardOrder::create($card);
               }
               else {
                $this->cardOrderOrPayment = CardPayment::create($card);
               }



            } catch (\Exception $exception) {

                Log::info($exception->getMessage());
                // هنا يتم تصحيح الخطاء السابق

                // $errorInfo = $exception->errorInfo;
                // Log::info($exception->getMessage());
                // Log::info($errorInfo);
                 throw new Exception($exception->getMessage());
            }
            Log::info("transaction13");
        });
            
            $twasulService = new TwasulService($this->cardOrderOrPayment, $this->journal,'GAME');
            $confrm = $twasulService->confirmGame();

            return $confrm;

    }

     // update payment api
     public function updatePayment(Request $request)
     {
        Log::info("data when call api from provider 3.1S");
             
         $action = $request->query('action');
         $backpass = $request->query('backpass');
         $transid = $request->query('transid');
         $message = $request->query('message');
         Log::info('Received GET request with data:', [
             'action' => $action,
             'backpass' => $backpass,
             'transid' => $transid,
             'message' => $message,
         ]);
         $decryptedPassword =$backpass;
         Log::info($decryptedPassword);
         if ($decryptedPassword!=$transid) {
                   return 'حدث خطاء' ;
         }
 
 
 
         Log::info("data when call api from provider 3");
        
          $orderInfo = OrderInfo::where('EntryID',$transid )->first();
          Log::info("data when call api from provider 4");
          $journal = Journal::where('id',$transid )->first();
          Log::info("data when call api from provider 5");
 
          if(str_contains($action, 'ban')){ 
             $orderInfo->Status = 3;
             $orderInfo->IsDebited = 0;  
            $orderInfo->IsRejected = 1;
             // $this->orderInfo->Debited = 0;// غير موجود
             $orderInfo->Note = "العملية فاشلة";  
             $orderInfo->RejectReason = "فشل في اجراء العملية"; 
             $orderInfo->SubNote = json_encode(["provider" => $message  ]) ?? '';
             $orderInfo->save();
             $journal->Status = 0;
             $journal->Debited = 0;
             $journal->save();
 
             Log::info("data when call api from provider 6");
          }
          else{
              $orderInfo->Status = 1;
              $orderInfo->IsDebited = 1;  
            $orderInfo->IsRejected = 0;
             //  $orderInfo->RefNumber =$this->body['ref_id_provider'];
              $orderInfo->Description =json_encode([$message]);
              $orderInfo->SubNote = "العملية ناجحة";
              $orderInfo->save();
              Log::info("data when call api from provider 7");
          }
          Log::info("data when call api from provider 8");
 
          return 'تمت العملية' ;
 
     }
}
