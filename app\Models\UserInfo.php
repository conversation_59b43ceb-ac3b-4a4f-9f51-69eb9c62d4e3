<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserInfo extends Model
{
    use HasFactory;
    protected $table = 'UserInfo';
    public $timestamps = false;

    protected $fillable = [
        'ID',
        'RowVersion',
        'Number',
        'UserName',
        'Note',
        'Password',
        'Type',
        'CreatedBy',
        'BranchID',
        'CreatedTime',
        'LastLoginDate',
        'Status',
        'AccessFailedCount',
        'ConcurrencyStamp',
        'SecurityStamp',
        'TwoFactorEnabled',
        'ClaimGroupID'
    ];
}
