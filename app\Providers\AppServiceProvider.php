<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Models\Topup;
use App\Observers\TopupObserver;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        //
        $this->app->alias('bugsnag.logger', \Illuminate\Contracts\Logging\Log::class);
       $this->app->alias('bugsnag.logger', \Psr\Log\LoggerInterface::class);
       Topup::observe(TopupObserver::class);
    }
}
