[2023-03-26 00:07:24] local.INFO: header  
[2023-03-26 00:07:24] local.INFO: header after fliter  
[2023-03-26 00:07:24] local.INFO: Body  after fliter  
[2023-03-26 00:07:24] local.INFO: array (
)  
[2023-03-26 00:07:24] local.INFO: transaction14  
[2023-03-26 00:07:24] local.INFO: first inquery phone = 103330532  
[2023-03-26 01:31:07] local.INFO: header  
[2023-03-26 01:31:07] local.CRITICAL: ****************************1  
[2023-03-26 01:31:07] local.ALERT: reach here  
[2023-03-26 01:31:08] local.ERROR: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '2400',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => '2.27',
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '5448.00',
  ),
  3 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'خمسة ألف وأربعمائة وثمانية وأربعون  ر.ي.',
  ),
)  
[2023-03-26 01:31:08] local.ERROR: الكمية  
[2023-03-26 01:31:08] local.ERROR: سعر الوحدة  
[2023-03-26 01:31:08] local.ERROR: المبلغ  
[2023-03-26 01:31:08] local.ERROR: 5,448.00  
[2023-03-26 01:31:08] local.ERROR: 2,400.00  
[2023-03-26 01:31:19] local.INFO: header  
[2023-03-26 01:31:19] local.CRITICAL: ****************************1  
[2023-03-26 01:31:19] local.ALERT: reach here  
[2023-03-26 01:31:19] local.ERROR: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '2400',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => '2.27',
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '5448.00',
  ),
  3 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'خمسة ألف وأربعمائة وثمانية وأربعون  ر.ي.',
  ),
)  
[2023-03-26 01:31:19] local.ERROR: الكمية  
[2023-03-26 01:31:19] local.ERROR: سعر الوحدة  
[2023-03-26 01:31:19] local.ERROR: المبلغ  
[2023-03-26 01:31:19] local.ERROR: 5,448.00  
[2023-03-26 01:31:19] local.ERROR: 2,400.00  
[2023-03-26 01:32:52] local.INFO: header  
[2023-03-26 01:32:52] local.INFO: header after fliter  
[2023-03-26 01:32:52] local.INFO: Body  after fliter  
[2023-03-26 01:32:52] local.INFO: array (
)  
[2023-03-26 01:32:52] local.INFO: transaction14  
[2023-03-26 01:32:52] local.INFO: first inquery phone = 101034733  
[2023-03-26 01:33:27] local.INFO: header  
[2023-03-26 01:33:27] local.INFO: header after fliter  
[2023-03-26 01:33:27] local.INFO: Body  after fliter  
[2023-03-26 01:33:27] local.INFO: array (
)  
[2023-03-26 01:33:27] local.INFO: transaction14  
[2023-03-26 01:33:27] local.INFO: first inquery phone = 101034733  
[2023-03-26 01:34:05] local.INFO: header  
[2023-03-26 01:34:05] local.INFO: header after fliter  
[2023-03-26 01:34:05] local.INFO: Body  after fliter  
[2023-03-26 01:34:05] local.INFO: array (
)  
[2023-03-26 01:34:05] local.INFO: transaction14  
[2023-03-26 01:34:05] local.INFO: first inquery phone = 101034733  
[2023-03-26 01:36:12] local.INFO: header  
[2023-03-26 01:36:12] local.CRITICAL: ****************************1  
[2023-03-26 01:36:12] local.ALERT: reach here  
[2023-03-26 01:36:12] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '4000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'أربعة ألف  ر.ي.',
  ),
)  
[2023-03-26 01:36:12] local.ERROR: المبلغ  
[2023-03-26 01:36:12] local.ERROR: 4,000.00  
[2023-03-26 01:36:12] local.ERROR: مبلغ وقدرة  
[2023-03-26 01:36:12] local.ALERT: 200  
[2023-03-26 01:36:12] local.ALERT: 3  
[2023-03-26 01:36:12] local.ALERT: 200  
[2023-03-26 01:36:12] local.ALERT: 3  
[2023-03-26 01:36:12] local.ALERT: 200  
[2023-03-26 01:36:12] local.ALERT: 3  
[2023-03-26 01:36:12] local.ALERT: 200  
[2023-03-26 01:36:12] local.ALERT: 3  
[2023-03-26 01:36:12] local.ALERT: 200  
[2023-03-26 01:36:12] local.ALERT: 3  
[2023-03-26 01:36:12] local.ALERT: 200  
[2023-03-26 01:36:12] local.ALERT: 3  
[2023-03-26 01:36:12] local.ALERT: 200  
[2023-03-26 01:36:12] local.ALERT: 3  
[2023-03-26 01:36:12] local.ALERT: 200  
[2023-03-26 01:36:12] local.ALERT: 3  
[2023-03-26 01:36:12] local.ALERT: 200  
[2023-03-26 01:36:12] local.ALERT: 3  
[2023-03-26 01:36:12] local.ALERT: 200  
[2023-03-26 01:36:12] local.ALERT: 2  
[2023-03-26 01:36:12] local.ALERT: 200  
[2023-03-26 01:36:12] local.ALERT: 2  
[2023-03-26 01:36:12] local.ALERT: 200  
[2023-03-26 01:36:12] local.ALERT: 2  
[2023-03-26 01:36:12] local.ALERT: 200  
[2023-03-26 01:36:12] local.ALERT: 2  
[2023-03-26 01:36:12] local.ALERT: 200  
[2023-03-26 01:36:12] local.ALERT: 2  
[2023-03-26 01:36:12] local.ALERT: 200  
[2023-03-26 01:36:12] local.ALERT: 2  
[2023-03-26 01:36:12] local.ALERT: 200  
[2023-03-26 01:36:12] local.ALERT: 2  
[2023-03-26 01:36:12] local.ALERT: 200  
[2023-03-26 01:36:12] local.ALERT: 1  
[2023-03-26 01:36:12] local.ALERT: 200  
[2023-03-26 01:36:12] local.ALERT: 1  
[2023-03-26 01:36:12] local.ALERT: 200  
[2023-03-26 01:36:12] local.ALERT: 1  
[2023-03-26 01:36:12] local.ALERT: 200  
[2023-03-26 01:36:12] local.ALERT: 1  
[2023-03-26 01:36:12] local.ALERT: 200  
[2023-03-26 01:36:12] local.ALERT: 1  
[2023-03-26 01:36:12] local.ALERT: 200  
[2023-03-26 01:36:12] local.ALERT: 2  
[2023-03-26 01:36:12] local.ALERT: 200  
[2023-03-26 01:36:12] local.ALERT: 10013  
[2023-03-26 01:36:12] local.ALERT: 200  
[2023-03-26 01:36:12] local.ALERT: 10013  
[2023-03-26 01:36:12] local.ALERT: 200  
[2023-03-26 01:36:12] local.ALERT: 10013  
[2023-03-26 01:36:12] local.ALERT: 200  
[2023-03-26 01:36:12] local.ALERT: 10013  
[2023-03-26 01:36:12] local.ALERT: 200  
[2023-03-26 01:36:12] local.ALERT: 1  
[2023-03-26 01:36:12] local.ALERT: 200  
[2023-03-26 01:36:12] local.ALERT: 3  
[2023-03-26 01:36:12] local.ALERT: 200  
[2023-03-26 01:36:12] local.ALERT: 3  
[2023-03-26 01:36:12] local.ALERT: 200  
[2023-03-26 01:36:12] local.ALERT: 40  
[2023-03-26 01:36:12] local.ALERT: 200  
[2023-03-26 01:36:12] local.ALERT: 40  
[2023-03-26 01:36:12] local.ALERT: 200  
[2023-03-26 01:36:12] local.ALERT: 40  
[2023-03-26 01:36:12] local.ALERT: 200  
[2023-03-26 01:36:12] local.ALERT: 40  
[2023-03-26 01:36:12] local.ALERT: 200  
[2023-03-26 01:36:12] local.ALERT: 200  
[2023-03-26 01:36:12] local.WARNING: 1  
[2023-03-26 01:36:12] local.WARNING: array (
  'ID' => 86,
  'Name' => 'فئة 25 جيجا 4000 ريال',
  'ServiceID' => 200,
  'Price' => 4000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 4000.0,
)  
[2023-03-26 01:36:12] local.ALERT: 200  
[2023-03-26 01:36:12] local.ALERT: 200  
[2023-03-26 01:36:12] local.ALERT: 200  
[2023-03-26 01:36:12] local.ALERT: 200  
[2023-03-26 01:36:12] local.ALERT: 200  
[2023-03-26 01:36:12] local.ALERT: 200  
[2023-03-26 01:36:12] local.ALERT: 200  
[2023-03-26 01:36:12] local.ALERT: 200  
[2023-03-26 01:36:12] local.ALERT: 200  
[2023-03-26 01:36:12] local.ALERT: 200  
[2023-03-26 01:36:12] local.ALERT: 200  
[2023-03-26 01:36:12] local.ALERT: 40  
[2023-03-26 01:36:12] local.CRITICAL: ****************************2  
[2023-03-26 01:36:12] local.CRITICAL: ****************************  
[2023-03-26 01:36:12] local.CRITICAL:   
[2023-03-26 01:36:12] local.CRITICAL: ****************************  
[2023-03-26 01:36:13] local.INFO: {
  "ClientBalanceResult": "-34736.5700"
}  
[2023-03-26 01:36:13] local.INFO: array (
  'ClientBalanceResult' => '-34736.5700',
)  
[2023-03-26 01:36:13] local.INFO: price less than Balance  
[2023-03-26 01:46:16] local.INFO: header  
[2023-03-26 01:46:16] local.INFO: header after fliter  
[2023-03-26 01:46:16] local.INFO: Body  after fliter  
[2023-03-26 01:46:16] local.INFO: array (
)  
[2023-03-26 01:46:16] local.INFO: transaction14  
[2023-03-26 01:46:16] local.INFO: first inquery phone = 106590077  
[2023-03-26 01:46:51] local.INFO: header  
[2023-03-26 01:46:51] local.CRITICAL: ****************************1  
[2023-03-26 01:46:51] local.ALERT: reach here  
[2023-03-26 01:46:51] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '8000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ثمانية ألف  ر.ي.',
  ),
)  
[2023-03-26 01:46:51] local.ERROR: المبلغ  
[2023-03-26 01:46:51] local.ERROR: 8,000.00  
[2023-03-26 01:46:51] local.ERROR: مبلغ وقدرة  
[2023-03-26 01:46:51] local.ALERT: 200  
[2023-03-26 01:46:51] local.ALERT: 3  
[2023-03-26 01:46:51] local.ALERT: 200  
[2023-03-26 01:46:51] local.ALERT: 3  
[2023-03-26 01:46:51] local.ALERT: 200  
[2023-03-26 01:46:51] local.ALERT: 3  
[2023-03-26 01:46:51] local.ALERT: 200  
[2023-03-26 01:46:51] local.ALERT: 3  
[2023-03-26 01:46:51] local.ALERT: 200  
[2023-03-26 01:46:51] local.ALERT: 3  
[2023-03-26 01:46:51] local.ALERT: 200  
[2023-03-26 01:46:51] local.ALERT: 3  
[2023-03-26 01:46:51] local.ALERT: 200  
[2023-03-26 01:46:51] local.ALERT: 3  
[2023-03-26 01:46:51] local.ALERT: 200  
[2023-03-26 01:46:51] local.ALERT: 3  
[2023-03-26 01:46:51] local.ALERT: 200  
[2023-03-26 01:46:51] local.ALERT: 3  
[2023-03-26 01:46:51] local.ALERT: 200  
[2023-03-26 01:46:51] local.ALERT: 2  
[2023-03-26 01:46:51] local.ALERT: 200  
[2023-03-26 01:46:51] local.ALERT: 2  
[2023-03-26 01:46:51] local.ALERT: 200  
[2023-03-26 01:46:51] local.ALERT: 2  
[2023-03-26 01:46:51] local.ALERT: 200  
[2023-03-26 01:46:51] local.ALERT: 2  
[2023-03-26 01:46:51] local.ALERT: 200  
[2023-03-26 01:46:51] local.ALERT: 2  
[2023-03-26 01:46:51] local.ALERT: 200  
[2023-03-26 01:46:51] local.ALERT: 2  
[2023-03-26 01:46:51] local.ALERT: 200  
[2023-03-26 01:46:51] local.ALERT: 2  
[2023-03-26 01:46:51] local.ALERT: 200  
[2023-03-26 01:46:51] local.ALERT: 1  
[2023-03-26 01:46:51] local.ALERT: 200  
[2023-03-26 01:46:51] local.ALERT: 1  
[2023-03-26 01:46:51] local.ALERT: 200  
[2023-03-26 01:46:51] local.ALERT: 1  
[2023-03-26 01:46:51] local.ALERT: 200  
[2023-03-26 01:46:51] local.ALERT: 1  
[2023-03-26 01:46:51] local.ALERT: 200  
[2023-03-26 01:46:51] local.ALERT: 1  
[2023-03-26 01:46:51] local.ALERT: 200  
[2023-03-26 01:46:51] local.ALERT: 2  
[2023-03-26 01:46:51] local.ALERT: 200  
[2023-03-26 01:46:51] local.ALERT: 10013  
[2023-03-26 01:46:51] local.ALERT: 200  
[2023-03-26 01:46:51] local.ALERT: 10013  
[2023-03-26 01:46:51] local.ALERT: 200  
[2023-03-26 01:46:51] local.ALERT: 10013  
[2023-03-26 01:46:51] local.ALERT: 200  
[2023-03-26 01:46:51] local.ALERT: 10013  
[2023-03-26 01:46:51] local.ALERT: 200  
[2023-03-26 01:46:51] local.ALERT: 1  
[2023-03-26 01:46:51] local.ALERT: 200  
[2023-03-26 01:46:51] local.ALERT: 3  
[2023-03-26 01:46:51] local.ALERT: 200  
[2023-03-26 01:46:51] local.ALERT: 3  
[2023-03-26 01:46:51] local.ALERT: 200  
[2023-03-26 01:46:51] local.ALERT: 40  
[2023-03-26 01:46:51] local.ALERT: 200  
[2023-03-26 01:46:51] local.ALERT: 40  
[2023-03-26 01:46:51] local.ALERT: 200  
[2023-03-26 01:46:51] local.ALERT: 40  
[2023-03-26 01:46:51] local.ALERT: 200  
[2023-03-26 01:46:51] local.ALERT: 40  
[2023-03-26 01:46:51] local.ALERT: 200  
[2023-03-26 01:46:51] local.ALERT: 200  
[2023-03-26 01:46:51] local.ALERT: 200  
[2023-03-26 01:46:51] local.ALERT: 200  
[2023-03-26 01:46:51] local.WARNING: 1  
[2023-03-26 01:46:51] local.WARNING: array (
  'ID' => 87,
  'Name' => 'فئة 60 جيجا 8000 ريال',
  'ServiceID' => 200,
  'Price' => 8000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 8000.0,
)  
[2023-03-26 01:46:51] local.ALERT: 200  
[2023-03-26 01:46:51] local.ALERT: 200  
[2023-03-26 01:46:51] local.ALERT: 200  
[2023-03-26 01:46:51] local.ALERT: 200  
[2023-03-26 01:46:51] local.ALERT: 200  
[2023-03-26 01:46:51] local.ALERT: 200  
[2023-03-26 01:46:51] local.ALERT: 200  
[2023-03-26 01:46:51] local.ALERT: 200  
[2023-03-26 01:46:51] local.ALERT: 200  
[2023-03-26 01:46:51] local.ALERT: 40  
[2023-03-26 01:46:51] local.CRITICAL: ****************************2  
[2023-03-26 01:46:51] local.CRITICAL: ****************************  
[2023-03-26 01:46:51] local.CRITICAL:   
[2023-03-26 01:46:51] local.CRITICAL: ****************************  
[2023-03-26 01:46:52] local.INFO: {
  "ClientBalanceResult": "8162.2150"
}  
[2023-03-26 01:46:52] local.INFO: array (
  'ClientBalanceResult' => '8162.2150',
)  
[2023-03-26 01:46:52] local.DEBUG: lattttef  
[2023-03-26 01:46:52] local.DEBUG: array (
  'ClientBalanceResult' => '8162.2150',
)  
[2023-03-26 01:46:52] local.INFO: transaction14  
[2023-03-26 01:46:52] local.INFO: first inquery phone = 106590077  
[2023-03-26 01:47:28] local.INFO: header  
[2023-03-26 01:47:28] local.INFO: header after fliter  
[2023-03-26 01:47:28] local.INFO: Body  after fliter  
[2023-03-26 01:47:28] local.INFO: array (
)  
[2023-03-26 01:47:28] local.INFO: transaction14  
[2023-03-26 01:47:28] local.INFO: first inquery phone = 106590077  
[2023-03-26 01:48:03] local.INFO: header  
[2023-03-26 01:48:03] local.INFO: header after fliter  
[2023-03-26 01:48:03] local.INFO: Body  after fliter  
[2023-03-26 01:48:03] local.INFO: array (
)  
[2023-03-26 01:48:03] local.INFO: transaction14  
[2023-03-26 01:48:03] local.INFO: first inquery phone = 106590077  
[2023-03-26 01:48:38] local.INFO: header  
[2023-03-26 01:48:38] local.INFO: header after fliter  
[2023-03-26 01:48:38] local.INFO: Body  after fliter  
[2023-03-26 01:48:38] local.INFO: array (
)  
[2023-03-26 01:48:38] local.INFO: transaction14  
[2023-03-26 01:48:38] local.INFO: first inquery phone = 106590077  
[2023-03-26 01:49:14] local.INFO: header  
[2023-03-26 01:49:14] local.CRITICAL: ****************************1  
[2023-03-26 01:49:14] local.ALERT: reach here  
[2023-03-26 01:49:14] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '8000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ثمانية ألف  ر.ي.',
  ),
)  
[2023-03-26 01:49:14] local.ERROR: المبلغ  
[2023-03-26 01:49:14] local.ERROR: 8,000.00  
[2023-03-26 01:49:14] local.ERROR: مبلغ وقدرة  
[2023-03-26 01:49:14] local.ALERT: 200  
[2023-03-26 01:49:14] local.ALERT: 3  
[2023-03-26 01:49:14] local.ALERT: 200  
[2023-03-26 01:49:14] local.ALERT: 3  
[2023-03-26 01:49:14] local.ALERT: 200  
[2023-03-26 01:49:14] local.ALERT: 3  
[2023-03-26 01:49:14] local.ALERT: 200  
[2023-03-26 01:49:14] local.ALERT: 3  
[2023-03-26 01:49:14] local.ALERT: 200  
[2023-03-26 01:49:14] local.ALERT: 3  
[2023-03-26 01:49:14] local.ALERT: 200  
[2023-03-26 01:49:14] local.ALERT: 3  
[2023-03-26 01:49:14] local.ALERT: 200  
[2023-03-26 01:49:14] local.ALERT: 3  
[2023-03-26 01:49:14] local.ALERT: 200  
[2023-03-26 01:49:14] local.ALERT: 3  
[2023-03-26 01:49:14] local.ALERT: 200  
[2023-03-26 01:49:14] local.ALERT: 3  
[2023-03-26 01:49:14] local.ALERT: 200  
[2023-03-26 01:49:14] local.ALERT: 2  
[2023-03-26 01:49:14] local.ALERT: 200  
[2023-03-26 01:49:14] local.ALERT: 2  
[2023-03-26 01:49:14] local.ALERT: 200  
[2023-03-26 01:49:14] local.ALERT: 2  
[2023-03-26 01:49:14] local.ALERT: 200  
[2023-03-26 01:49:14] local.ALERT: 2  
[2023-03-26 01:49:14] local.ALERT: 200  
[2023-03-26 01:49:14] local.ALERT: 2  
[2023-03-26 01:49:14] local.ALERT: 200  
[2023-03-26 01:49:14] local.ALERT: 2  
[2023-03-26 01:49:14] local.ALERT: 200  
[2023-03-26 01:49:14] local.ALERT: 2  
[2023-03-26 01:49:14] local.ALERT: 200  
[2023-03-26 01:49:14] local.ALERT: 1  
[2023-03-26 01:49:14] local.ALERT: 200  
[2023-03-26 01:49:14] local.ALERT: 1  
[2023-03-26 01:49:14] local.ALERT: 200  
[2023-03-26 01:49:14] local.ALERT: 1  
[2023-03-26 01:49:14] local.ALERT: 200  
[2023-03-26 01:49:14] local.ALERT: 1  
[2023-03-26 01:49:14] local.ALERT: 200  
[2023-03-26 01:49:14] local.ALERT: 1  
[2023-03-26 01:49:14] local.ALERT: 200  
[2023-03-26 01:49:14] local.ALERT: 2  
[2023-03-26 01:49:14] local.ALERT: 200  
[2023-03-26 01:49:14] local.ALERT: 10013  
[2023-03-26 01:49:14] local.ALERT: 200  
[2023-03-26 01:49:14] local.ALERT: 10013  
[2023-03-26 01:49:14] local.ALERT: 200  
[2023-03-26 01:49:14] local.ALERT: 10013  
[2023-03-26 01:49:14] local.ALERT: 200  
[2023-03-26 01:49:14] local.ALERT: 10013  
[2023-03-26 01:49:14] local.ALERT: 200  
[2023-03-26 01:49:14] local.ALERT: 1  
[2023-03-26 01:49:14] local.ALERT: 200  
[2023-03-26 01:49:14] local.ALERT: 3  
[2023-03-26 01:49:14] local.ALERT: 200  
[2023-03-26 01:49:14] local.ALERT: 3  
[2023-03-26 01:49:14] local.ALERT: 200  
[2023-03-26 01:49:14] local.ALERT: 40  
[2023-03-26 01:49:14] local.ALERT: 200  
[2023-03-26 01:49:14] local.ALERT: 40  
[2023-03-26 01:49:14] local.ALERT: 200  
[2023-03-26 01:49:14] local.ALERT: 40  
[2023-03-26 01:49:14] local.ALERT: 200  
[2023-03-26 01:49:14] local.ALERT: 40  
[2023-03-26 01:49:14] local.ALERT: 200  
[2023-03-26 01:49:14] local.ALERT: 200  
[2023-03-26 01:49:14] local.ALERT: 200  
[2023-03-26 01:49:14] local.ALERT: 200  
[2023-03-26 01:49:14] local.WARNING: 1  
[2023-03-26 01:49:14] local.WARNING: array (
  'ID' => 87,
  'Name' => 'فئة 60 جيجا 8000 ريال',
  'ServiceID' => 200,
  'Price' => 8000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 8000.0,
)  
[2023-03-26 01:49:14] local.ALERT: 200  
[2023-03-26 01:49:14] local.ALERT: 200  
[2023-03-26 01:49:14] local.ALERT: 200  
[2023-03-26 01:49:14] local.ALERT: 200  
[2023-03-26 01:49:14] local.ALERT: 200  
[2023-03-26 01:49:14] local.ALERT: 200  
[2023-03-26 01:49:14] local.ALERT: 200  
[2023-03-26 01:49:14] local.ALERT: 200  
[2023-03-26 01:49:14] local.ALERT: 200  
[2023-03-26 01:49:14] local.ALERT: 40  
[2023-03-26 01:49:14] local.CRITICAL: ****************************2  
[2023-03-26 01:49:14] local.CRITICAL: ****************************  
[2023-03-26 01:49:14] local.CRITICAL:   
[2023-03-26 01:49:14] local.CRITICAL: ****************************  
[2023-03-26 01:49:15] local.INFO: {
  "ClientBalanceResult": "8162.2150"
}  
[2023-03-26 01:49:15] local.INFO: array (
  'ClientBalanceResult' => '8162.2150',
)  
[2023-03-26 01:49:15] local.DEBUG: lattttef  
[2023-03-26 01:49:15] local.DEBUG: array (
  'ClientBalanceResult' => '8162.2150',
)  
[2023-03-26 01:49:15] local.INFO: transaction14  
[2023-03-26 01:49:15] local.INFO: first inquery phone = 106590077  
[2023-03-26 01:49:50] local.INFO: header  
[2023-03-26 01:49:50] local.INFO: header after fliter  
[2023-03-26 01:49:50] local.INFO: Body  after fliter  
[2023-03-26 01:49:50] local.INFO: array (
)  
[2023-03-26 01:49:50] local.INFO: transaction14  
[2023-03-26 01:49:50] local.INFO: first inquery phone = 106590077  
[2023-03-26 01:50:26] local.INFO: header  
[2023-03-26 01:50:26] local.INFO: header after fliter  
[2023-03-26 01:50:26] local.INFO: Body  after fliter  
[2023-03-26 01:50:26] local.INFO: array (
)  
[2023-03-26 01:50:26] local.INFO: transaction14  
[2023-03-26 01:50:26] local.INFO: first inquery phone = 106590077  
[2023-03-26 01:53:09] local.INFO: header  
[2023-03-26 01:53:09] local.INFO: header after fliter  
[2023-03-26 01:53:09] local.INFO: Body  after fliter  
[2023-03-26 01:53:09] local.INFO: array (
)  
[2023-03-26 01:53:09] local.INFO: transaction14  
[2023-03-26 01:53:09] local.INFO: first inquery phone = 106590077  
[2023-03-26 01:53:45] local.INFO: header  
[2023-03-26 01:53:45] local.INFO: header after fliter  
[2023-03-26 01:53:45] local.INFO: Body  after fliter  
[2023-03-26 01:53:45] local.INFO: array (
)  
[2023-03-26 01:53:45] local.INFO: transaction14  
[2023-03-26 01:53:45] local.INFO: first inquery phone = 106590077  
[2023-03-26 01:54:20] local.INFO: header  
[2023-03-26 01:54:20] local.INFO: header after fliter  
[2023-03-26 01:54:20] local.INFO: Body  after fliter  
[2023-03-26 01:54:20] local.INFO: array (
)  
[2023-03-26 01:54:20] local.INFO: transaction14  
[2023-03-26 01:54:20] local.INFO: first inquery phone = 106590077  
[2023-03-26 01:55:01] local.INFO: header  
[2023-03-26 01:55:01] local.INFO: header after fliter  
[2023-03-26 01:55:01] local.INFO: Body  after fliter  
[2023-03-26 01:55:01] local.INFO: array (
)  
[2023-03-26 01:55:01] local.INFO: transaction14  
[2023-03-26 01:55:01] local.INFO: first inquery phone = 106590077  
[2023-03-26 01:55:36] local.INFO: header  
[2023-03-26 01:55:36] local.INFO: header after fliter  
[2023-03-26 01:55:36] local.INFO: Body  after fliter  
[2023-03-26 01:55:36] local.INFO: array (
)  
[2023-03-26 01:55:36] local.INFO: transaction14  
[2023-03-26 01:55:36] local.INFO: first inquery phone = 106590077  
[2023-03-26 01:56:12] local.INFO: header  
[2023-03-26 01:56:12] local.INFO: header after fliter  
[2023-03-26 01:56:12] local.INFO: Body  after fliter  
[2023-03-26 01:56:12] local.INFO: array (
)  
[2023-03-26 01:56:12] local.INFO: transaction14  
[2023-03-26 01:56:12] local.INFO: first inquery phone = 106590077  
[2023-03-26 02:12:51] local.INFO: header  
[2023-03-26 02:12:51] local.INFO: header after fliter  
[2023-03-26 02:12:51] local.INFO: Body  after fliter  
[2023-03-26 02:12:51] local.INFO: array (
)  
[2023-03-26 02:12:51] local.INFO: transaction14  
[2023-03-26 02:12:51] local.INFO: first inquery phone = 106400860  
[2023-03-26 02:15:11] local.INFO: header  
[2023-03-26 02:15:11] local.INFO: header after fliter  
[2023-03-26 02:15:11] local.INFO: Body  after fliter  
[2023-03-26 02:15:11] local.INFO: array (
)  
[2023-03-26 02:15:11] local.INFO: transaction14  
[2023-03-26 02:15:11] local.INFO: first inquery phone = *********  
[2023-03-26 02:15:46] local.INFO: header  
[2023-03-26 02:15:46] local.INFO: header after fliter  
[2023-03-26 02:15:46] local.INFO: Body  after fliter  
[2023-03-26 02:15:46] local.INFO: array (
)  
[2023-03-26 02:15:46] local.INFO: transaction14  
[2023-03-26 02:15:46] local.INFO: first inquery phone = *********  
[2023-03-26 02:16:22] local.INFO: header  
[2023-03-26 02:16:22] local.INFO: header after fliter  
[2023-03-26 02:16:22] local.INFO: Body  after fliter  
[2023-03-26 02:16:22] local.INFO: array (
)  
[2023-03-26 02:16:22] local.INFO: transaction14  
[2023-03-26 02:16:22] local.INFO: first inquery phone = *********  
[2023-03-26 02:17:27] local.INFO: header  
[2023-03-26 02:17:27] local.INFO: header after fliter  
[2023-03-26 02:17:27] local.INFO: Body  after fliter  
[2023-03-26 02:17:27] local.INFO: array (
)  
[2023-03-26 02:17:27] local.INFO: transaction14  
[2023-03-26 02:17:27] local.INFO: first inquery phone = *********  
[2023-03-26 02:19:20] local.INFO: header  
[2023-03-26 02:19:20] local.INFO: header after fliter  
[2023-03-26 02:19:20] local.INFO: Body  after fliter  
[2023-03-26 02:19:20] local.INFO: array (
)  
[2023-03-26 02:19:20] local.INFO: transaction14  
[2023-03-26 02:19:20] local.INFO: first inquery phone = *********  
[2023-03-26 02:19:55] local.INFO: header  
[2023-03-26 02:19:55] local.INFO: header after fliter  
[2023-03-26 02:19:55] local.INFO: Body  after fliter  
[2023-03-26 02:19:55] local.INFO: array (
)  
[2023-03-26 02:19:55] local.INFO: transaction14  
[2023-03-26 02:19:55] local.INFO: first inquery phone = *********  
[2023-03-26 02:20:31] local.INFO: header  
[2023-03-26 02:20:31] local.INFO: header after fliter  
[2023-03-26 02:20:31] local.INFO: Body  after fliter  
[2023-03-26 02:20:31] local.INFO: array (
)  
[2023-03-26 02:20:31] local.INFO: transaction14  
[2023-03-26 02:20:31] local.INFO: first inquery phone = *********  
[2023-03-26 02:24:49] local.INFO: header  
[2023-03-26 02:24:49] local.INFO: header after fliter  
[2023-03-26 02:24:49] local.INFO: Body  after fliter  
[2023-03-26 02:24:49] local.INFO: array (
)  
[2023-03-26 02:24:49] local.INFO: transaction14  
[2023-03-26 02:24:49] local.INFO: first inquery phone = *********  
[2023-03-26 02:25:59] local.INFO: header  
[2023-03-26 02:25:59] local.INFO: header after fliter  
[2023-03-26 02:25:59] local.INFO: Body  after fliter  
[2023-03-26 02:25:59] local.INFO: array (
)  
[2023-03-26 02:25:59] local.INFO: transaction14  
[2023-03-26 02:25:59] local.INFO: first inquery phone = *********  
[2023-03-26 02:26:35] local.INFO: header  
[2023-03-26 02:26:35] local.INFO: header after fliter  
[2023-03-26 02:26:35] local.INFO: Body  after fliter  
[2023-03-26 02:26:35] local.INFO: array (
)  
[2023-03-26 02:26:35] local.INFO: transaction14  
[2023-03-26 02:26:35] local.INFO: first inquery phone = *********  
[2023-03-26 02:27:30] local.INFO: header  
[2023-03-26 02:27:30] local.INFO: header after fliter  
[2023-03-26 02:27:30] local.INFO: Body  after fliter  
[2023-03-26 02:27:30] local.INFO: array (
)  
[2023-03-26 02:27:30] local.INFO: transaction14  
[2023-03-26 02:27:30] local.INFO: first inquery phone = *********  
[2023-03-26 02:28:05] local.INFO: header  
[2023-03-26 02:28:05] local.INFO: header after fliter  
[2023-03-26 02:28:05] local.INFO: Body  after fliter  
[2023-03-26 02:28:05] local.INFO: array (
)  
[2023-03-26 02:28:05] local.INFO: transaction14  
[2023-03-26 02:28:05] local.INFO: first inquery phone = 106323109  
[2023-03-26 02:28:41] local.INFO: header  
[2023-03-26 02:28:41] local.INFO: header after fliter  
[2023-03-26 02:28:41] local.INFO: Body  after fliter  
[2023-03-26 02:28:41] local.INFO: array (
)  
[2023-03-26 02:28:41] local.INFO: transaction14  
[2023-03-26 02:28:41] local.INFO: first inquery phone = 106323109  
[2023-03-26 02:29:16] local.INFO: header  
[2023-03-26 02:29:16] local.INFO: header after fliter  
[2023-03-26 02:29:16] local.INFO: Body  after fliter  
[2023-03-26 02:29:16] local.INFO: array (
)  
[2023-03-26 02:29:16] local.INFO: transaction14  
[2023-03-26 02:29:16] local.INFO: first inquery phone = 106323109  
[2023-03-26 02:47:36] local.INFO: header  
[2023-03-26 02:47:36] local.INFO: header after fliter  
[2023-03-26 02:47:36] local.INFO: Body  after fliter  
[2023-03-26 02:47:36] local.INFO: array (
)  
[2023-03-26 02:47:36] local.INFO: transaction14  
[2023-03-26 02:47:36] local.INFO: first inquery phone = 106590077  
[2023-03-26 02:48:12] local.INFO: header  
[2023-03-26 02:48:12] local.INFO: header after fliter  
[2023-03-26 02:48:12] local.INFO: Body  after fliter  
[2023-03-26 02:48:12] local.INFO: array (
)  
[2023-03-26 02:48:12] local.INFO: transaction14  
[2023-03-26 02:48:12] local.INFO: first inquery phone = 106590077  
[2023-03-26 02:48:47] local.INFO: header  
[2023-03-26 02:48:47] local.INFO: header after fliter  
[2023-03-26 02:48:47] local.INFO: Body  after fliter  
[2023-03-26 02:48:47] local.INFO: array (
)  
[2023-03-26 02:48:47] local.INFO: transaction14  
[2023-03-26 02:48:47] local.INFO: first inquery phone = 106590077  
[2023-03-26 03:02:12] local.INFO: header  
[2023-03-26 03:02:12] local.INFO: header after fliter  
[2023-03-26 03:02:12] local.INFO: Body  after fliter  
[2023-03-26 03:02:12] local.INFO: array (
)  
[2023-03-26 03:02:12] local.INFO: transaction14  
[2023-03-26 03:02:12] local.INFO: first inquery phone = *********  
[2023-03-26 03:02:48] local.INFO: header  
[2023-03-26 03:02:48] local.INFO: header after fliter  
[2023-03-26 03:02:48] local.INFO: Body  after fliter  
[2023-03-26 03:02:48] local.INFO: array (
)  
[2023-03-26 03:02:48] local.INFO: transaction14  
[2023-03-26 03:02:48] local.INFO: first inquery phone = *********  
[2023-03-26 03:03:23] local.INFO: header  
[2023-03-26 03:03:23] local.INFO: header after fliter  
[2023-03-26 03:03:23] local.INFO: Body  after fliter  
[2023-03-26 03:03:23] local.INFO: array (
)  
[2023-03-26 03:03:23] local.INFO: transaction14  
[2023-03-26 03:03:23] local.INFO: first inquery phone = *********  
[2023-03-26 03:04:13] local.INFO: header  
[2023-03-26 03:04:13] local.INFO: header after fliter  
[2023-03-26 03:04:13] local.INFO: Body  after fliter  
[2023-03-26 03:04:13] local.INFO: array (
)  
[2023-03-26 03:04:13] local.INFO: transaction14  
[2023-03-26 03:04:13] local.INFO: first inquery phone = 106590077  
[2023-03-26 03:04:48] local.INFO: header  
[2023-03-26 03:04:48] local.INFO: header after fliter  
[2023-03-26 03:04:48] local.INFO: Body  after fliter  
[2023-03-26 03:04:48] local.INFO: array (
)  
[2023-03-26 03:04:48] local.INFO: transaction14  
[2023-03-26 03:04:48] local.INFO: first inquery phone = 106590077  
[2023-03-26 03:05:24] local.INFO: header  
[2023-03-26 03:05:24] local.INFO: header after fliter  
[2023-03-26 03:05:24] local.INFO: Body  after fliter  
[2023-03-26 03:05:24] local.INFO: array (
)  
[2023-03-26 03:05:24] local.INFO: transaction14  
[2023-03-26 03:05:24] local.INFO: first inquery phone = 106590077  
[2023-03-26 03:12:27] local.INFO: header  
[2023-03-26 03:12:27] local.INFO: header after fliter  
[2023-03-26 03:12:27] local.INFO: Body  after fliter  
[2023-03-26 03:12:27] local.INFO: array (
)  
[2023-03-26 03:12:27] local.INFO: transaction14  
[2023-03-26 03:12:27] local.INFO: first inquery phone = 106590077  
[2023-03-26 03:13:03] local.INFO: header  
[2023-03-26 03:13:03] local.INFO: header after fliter  
[2023-03-26 03:13:03] local.INFO: Body  after fliter  
[2023-03-26 03:13:03] local.INFO: array (
)  
[2023-03-26 03:13:03] local.INFO: transaction14  
[2023-03-26 03:13:03] local.INFO: first inquery phone = 106590077  
[2023-03-26 03:13:38] local.INFO: header  
[2023-03-26 03:13:38] local.INFO: header after fliter  
[2023-03-26 03:13:38] local.INFO: Body  after fliter  
[2023-03-26 03:13:38] local.INFO: array (
)  
[2023-03-26 03:13:38] local.INFO: transaction14  
[2023-03-26 03:13:38] local.INFO: first inquery phone = 106590077  
[2023-03-26 03:19:35] local.INFO: header  
[2023-03-26 03:19:35] local.INFO: header after fliter  
[2023-03-26 03:19:35] local.INFO: Body  after fliter  
[2023-03-26 03:19:35] local.INFO: array (
)  
[2023-03-26 03:19:35] local.INFO: transaction14  
[2023-03-26 03:19:35] local.INFO: first inquery phone = *********  
[2023-03-26 03:20:11] local.INFO: header  
[2023-03-26 03:20:11] local.INFO: header after fliter  
[2023-03-26 03:20:11] local.INFO: Body  after fliter  
[2023-03-26 03:20:11] local.INFO: array (
)  
[2023-03-26 03:20:11] local.INFO: transaction14  
[2023-03-26 03:20:11] local.INFO: first inquery phone = *********  
[2023-03-26 03:20:46] local.INFO: header  
[2023-03-26 03:20:46] local.INFO: header after fliter  
[2023-03-26 03:20:46] local.INFO: Body  after fliter  
[2023-03-26 03:20:46] local.INFO: array (
)  
[2023-03-26 03:20:46] local.INFO: transaction14  
[2023-03-26 03:20:46] local.INFO: first inquery phone = *********  
[2023-03-26 03:21:55] local.INFO: header  
[2023-03-26 03:21:55] local.CRITICAL: ****************************1  
[2023-03-26 03:21:55] local.ALERT: reach here  
[2023-03-26 03:21:55] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '8000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ثمانية ألف  ر.ي.',
  ),
)  
[2023-03-26 03:21:55] local.ERROR: المبلغ  
[2023-03-26 03:21:55] local.ERROR: 8,000.00  
[2023-03-26 03:21:55] local.ERROR: مبلغ وقدرة  
[2023-03-26 03:21:55] local.ALERT: 200  
[2023-03-26 03:21:55] local.ALERT: 3  
[2023-03-26 03:21:55] local.ALERT: 200  
[2023-03-26 03:21:55] local.ALERT: 3  
[2023-03-26 03:21:55] local.ALERT: 200  
[2023-03-26 03:21:55] local.ALERT: 3  
[2023-03-26 03:21:55] local.ALERT: 200  
[2023-03-26 03:21:55] local.ALERT: 3  
[2023-03-26 03:21:55] local.ALERT: 200  
[2023-03-26 03:21:55] local.ALERT: 3  
[2023-03-26 03:21:55] local.ALERT: 200  
[2023-03-26 03:21:55] local.ALERT: 3  
[2023-03-26 03:21:55] local.ALERT: 200  
[2023-03-26 03:21:55] local.ALERT: 3  
[2023-03-26 03:21:55] local.ALERT: 200  
[2023-03-26 03:21:55] local.ALERT: 3  
[2023-03-26 03:21:55] local.ALERT: 200  
[2023-03-26 03:21:55] local.ALERT: 3  
[2023-03-26 03:21:55] local.ALERT: 200  
[2023-03-26 03:21:55] local.ALERT: 2  
[2023-03-26 03:21:55] local.ALERT: 200  
[2023-03-26 03:21:55] local.ALERT: 2  
[2023-03-26 03:21:55] local.ALERT: 200  
[2023-03-26 03:21:55] local.ALERT: 2  
[2023-03-26 03:21:55] local.ALERT: 200  
[2023-03-26 03:21:55] local.ALERT: 2  
[2023-03-26 03:21:55] local.ALERT: 200  
[2023-03-26 03:21:55] local.ALERT: 2  
[2023-03-26 03:21:55] local.ALERT: 200  
[2023-03-26 03:21:55] local.ALERT: 2  
[2023-03-26 03:21:55] local.ALERT: 200  
[2023-03-26 03:21:55] local.ALERT: 2  
[2023-03-26 03:21:55] local.ALERT: 200  
[2023-03-26 03:21:55] local.ALERT: 1  
[2023-03-26 03:21:55] local.ALERT: 200  
[2023-03-26 03:21:55] local.ALERT: 1  
[2023-03-26 03:21:55] local.ALERT: 200  
[2023-03-26 03:21:55] local.ALERT: 1  
[2023-03-26 03:21:55] local.ALERT: 200  
[2023-03-26 03:21:55] local.ALERT: 1  
[2023-03-26 03:21:55] local.ALERT: 200  
[2023-03-26 03:21:55] local.ALERT: 1  
[2023-03-26 03:21:55] local.ALERT: 200  
[2023-03-26 03:21:55] local.ALERT: 2  
[2023-03-26 03:21:55] local.ALERT: 200  
[2023-03-26 03:21:55] local.ALERT: 10013  
[2023-03-26 03:21:55] local.ALERT: 200  
[2023-03-26 03:21:55] local.ALERT: 10013  
[2023-03-26 03:21:55] local.ALERT: 200  
[2023-03-26 03:21:55] local.ALERT: 10013  
[2023-03-26 03:21:55] local.ALERT: 200  
[2023-03-26 03:21:55] local.ALERT: 10013  
[2023-03-26 03:21:55] local.ALERT: 200  
[2023-03-26 03:21:55] local.ALERT: 1  
[2023-03-26 03:21:55] local.ALERT: 200  
[2023-03-26 03:21:55] local.ALERT: 3  
[2023-03-26 03:21:55] local.ALERT: 200  
[2023-03-26 03:21:55] local.ALERT: 3  
[2023-03-26 03:21:55] local.ALERT: 200  
[2023-03-26 03:21:55] local.ALERT: 40  
[2023-03-26 03:21:55] local.ALERT: 200  
[2023-03-26 03:21:55] local.ALERT: 40  
[2023-03-26 03:21:55] local.ALERT: 200  
[2023-03-26 03:21:55] local.ALERT: 40  
[2023-03-26 03:21:55] local.ALERT: 200  
[2023-03-26 03:21:55] local.ALERT: 40  
[2023-03-26 03:21:55] local.ALERT: 200  
[2023-03-26 03:21:55] local.ALERT: 200  
[2023-03-26 03:21:55] local.ALERT: 200  
[2023-03-26 03:21:55] local.ALERT: 200  
[2023-03-26 03:21:55] local.WARNING: 1  
[2023-03-26 03:21:55] local.WARNING: array (
  'ID' => 87,
  'Name' => 'فئة 60 جيجا 8000 ريال',
  'ServiceID' => 200,
  'Price' => 8000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 8000.0,
)  
[2023-03-26 03:21:55] local.ALERT: 200  
[2023-03-26 03:21:55] local.ALERT: 200  
[2023-03-26 03:21:55] local.ALERT: 200  
[2023-03-26 03:21:55] local.ALERT: 200  
[2023-03-26 03:21:55] local.ALERT: 200  
[2023-03-26 03:21:55] local.ALERT: 200  
[2023-03-26 03:21:55] local.ALERT: 200  
[2023-03-26 03:21:55] local.ALERT: 200  
[2023-03-26 03:21:55] local.ALERT: 200  
[2023-03-26 03:21:55] local.ALERT: 40  
[2023-03-26 03:21:55] local.CRITICAL: ****************************2  
[2023-03-26 03:21:55] local.CRITICAL: ****************************  
[2023-03-26 03:21:55] local.CRITICAL:   
[2023-03-26 03:21:55] local.CRITICAL: ****************************  
[2023-03-26 03:21:57] local.INFO: {
  "ClientBalanceResult": "365608.3600"
}  
[2023-03-26 03:21:57] local.INFO: array (
  'ClientBalanceResult' => '365608.3600',
)  
[2023-03-26 03:21:57] local.DEBUG: lattttef  
[2023-03-26 03:21:57] local.DEBUG: array (
  'ClientBalanceResult' => '365608.3600',
)  
[2023-03-26 03:21:57] local.INFO: transaction14  
[2023-03-26 03:21:57] local.INFO: first inquery phone = *********  
[2023-03-26 04:39:09] local.INFO: header  
[2023-03-26 04:39:09] local.INFO: header after fliter  
[2023-03-26 04:39:09] local.INFO: Body  after fliter  
[2023-03-26 04:39:09] local.INFO: array (
)  
[2023-03-26 04:39:09] local.INFO: transaction14  
[2023-03-26 04:39:09] local.INFO: first inquery phone = *********  
[2023-03-26 04:39:12] local.DEBUG: response querySubBalance  
[2023-03-26 04:39:12] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#8,000.00#8,000.00#6.55 MB#18-04-2023#0#0##8500.00#4G 60</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-26 04:39:12] local.DEBUG: array (
  0 => 'OK',
  1 => '8,000.00',
  2 => '8,000.00',
  3 => '6.55 MB',
  4 => '18-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '8500.00',
  9 => '4G 60',
)  
[2023-03-26 04:39:12] local.DEBUG: print  before faction by provider price  
[2023-03-26 04:39:12] local.DEBUG: print  after faction by provider price  
[2023-03-26 04:39:12] local.DEBUG: فئة 60 جيجا 8000 ريال  
[2023-03-26 04:39:12] local.DEBUG: print1  
[2023-03-26 04:39:12] local.DEBUG: print  2  
[2023-03-26 04:39:26] local.INFO: header  
[2023-03-26 04:39:26] local.CRITICAL: ****************************1  
[2023-03-26 04:39:26] local.ALERT: reach here  
[2023-03-26 04:39:26] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '8000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ثمانية ألف  ر.ي.',
  ),
)  
[2023-03-26 04:39:26] local.ERROR: المبلغ  
[2023-03-26 04:39:26] local.ERROR: 8,000.00  
[2023-03-26 04:39:26] local.ERROR: مبلغ وقدرة  
[2023-03-26 04:39:26] local.ALERT: 200  
[2023-03-26 04:39:26] local.ALERT: 3  
[2023-03-26 04:39:26] local.ALERT: 200  
[2023-03-26 04:39:26] local.ALERT: 3  
[2023-03-26 04:39:26] local.ALERT: 200  
[2023-03-26 04:39:26] local.ALERT: 3  
[2023-03-26 04:39:26] local.ALERT: 200  
[2023-03-26 04:39:26] local.ALERT: 3  
[2023-03-26 04:39:26] local.ALERT: 200  
[2023-03-26 04:39:26] local.ALERT: 3  
[2023-03-26 04:39:26] local.ALERT: 200  
[2023-03-26 04:39:26] local.ALERT: 3  
[2023-03-26 04:39:26] local.ALERT: 200  
[2023-03-26 04:39:26] local.ALERT: 3  
[2023-03-26 04:39:26] local.ALERT: 200  
[2023-03-26 04:39:26] local.ALERT: 3  
[2023-03-26 04:39:26] local.ALERT: 200  
[2023-03-26 04:39:26] local.ALERT: 3  
[2023-03-26 04:39:26] local.ALERT: 200  
[2023-03-26 04:39:26] local.ALERT: 2  
[2023-03-26 04:39:26] local.ALERT: 200  
[2023-03-26 04:39:26] local.ALERT: 2  
[2023-03-26 04:39:26] local.ALERT: 200  
[2023-03-26 04:39:26] local.ALERT: 2  
[2023-03-26 04:39:26] local.ALERT: 200  
[2023-03-26 04:39:26] local.ALERT: 2  
[2023-03-26 04:39:26] local.ALERT: 200  
[2023-03-26 04:39:26] local.ALERT: 2  
[2023-03-26 04:39:26] local.ALERT: 200  
[2023-03-26 04:39:26] local.ALERT: 2  
[2023-03-26 04:39:26] local.ALERT: 200  
[2023-03-26 04:39:26] local.ALERT: 2  
[2023-03-26 04:39:26] local.ALERT: 200  
[2023-03-26 04:39:26] local.ALERT: 1  
[2023-03-26 04:39:26] local.ALERT: 200  
[2023-03-26 04:39:26] local.ALERT: 1  
[2023-03-26 04:39:26] local.ALERT: 200  
[2023-03-26 04:39:26] local.ALERT: 1  
[2023-03-26 04:39:26] local.ALERT: 200  
[2023-03-26 04:39:26] local.ALERT: 1  
[2023-03-26 04:39:26] local.ALERT: 200  
[2023-03-26 04:39:26] local.ALERT: 1  
[2023-03-26 04:39:26] local.ALERT: 200  
[2023-03-26 04:39:26] local.ALERT: 2  
[2023-03-26 04:39:26] local.ALERT: 200  
[2023-03-26 04:39:26] local.ALERT: 10013  
[2023-03-26 04:39:26] local.ALERT: 200  
[2023-03-26 04:39:26] local.ALERT: 10013  
[2023-03-26 04:39:26] local.ALERT: 200  
[2023-03-26 04:39:26] local.ALERT: 10013  
[2023-03-26 04:39:26] local.ALERT: 200  
[2023-03-26 04:39:26] local.ALERT: 10013  
[2023-03-26 04:39:26] local.ALERT: 200  
[2023-03-26 04:39:26] local.ALERT: 1  
[2023-03-26 04:39:26] local.ALERT: 200  
[2023-03-26 04:39:26] local.ALERT: 3  
[2023-03-26 04:39:26] local.ALERT: 200  
[2023-03-26 04:39:26] local.ALERT: 3  
[2023-03-26 04:39:26] local.ALERT: 200  
[2023-03-26 04:39:26] local.ALERT: 40  
[2023-03-26 04:39:26] local.ALERT: 200  
[2023-03-26 04:39:26] local.ALERT: 40  
[2023-03-26 04:39:26] local.ALERT: 200  
[2023-03-26 04:39:26] local.ALERT: 40  
[2023-03-26 04:39:26] local.ALERT: 200  
[2023-03-26 04:39:26] local.ALERT: 40  
[2023-03-26 04:39:26] local.ALERT: 200  
[2023-03-26 04:39:26] local.ALERT: 200  
[2023-03-26 04:39:26] local.ALERT: 200  
[2023-03-26 04:39:26] local.ALERT: 200  
[2023-03-26 04:39:26] local.WARNING: 1  
[2023-03-26 04:39:26] local.WARNING: array (
  'ID' => 87,
  'Name' => 'فئة 60 جيجا 8000 ريال',
  'ServiceID' => 200,
  'Price' => 8000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 8000.0,
)  
[2023-03-26 04:39:26] local.ALERT: 200  
[2023-03-26 04:39:26] local.ALERT: 200  
[2023-03-26 04:39:26] local.ALERT: 200  
[2023-03-26 04:39:26] local.ALERT: 200  
[2023-03-26 04:39:26] local.ALERT: 200  
[2023-03-26 04:39:26] local.ALERT: 200  
[2023-03-26 04:39:26] local.ALERT: 200  
[2023-03-26 04:39:26] local.ALERT: 200  
[2023-03-26 04:39:26] local.ALERT: 200  
[2023-03-26 04:39:26] local.ALERT: 40  
[2023-03-26 04:39:26] local.CRITICAL: ****************************2  
[2023-03-26 04:39:26] local.CRITICAL: ****************************  
[2023-03-26 04:39:26] local.CRITICAL:   
[2023-03-26 04:39:26] local.CRITICAL: ****************************  
[2023-03-26 04:39:27] local.INFO: {
  "ClientBalanceResult": "365130.3600"
}  
[2023-03-26 04:39:27] local.INFO: array (
  'ClientBalanceResult' => '365130.3600',
)  
[2023-03-26 04:39:27] local.DEBUG: lattttef  
[2023-03-26 04:39:27] local.DEBUG: array (
  'ClientBalanceResult' => '365130.3600',
)  
[2023-03-26 04:39:27] local.INFO: transaction14  
[2023-03-26 04:39:27] local.INFO: first inquery phone = *********  
[2023-03-26 04:39:30] local.DEBUG: response querySubBalance  
[2023-03-26 04:39:30] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#8,000.00#8,000.00#6.55 MB#18-04-2023#0#0##8500.00#4G 60</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-26 04:39:30] local.DEBUG: array (
  0 => 'OK',
  1 => '8,000.00',
  2 => '8,000.00',
  3 => '6.55 MB',
  4 => '18-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '8500.00',
  9 => '4G 60',
)  
[2023-03-26 04:39:30] local.DEBUG: print  before faction by provider price  
[2023-03-26 04:39:30] local.DEBUG: print  after faction by provider price  
[2023-03-26 04:39:30] local.DEBUG: فئة 60 جيجا 8000 ريال  
[2023-03-26 04:39:30] local.DEBUG: print1  
[2023-03-26 04:39:30] local.DEBUG: print  2  
[2023-03-26 04:39:30] local.INFO: transaction1  
[2023-03-26 04:39:30] local.INFO: transaction2  
[2023-03-26 04:39:30] local.INFO: transaction3  
[2023-03-26 04:39:30] local.INFO: transaction4  
[2023-03-26 04:39:30] local.INFO: transaction4  
[2023-03-26 04:39:30] local.INFO: transaction5  
[2023-03-26 04:39:30] local.INFO: transaction6  
[2023-03-26 04:39:30] local.INFO: transaction7  
[2023-03-26 04:39:30] local.DEBUG: array (
  'AMT' => 8000.0,
  'CType' => 0,
  'FID' => 87,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '*********',
  'State' => 0,
  'lateflog' => '337620',
)  
[2023-03-26 04:39:30] local.INFO: transaction8  
[2023-03-26 04:39:31] local.INFO: transaction9  
[2023-03-26 04:39:31] local.INFO: transaction10  
[2023-03-26 04:39:31] local.INFO: transaction11  
[2023-03-26 04:39:31] local.INFO: 12  
[2023-03-26 04:39:31] local.INFO: transaction13  
[2023-03-26 04:39:31] local.INFO: transaction14  
[2023-03-26 04:39:31] local.INFO: transaction19  
[2023-03-26 04:39:31] local.INFO: transaction15  
[2023-03-26 04:39:31] local.INFO: transaction16  
[2023-03-26 04:39:31] local.INFO: 98#*********#8000.00#0  
[2023-03-26 04:39:31] local.INFO: transaction18  
[2023-03-26 04:39:31] local.INFO: array (
  0 => 'Error',
  1 => '9',
  2 => 'Payment was failed  - 5521',
)  
[2023-03-26 04:39:32] local.DEBUG: {"Number":********,"ServiceID":200,"NetworkID":null,"SubscriberNumber":"*********","Amount":8000,"FactionID":87,"RegionID":null,"LineType":"\u063a\u064a\u0631 \u0645\u0639\u0631\u0648\u0641","Date":"2023-03-26 04:39:31","Status":0,"Note":"\u0627\u0644\u0639\u0645\u0644\u064a\u0629 \u0641\u0627\u0634\u0644\u0629","CreditorAccountID":379015,"CurrencyID":1,"DebitorAccountID":"337620","AgentID":null,"RefNumber":null,"TransactionID":********,"ProviderID":10028,"EntryID":4400648,"PaymentEntryID":null,"Channel":2,"CreatedBy":"335360","BranchBy":null,"CreatedTime":"2023-03-26 04:39:31","BranchID":"1","ProviderRM":"\u0641\u0634\u0644 \u0641\u064a \u0627\u062c\u0631\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629","ProviderPrice":"8000.00","SubNote":null,"Datestamb":"********","UniqueNo":"********043931","Quantity":"8000.0000","UnitPrice":1,"UnitCost":1,"CostAmount":"8000.00","DifferentialAmount":0,"CommissionAmount":0,"Discount":0,"TotalCost":8000,"TotalAmount":8000,"Profits":0,"Method":2,"Type":0,"Class":0,"LType":"1","OperatorID":1,"AppTechApi":0,"BillNumber":"20000********","BillState":0,"Debited":0,"ByChild":0,"IsDirect":1,"BundleName":"\u0641\u0626\u0629 60 \u062c\u064a\u062c\u0627 8000 \u0631\u064a\u0627\u0644","BundleCode":"30","ExCode":null,"TransNumber":"********043931","OperationID":0,"AccountID":"337620","State":0,"StateClass":"\u0627\u0644\u0639\u0645\u0644\u064a\u0629 \u0641\u0627\u0634\u0644\u0629","Identifier":"b388e55bb399bca5","AdminNote":"\u0641\u0634\u0644 \u0641\u064a \u0627\u062c\u0631\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629 \u062a\u0645 \u0627\u0644\u063a\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629","AccountNote":"\u0641\u0634\u0644 \u0641\u064a \u0627\u062c\u0631\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629 \u062a\u0645 \u0627\u0644\u063a\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629","Description":null,"Responded":1,"RequestInfo":"200#*********#8000#87","ResponseTime":"2023-03-26 04:39:31","ResponseStatus":0,"ExecutionPeroid":"31","FaildRequest":0,"FailedReason":"\u0641\u0634\u0644 \u0641\u064a \u0627\u062c\u0631\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629","FailedType":0,"Cured":0,"CuredBy":null,"CuredInfo":null,"InspectInfo":null,"Flag":2,"Action":0,"QuotaionID":0,"SyncID":0,"id":4223818,"ResponseInfo":"Error#9#Payment was failed  - 5521"}  
[2023-03-26 04:39:32] local.DEBUG: response faild  
[2023-03-26 04:39:32] local.DEBUG: validateee  
[2023-03-26 04:39:32] local.DEBUG: response faild  
[2023-03-26 04:39:39] local.INFO: header  
[2023-03-26 04:39:39] local.CRITICAL: ****************************1  
[2023-03-26 04:39:39] local.ALERT: reach here  
[2023-03-26 04:39:39] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '8000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ثمانية ألف  ر.ي.',
  ),
)  
[2023-03-26 04:39:39] local.ERROR: المبلغ  
[2023-03-26 04:39:39] local.ERROR: 8,000.00  
[2023-03-26 04:39:39] local.ERROR: مبلغ وقدرة  
[2023-03-26 04:39:39] local.ALERT: 200  
[2023-03-26 04:39:39] local.ALERT: 3  
[2023-03-26 04:39:39] local.ALERT: 200  
[2023-03-26 04:39:39] local.ALERT: 3  
[2023-03-26 04:39:39] local.ALERT: 200  
[2023-03-26 04:39:39] local.ALERT: 3  
[2023-03-26 04:39:39] local.ALERT: 200  
[2023-03-26 04:39:39] local.ALERT: 3  
[2023-03-26 04:39:39] local.ALERT: 200  
[2023-03-26 04:39:39] local.ALERT: 3  
[2023-03-26 04:39:39] local.ALERT: 200  
[2023-03-26 04:39:39] local.ALERT: 3  
[2023-03-26 04:39:39] local.ALERT: 200  
[2023-03-26 04:39:39] local.ALERT: 3  
[2023-03-26 04:39:39] local.ALERT: 200  
[2023-03-26 04:39:39] local.ALERT: 3  
[2023-03-26 04:39:39] local.ALERT: 200  
[2023-03-26 04:39:39] local.ALERT: 3  
[2023-03-26 04:39:39] local.ALERT: 200  
[2023-03-26 04:39:39] local.ALERT: 2  
[2023-03-26 04:39:39] local.ALERT: 200  
[2023-03-26 04:39:39] local.ALERT: 2  
[2023-03-26 04:39:39] local.ALERT: 200  
[2023-03-26 04:39:39] local.ALERT: 2  
[2023-03-26 04:39:39] local.ALERT: 200  
[2023-03-26 04:39:39] local.ALERT: 2  
[2023-03-26 04:39:39] local.ALERT: 200  
[2023-03-26 04:39:39] local.ALERT: 2  
[2023-03-26 04:39:39] local.ALERT: 200  
[2023-03-26 04:39:39] local.ALERT: 2  
[2023-03-26 04:39:39] local.ALERT: 200  
[2023-03-26 04:39:39] local.ALERT: 2  
[2023-03-26 04:39:39] local.ALERT: 200  
[2023-03-26 04:39:39] local.ALERT: 1  
[2023-03-26 04:39:39] local.ALERT: 200  
[2023-03-26 04:39:39] local.ALERT: 1  
[2023-03-26 04:39:39] local.ALERT: 200  
[2023-03-26 04:39:39] local.ALERT: 1  
[2023-03-26 04:39:39] local.ALERT: 200  
[2023-03-26 04:39:39] local.ALERT: 1  
[2023-03-26 04:39:39] local.ALERT: 200  
[2023-03-26 04:39:39] local.ALERT: 1  
[2023-03-26 04:39:39] local.ALERT: 200  
[2023-03-26 04:39:39] local.ALERT: 2  
[2023-03-26 04:39:39] local.ALERT: 200  
[2023-03-26 04:39:39] local.ALERT: 10013  
[2023-03-26 04:39:39] local.ALERT: 200  
[2023-03-26 04:39:39] local.ALERT: 10013  
[2023-03-26 04:39:39] local.ALERT: 200  
[2023-03-26 04:39:39] local.ALERT: 10013  
[2023-03-26 04:39:39] local.ALERT: 200  
[2023-03-26 04:39:39] local.ALERT: 10013  
[2023-03-26 04:39:39] local.ALERT: 200  
[2023-03-26 04:39:39] local.ALERT: 1  
[2023-03-26 04:39:39] local.ALERT: 200  
[2023-03-26 04:39:39] local.ALERT: 3  
[2023-03-26 04:39:39] local.ALERT: 200  
[2023-03-26 04:39:39] local.ALERT: 3  
[2023-03-26 04:39:39] local.ALERT: 200  
[2023-03-26 04:39:39] local.ALERT: 40  
[2023-03-26 04:39:39] local.ALERT: 200  
[2023-03-26 04:39:39] local.ALERT: 40  
[2023-03-26 04:39:39] local.ALERT: 200  
[2023-03-26 04:39:39] local.ALERT: 40  
[2023-03-26 04:39:39] local.ALERT: 200  
[2023-03-26 04:39:39] local.ALERT: 40  
[2023-03-26 04:39:39] local.ALERT: 200  
[2023-03-26 04:39:39] local.ALERT: 200  
[2023-03-26 04:39:39] local.ALERT: 200  
[2023-03-26 04:39:39] local.ALERT: 200  
[2023-03-26 04:39:39] local.WARNING: 1  
[2023-03-26 04:39:39] local.WARNING: array (
  'ID' => 87,
  'Name' => 'فئة 60 جيجا 8000 ريال',
  'ServiceID' => 200,
  'Price' => 8000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 8000.0,
)  
[2023-03-26 04:39:39] local.ALERT: 200  
[2023-03-26 04:39:39] local.ALERT: 200  
[2023-03-26 04:39:39] local.ALERT: 200  
[2023-03-26 04:39:39] local.ALERT: 200  
[2023-03-26 04:39:39] local.ALERT: 200  
[2023-03-26 04:39:39] local.ALERT: 200  
[2023-03-26 04:39:39] local.ALERT: 200  
[2023-03-26 04:39:39] local.ALERT: 200  
[2023-03-26 04:39:39] local.ALERT: 200  
[2023-03-26 04:39:39] local.ALERT: 40  
[2023-03-26 04:39:39] local.CRITICAL: ****************************2  
[2023-03-26 04:39:39] local.CRITICAL: ****************************  
[2023-03-26 04:39:39] local.CRITICAL:   
[2023-03-26 04:39:39] local.CRITICAL: ****************************  
[2023-03-26 04:39:40] local.INFO: {
  "ClientBalanceResult": "365130.3600"
}  
[2023-03-26 04:39:40] local.INFO: array (
  'ClientBalanceResult' => '365130.3600',
)  
[2023-03-26 04:39:40] local.DEBUG: lattttef  
[2023-03-26 04:39:40] local.DEBUG: array (
  'ClientBalanceResult' => '365130.3600',
)  
[2023-03-26 04:39:40] local.INFO: transaction14  
[2023-03-26 04:39:40] local.INFO: first inquery phone = *********  
[2023-03-26 04:39:42] local.DEBUG: response querySubBalance  
[2023-03-26 04:39:42] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#8,000.00#8,000.00#6.55 MB#18-04-2023#0#0##8500.00#4G 60</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-26 04:39:42] local.DEBUG: array (
  0 => 'OK',
  1 => '8,000.00',
  2 => '8,000.00',
  3 => '6.55 MB',
  4 => '18-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '8500.00',
  9 => '4G 60',
)  
[2023-03-26 04:39:42] local.DEBUG: print  before faction by provider price  
[2023-03-26 04:39:42] local.DEBUG: print  after faction by provider price  
[2023-03-26 04:39:42] local.DEBUG: فئة 60 جيجا 8000 ريال  
[2023-03-26 04:39:42] local.DEBUG: print1  
[2023-03-26 04:39:42] local.DEBUG: print  2  
[2023-03-26 04:39:42] local.INFO: transaction1  
[2023-03-26 04:39:42] local.INFO: transaction2  
[2023-03-26 04:39:42] local.INFO: transaction3  
[2023-03-26 04:39:42] local.INFO: transaction4  
[2023-03-26 04:39:42] local.INFO: transaction4  
[2023-03-26 04:39:42] local.INFO: transaction5  
[2023-03-26 04:39:42] local.INFO: transaction6  
[2023-03-26 04:39:42] local.INFO: transaction7  
[2023-03-26 04:39:42] local.DEBUG: array (
  'AMT' => 8000.0,
  'CType' => 0,
  'FID' => 87,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '*********',
  'State' => 0,
  'lateflog' => '337620',
)  
[2023-03-26 04:39:42] local.INFO: transaction8  
[2023-03-26 04:39:43] local.INFO: transaction9  
[2023-03-26 04:39:43] local.INFO: transaction10  
[2023-03-26 04:39:43] local.INFO: transaction11  
[2023-03-26 04:39:43] local.INFO: 12  
[2023-03-26 04:39:43] local.INFO: transaction13  
[2023-03-26 04:39:43] local.INFO: transaction14  
[2023-03-26 04:39:43] local.INFO: transaction19  
[2023-03-26 04:39:43] local.INFO: transaction15  
[2023-03-26 04:39:43] local.INFO: transaction16  
[2023-03-26 04:39:43] local.INFO: 98#*********#8000.00#0  
[2023-03-26 04:39:44] local.INFO: transaction18  
[2023-03-26 04:39:44] local.INFO: array (
  0 => 'Error',
  1 => '9',
  2 => 'Payment was failed  - 5521',
)  
[2023-03-26 04:39:44] local.DEBUG: {"Number":********,"ServiceID":200,"NetworkID":null,"SubscriberNumber":"*********","Amount":8000,"FactionID":87,"RegionID":null,"LineType":"\u063a\u064a\u0631 \u0645\u0639\u0631\u0648\u0641","Date":"2023-03-26 04:39:43","Status":0,"Note":"\u0627\u0644\u0639\u0645\u0644\u064a\u0629 \u0641\u0627\u0634\u0644\u0629","CreditorAccountID":379015,"CurrencyID":1,"DebitorAccountID":"337620","AgentID":null,"RefNumber":null,"TransactionID":********,"ProviderID":10028,"EntryID":4400649,"PaymentEntryID":null,"Channel":2,"CreatedBy":"335360","BranchBy":null,"CreatedTime":"2023-03-26 04:39:43","BranchID":"1","ProviderRM":"\u0641\u0634\u0644 \u0641\u064a \u0627\u062c\u0631\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629","ProviderPrice":"8000.00","SubNote":null,"Datestamb":"********","UniqueNo":"********043943","Quantity":"8000.0000","UnitPrice":1,"UnitCost":1,"CostAmount":"8000.00","DifferentialAmount":0,"CommissionAmount":0,"Discount":0,"TotalCost":8000,"TotalAmount":8000,"Profits":0,"Method":2,"Type":0,"Class":0,"LType":"1","OperatorID":1,"AppTechApi":0,"BillNumber":"*************","BillState":0,"Debited":0,"ByChild":0,"IsDirect":1,"BundleName":"\u0641\u0626\u0629 60 \u062c\u064a\u062c\u0627 8000 \u0631\u064a\u0627\u0644","BundleCode":"30","ExCode":null,"TransNumber":"********043943","OperationID":0,"AccountID":"337620","State":0,"StateClass":"\u0627\u0644\u0639\u0645\u0644\u064a\u0629 \u0641\u0627\u0634\u0644\u0629","Identifier":"b388e55bb399bca5","AdminNote":"\u0641\u0634\u0644 \u0641\u064a \u0627\u062c\u0631\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629 \u062a\u0645 \u0627\u0644\u063a\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629","AccountNote":"\u0641\u0634\u0644 \u0641\u064a \u0627\u062c\u0631\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629 \u062a\u0645 \u0627\u0644\u063a\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629","Description":null,"Responded":1,"RequestInfo":"200#*********#8000#87","ResponseTime":"2023-03-26 04:39:44","ResponseStatus":0,"ExecutionPeroid":"44","FaildRequest":0,"FailedReason":"\u0641\u0634\u0644 \u0641\u064a \u0627\u062c\u0631\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629","FailedType":0,"Cured":0,"CuredBy":null,"CuredInfo":null,"InspectInfo":null,"Flag":2,"Action":0,"QuotaionID":0,"SyncID":0,"id":4223820,"ResponseInfo":"Error#9#Payment was failed  - 5521"}  
[2023-03-26 04:39:44] local.DEBUG: response faild  
[2023-03-26 04:39:44] local.DEBUG: validateee  
[2023-03-26 04:39:44] local.DEBUG: response faild  
[2023-03-26 04:40:37] local.INFO: header  
[2023-03-26 04:40:37] local.CRITICAL: ****************************1  
[2023-03-26 04:40:37] local.ALERT: reach here  
[2023-03-26 04:40:37] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '8000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ثمانية ألف  ر.ي.',
  ),
)  
[2023-03-26 04:40:37] local.ERROR: المبلغ  
[2023-03-26 04:40:37] local.ERROR: 8,000.00  
[2023-03-26 04:40:37] local.ERROR: مبلغ وقدرة  
[2023-03-26 04:40:37] local.ALERT: 200  
[2023-03-26 04:40:37] local.ALERT: 3  
[2023-03-26 04:40:37] local.ALERT: 200  
[2023-03-26 04:40:37] local.ALERT: 3  
[2023-03-26 04:40:37] local.ALERT: 200  
[2023-03-26 04:40:37] local.ALERT: 3  
[2023-03-26 04:40:37] local.ALERT: 200  
[2023-03-26 04:40:37] local.ALERT: 3  
[2023-03-26 04:40:37] local.ALERT: 200  
[2023-03-26 04:40:37] local.ALERT: 3  
[2023-03-26 04:40:37] local.ALERT: 200  
[2023-03-26 04:40:37] local.ALERT: 3  
[2023-03-26 04:40:37] local.ALERT: 200  
[2023-03-26 04:40:37] local.ALERT: 3  
[2023-03-26 04:40:37] local.ALERT: 200  
[2023-03-26 04:40:37] local.ALERT: 3  
[2023-03-26 04:40:37] local.ALERT: 200  
[2023-03-26 04:40:37] local.ALERT: 3  
[2023-03-26 04:40:37] local.ALERT: 200  
[2023-03-26 04:40:37] local.ALERT: 2  
[2023-03-26 04:40:37] local.ALERT: 200  
[2023-03-26 04:40:37] local.ALERT: 2  
[2023-03-26 04:40:37] local.ALERT: 200  
[2023-03-26 04:40:37] local.ALERT: 2  
[2023-03-26 04:40:37] local.ALERT: 200  
[2023-03-26 04:40:37] local.ALERT: 2  
[2023-03-26 04:40:37] local.ALERT: 200  
[2023-03-26 04:40:37] local.ALERT: 2  
[2023-03-26 04:40:37] local.ALERT: 200  
[2023-03-26 04:40:37] local.ALERT: 2  
[2023-03-26 04:40:37] local.ALERT: 200  
[2023-03-26 04:40:37] local.ALERT: 2  
[2023-03-26 04:40:37] local.ALERT: 200  
[2023-03-26 04:40:37] local.ALERT: 1  
[2023-03-26 04:40:37] local.ALERT: 200  
[2023-03-26 04:40:37] local.ALERT: 1  
[2023-03-26 04:40:37] local.ALERT: 200  
[2023-03-26 04:40:37] local.ALERT: 1  
[2023-03-26 04:40:37] local.ALERT: 200  
[2023-03-26 04:40:37] local.ALERT: 1  
[2023-03-26 04:40:37] local.ALERT: 200  
[2023-03-26 04:40:37] local.ALERT: 1  
[2023-03-26 04:40:37] local.ALERT: 200  
[2023-03-26 04:40:37] local.ALERT: 2  
[2023-03-26 04:40:37] local.ALERT: 200  
[2023-03-26 04:40:37] local.ALERT: 10013  
[2023-03-26 04:40:37] local.ALERT: 200  
[2023-03-26 04:40:37] local.ALERT: 10013  
[2023-03-26 04:40:37] local.ALERT: 200  
[2023-03-26 04:40:37] local.ALERT: 10013  
[2023-03-26 04:40:37] local.ALERT: 200  
[2023-03-26 04:40:37] local.ALERT: 10013  
[2023-03-26 04:40:37] local.ALERT: 200  
[2023-03-26 04:40:37] local.ALERT: 1  
[2023-03-26 04:40:37] local.ALERT: 200  
[2023-03-26 04:40:37] local.ALERT: 3  
[2023-03-26 04:40:37] local.ALERT: 200  
[2023-03-26 04:40:37] local.ALERT: 3  
[2023-03-26 04:40:37] local.ALERT: 200  
[2023-03-26 04:40:37] local.ALERT: 40  
[2023-03-26 04:40:37] local.ALERT: 200  
[2023-03-26 04:40:37] local.ALERT: 40  
[2023-03-26 04:40:37] local.ALERT: 200  
[2023-03-26 04:40:37] local.ALERT: 40  
[2023-03-26 04:40:37] local.ALERT: 200  
[2023-03-26 04:40:37] local.ALERT: 40  
[2023-03-26 04:40:37] local.ALERT: 200  
[2023-03-26 04:40:37] local.ALERT: 200  
[2023-03-26 04:40:37] local.ALERT: 200  
[2023-03-26 04:40:37] local.ALERT: 200  
[2023-03-26 04:40:37] local.WARNING: 1  
[2023-03-26 04:40:37] local.WARNING: array (
  'ID' => 87,
  'Name' => 'فئة 60 جيجا 8000 ريال',
  'ServiceID' => 200,
  'Price' => 8000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 8000.0,
)  
[2023-03-26 04:40:37] local.ALERT: 200  
[2023-03-26 04:40:37] local.ALERT: 200  
[2023-03-26 04:40:37] local.ALERT: 200  
[2023-03-26 04:40:37] local.ALERT: 200  
[2023-03-26 04:40:37] local.ALERT: 200  
[2023-03-26 04:40:37] local.ALERT: 200  
[2023-03-26 04:40:37] local.ALERT: 200  
[2023-03-26 04:40:37] local.ALERT: 200  
[2023-03-26 04:40:37] local.ALERT: 200  
[2023-03-26 04:40:37] local.ALERT: 40  
[2023-03-26 04:40:37] local.CRITICAL: ****************************2  
[2023-03-26 04:40:37] local.CRITICAL: ****************************  
[2023-03-26 04:40:37] local.CRITICAL:   
[2023-03-26 04:40:37] local.CRITICAL: ****************************  
[2023-03-26 04:40:38] local.INFO: {
  "ClientBalanceResult": "365130.3600"
}  
[2023-03-26 04:40:38] local.INFO: array (
  'ClientBalanceResult' => '365130.3600',
)  
[2023-03-26 04:40:38] local.DEBUG: lattttef  
[2023-03-26 04:40:38] local.DEBUG: array (
  'ClientBalanceResult' => '365130.3600',
)  
[2023-03-26 04:40:38] local.INFO: transaction14  
[2023-03-26 04:40:38] local.INFO: first inquery phone = *********  
[2023-03-26 04:40:41] local.DEBUG: response querySubBalance  
[2023-03-26 04:40:41] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#8,000.00#8,000.00#6.55 MB#18-04-2023#0#0##8500.00#4G 60</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-26 04:40:41] local.DEBUG: array (
  0 => 'OK',
  1 => '8,000.00',
  2 => '8,000.00',
  3 => '6.55 MB',
  4 => '18-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '8500.00',
  9 => '4G 60',
)  
[2023-03-26 04:40:41] local.DEBUG: print  before faction by provider price  
[2023-03-26 04:40:41] local.DEBUG: print  after faction by provider price  
[2023-03-26 04:40:41] local.DEBUG: فئة 60 جيجا 8000 ريال  
[2023-03-26 04:40:41] local.DEBUG: print1  
[2023-03-26 04:40:41] local.DEBUG: print  2  
[2023-03-26 04:40:41] local.INFO: transaction1  
[2023-03-26 04:40:41] local.INFO: transaction2  
[2023-03-26 04:40:41] local.INFO: transaction3  
[2023-03-26 04:40:41] local.INFO: transaction4  
[2023-03-26 04:40:41] local.INFO: transaction4  
[2023-03-26 04:40:41] local.INFO: transaction5  
[2023-03-26 04:40:41] local.INFO: transaction6  
[2023-03-26 04:40:41] local.INFO: transaction7  
[2023-03-26 04:40:41] local.DEBUG: array (
  'AMT' => 8000.0,
  'CType' => 0,
  'FID' => 87,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '*********',
  'State' => 0,
  'lateflog' => '337620',
)  
[2023-03-26 04:40:41] local.INFO: transaction8  
[2023-03-26 04:40:42] local.INFO: transaction9  
[2023-03-26 04:40:42] local.INFO: transaction10  
[2023-03-26 04:40:42] local.INFO: transaction11  
[2023-03-26 04:40:42] local.INFO: 12  
[2023-03-26 04:40:42] local.INFO: transaction13  
[2023-03-26 04:40:42] local.INFO: transaction14  
[2023-03-26 04:40:42] local.INFO: transaction19  
[2023-03-26 04:40:42] local.INFO: transaction15  
[2023-03-26 04:40:42] local.INFO: transaction16  
[2023-03-26 04:40:42] local.INFO: 98#*********#8000.00#0  
[2023-03-26 04:40:42] local.INFO: transaction18  
[2023-03-26 04:40:42] local.INFO: array (
  0 => 'Error',
  1 => '9',
  2 => 'Payment was failed  - 5521',
)  
[2023-03-26 04:40:42] local.DEBUG: {"Number":********,"ServiceID":200,"NetworkID":null,"SubscriberNumber":"*********","Amount":8000,"FactionID":87,"RegionID":null,"LineType":"\u063a\u064a\u0631 \u0645\u0639\u0631\u0648\u0641","Date":"2023-03-26 04:40:42","Status":0,"Note":"\u0627\u0644\u0639\u0645\u0644\u064a\u0629 \u0641\u0627\u0634\u0644\u0629","CreditorAccountID":379015,"CurrencyID":1,"DebitorAccountID":"337620","AgentID":null,"RefNumber":null,"TransactionID":********,"ProviderID":10028,"EntryID":4400650,"PaymentEntryID":null,"Channel":2,"CreatedBy":"335360","BranchBy":null,"CreatedTime":"2023-03-26 04:40:42","BranchID":"1","ProviderRM":"\u0641\u0634\u0644 \u0641\u064a \u0627\u062c\u0631\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629","ProviderPrice":"8000.00","SubNote":null,"Datestamb":"********","UniqueNo":"********044042","Quantity":"8000.0000","UnitPrice":1,"UnitCost":1,"CostAmount":"8000.00","DifferentialAmount":0,"CommissionAmount":0,"Discount":0,"TotalCost":8000,"TotalAmount":8000,"Profits":0,"Method":2,"Type":0,"Class":0,"LType":"1","OperatorID":1,"AppTechApi":0,"BillNumber":"20000********","BillState":0,"Debited":0,"ByChild":0,"IsDirect":1,"BundleName":"\u0641\u0626\u0629 60 \u062c\u064a\u062c\u0627 8000 \u0631\u064a\u0627\u0644","BundleCode":"30","ExCode":null,"TransNumber":"********044042","OperationID":0,"AccountID":"337620","State":0,"StateClass":"\u0627\u0644\u0639\u0645\u0644\u064a\u0629 \u0641\u0627\u0634\u0644\u0629","Identifier":"b388e55bb399bca5","AdminNote":"\u0641\u0634\u0644 \u0641\u064a \u0627\u062c\u0631\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629 \u062a\u0645 \u0627\u0644\u063a\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629","AccountNote":"\u0641\u0634\u0644 \u0641\u064a \u0627\u062c\u0631\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629 \u062a\u0645 \u0627\u0644\u063a\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629","Description":null,"Responded":1,"RequestInfo":"200#*********#8000#87","ResponseTime":"2023-03-26 04:40:42","ResponseStatus":0,"ExecutionPeroid":"42","FaildRequest":0,"FailedReason":"\u0641\u0634\u0644 \u0641\u064a \u0627\u062c\u0631\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629","FailedType":0,"Cured":0,"CuredBy":null,"CuredInfo":null,"InspectInfo":null,"Flag":2,"Action":0,"QuotaionID":0,"SyncID":0,"id":4223822,"ResponseInfo":"Error#9#Payment was failed  - 5521"}  
[2023-03-26 04:40:42] local.DEBUG: response faild  
[2023-03-26 04:40:42] local.DEBUG: validateee  
[2023-03-26 04:40:42] local.DEBUG: response faild  
[2023-03-26 04:41:18] local.INFO: header  
[2023-03-26 04:41:18] local.CRITICAL: ****************************1  
[2023-03-26 04:41:18] local.ALERT: reach here  
[2023-03-26 04:41:18] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '8000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ثمانية ألف  ر.ي.',
  ),
)  
[2023-03-26 04:41:18] local.ERROR: المبلغ  
[2023-03-26 04:41:18] local.ERROR: 8,000.00  
[2023-03-26 04:41:18] local.ERROR: مبلغ وقدرة  
[2023-03-26 04:41:18] local.ALERT: 200  
[2023-03-26 04:41:18] local.ALERT: 3  
[2023-03-26 04:41:18] local.ALERT: 200  
[2023-03-26 04:41:18] local.ALERT: 3  
[2023-03-26 04:41:18] local.ALERT: 200  
[2023-03-26 04:41:18] local.ALERT: 3  
[2023-03-26 04:41:18] local.ALERT: 200  
[2023-03-26 04:41:18] local.ALERT: 3  
[2023-03-26 04:41:18] local.ALERT: 200  
[2023-03-26 04:41:18] local.ALERT: 3  
[2023-03-26 04:41:18] local.ALERT: 200  
[2023-03-26 04:41:18] local.ALERT: 3  
[2023-03-26 04:41:18] local.ALERT: 200  
[2023-03-26 04:41:18] local.ALERT: 3  
[2023-03-26 04:41:18] local.ALERT: 200  
[2023-03-26 04:41:18] local.ALERT: 3  
[2023-03-26 04:41:18] local.ALERT: 200  
[2023-03-26 04:41:18] local.ALERT: 3  
[2023-03-26 04:41:18] local.ALERT: 200  
[2023-03-26 04:41:18] local.ALERT: 2  
[2023-03-26 04:41:18] local.ALERT: 200  
[2023-03-26 04:41:18] local.ALERT: 2  
[2023-03-26 04:41:18] local.ALERT: 200  
[2023-03-26 04:41:18] local.ALERT: 2  
[2023-03-26 04:41:18] local.ALERT: 200  
[2023-03-26 04:41:18] local.ALERT: 2  
[2023-03-26 04:41:18] local.ALERT: 200  
[2023-03-26 04:41:18] local.ALERT: 2  
[2023-03-26 04:41:18] local.ALERT: 200  
[2023-03-26 04:41:18] local.ALERT: 2  
[2023-03-26 04:41:18] local.ALERT: 200  
[2023-03-26 04:41:18] local.ALERT: 2  
[2023-03-26 04:41:18] local.ALERT: 200  
[2023-03-26 04:41:18] local.ALERT: 1  
[2023-03-26 04:41:18] local.ALERT: 200  
[2023-03-26 04:41:18] local.ALERT: 1  
[2023-03-26 04:41:18] local.ALERT: 200  
[2023-03-26 04:41:18] local.ALERT: 1  
[2023-03-26 04:41:18] local.ALERT: 200  
[2023-03-26 04:41:18] local.ALERT: 1  
[2023-03-26 04:41:18] local.ALERT: 200  
[2023-03-26 04:41:18] local.ALERT: 1  
[2023-03-26 04:41:18] local.ALERT: 200  
[2023-03-26 04:41:18] local.ALERT: 2  
[2023-03-26 04:41:18] local.ALERT: 200  
[2023-03-26 04:41:18] local.ALERT: 10013  
[2023-03-26 04:41:18] local.ALERT: 200  
[2023-03-26 04:41:18] local.ALERT: 10013  
[2023-03-26 04:41:18] local.ALERT: 200  
[2023-03-26 04:41:18] local.ALERT: 10013  
[2023-03-26 04:41:18] local.ALERT: 200  
[2023-03-26 04:41:18] local.ALERT: 10013  
[2023-03-26 04:41:18] local.ALERT: 200  
[2023-03-26 04:41:18] local.ALERT: 1  
[2023-03-26 04:41:18] local.ALERT: 200  
[2023-03-26 04:41:18] local.ALERT: 3  
[2023-03-26 04:41:18] local.ALERT: 200  
[2023-03-26 04:41:18] local.ALERT: 3  
[2023-03-26 04:41:18] local.ALERT: 200  
[2023-03-26 04:41:18] local.ALERT: 40  
[2023-03-26 04:41:18] local.ALERT: 200  
[2023-03-26 04:41:18] local.ALERT: 40  
[2023-03-26 04:41:18] local.ALERT: 200  
[2023-03-26 04:41:18] local.ALERT: 40  
[2023-03-26 04:41:18] local.ALERT: 200  
[2023-03-26 04:41:18] local.ALERT: 40  
[2023-03-26 04:41:18] local.ALERT: 200  
[2023-03-26 04:41:18] local.ALERT: 200  
[2023-03-26 04:41:18] local.ALERT: 200  
[2023-03-26 04:41:18] local.ALERT: 200  
[2023-03-26 04:41:18] local.WARNING: 1  
[2023-03-26 04:41:18] local.WARNING: array (
  'ID' => 87,
  'Name' => 'فئة 60 جيجا 8000 ريال',
  'ServiceID' => 200,
  'Price' => 8000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 8000.0,
)  
[2023-03-26 04:41:18] local.ALERT: 200  
[2023-03-26 04:41:18] local.ALERT: 200  
[2023-03-26 04:41:18] local.ALERT: 200  
[2023-03-26 04:41:18] local.ALERT: 200  
[2023-03-26 04:41:18] local.ALERT: 200  
[2023-03-26 04:41:18] local.ALERT: 200  
[2023-03-26 04:41:18] local.ALERT: 200  
[2023-03-26 04:41:18] local.ALERT: 200  
[2023-03-26 04:41:18] local.ALERT: 200  
[2023-03-26 04:41:18] local.ALERT: 40  
[2023-03-26 04:41:18] local.CRITICAL: ****************************2  
[2023-03-26 04:41:18] local.CRITICAL: ****************************  
[2023-03-26 04:41:18] local.CRITICAL:   
[2023-03-26 04:41:18] local.CRITICAL: ****************************  
[2023-03-26 04:41:19] local.INFO: {
  "ClientBalanceResult": "365130.3600"
}  
[2023-03-26 04:41:19] local.INFO: array (
  'ClientBalanceResult' => '365130.3600',
)  
[2023-03-26 04:41:19] local.DEBUG: lattttef  
[2023-03-26 04:41:19] local.DEBUG: array (
  'ClientBalanceResult' => '365130.3600',
)  
[2023-03-26 04:41:19] local.INFO: transaction14  
[2023-03-26 04:41:19] local.INFO: first inquery phone = *********  
[2023-03-26 04:41:22] local.DEBUG: response querySubBalance  
[2023-03-26 04:41:22] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#8,000.00#8,000.00#6.55 MB#18-04-2023#0#0##8500.00#4G 60</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-26 04:41:22] local.DEBUG: array (
  0 => 'OK',
  1 => '8,000.00',
  2 => '8,000.00',
  3 => '6.55 MB',
  4 => '18-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '8500.00',
  9 => '4G 60',
)  
[2023-03-26 04:41:22] local.DEBUG: print  before faction by provider price  
[2023-03-26 04:41:22] local.DEBUG: print  after faction by provider price  
[2023-03-26 04:41:22] local.DEBUG: فئة 60 جيجا 8000 ريال  
[2023-03-26 04:41:22] local.DEBUG: print1  
[2023-03-26 04:41:22] local.DEBUG: print  2  
[2023-03-26 04:41:22] local.INFO: transaction1  
[2023-03-26 04:41:22] local.INFO: transaction2  
[2023-03-26 04:41:22] local.INFO: transaction3  
[2023-03-26 04:41:22] local.INFO: transaction4  
[2023-03-26 04:41:22] local.INFO: transaction4  
[2023-03-26 04:41:22] local.INFO: transaction5  
[2023-03-26 04:41:22] local.INFO: transaction6  
[2023-03-26 04:41:22] local.INFO: transaction7  
[2023-03-26 04:41:22] local.DEBUG: array (
  'AMT' => 8000.0,
  'CType' => 0,
  'FID' => 87,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '*********',
  'State' => 0,
  'lateflog' => '337620',
)  
[2023-03-26 04:41:22] local.INFO: transaction8  
[2023-03-26 04:41:23] local.INFO: transaction9  
[2023-03-26 04:41:23] local.INFO: transaction10  
[2023-03-26 04:41:23] local.INFO: transaction11  
[2023-03-26 04:41:23] local.INFO: 12  
[2023-03-26 04:41:23] local.INFO: transaction13  
[2023-03-26 04:41:23] local.INFO: transaction14  
[2023-03-26 04:41:23] local.INFO: transaction19  
[2023-03-26 04:41:23] local.INFO: transaction15  
[2023-03-26 04:41:23] local.INFO: transaction16  
[2023-03-26 04:41:23] local.INFO: 98#*********#8000.00#0  
[2023-03-26 04:41:23] local.INFO: transaction18  
[2023-03-26 04:41:23] local.INFO: array (
  0 => 'Error',
  1 => '9',
  2 => 'Payment was failed  - 5521',
)  
[2023-03-26 04:41:23] local.DEBUG: {"Number":********,"ServiceID":200,"NetworkID":null,"SubscriberNumber":"*********","Amount":8000,"FactionID":87,"RegionID":null,"LineType":"\u063a\u064a\u0631 \u0645\u0639\u0631\u0648\u0641","Date":"2023-03-26 04:41:23","Status":0,"Note":"\u0627\u0644\u0639\u0645\u0644\u064a\u0629 \u0641\u0627\u0634\u0644\u0629","CreditorAccountID":379015,"CurrencyID":1,"DebitorAccountID":"337620","AgentID":null,"RefNumber":null,"TransactionID":********,"ProviderID":10028,"EntryID":4400653,"PaymentEntryID":null,"Channel":2,"CreatedBy":"335360","BranchBy":null,"CreatedTime":"2023-03-26 04:41:23","BranchID":"1","ProviderRM":"\u0641\u0634\u0644 \u0641\u064a \u0627\u062c\u0631\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629","ProviderPrice":"8000.00","SubNote":null,"Datestamb":"********","UniqueNo":"********044123","Quantity":"8000.0000","UnitPrice":1,"UnitCost":1,"CostAmount":"8000.00","DifferentialAmount":0,"CommissionAmount":0,"Discount":0,"TotalCost":8000,"TotalAmount":8000,"Profits":0,"Method":2,"Type":0,"Class":0,"LType":"1","OperatorID":1,"AppTechApi":0,"BillNumber":"20000********","BillState":0,"Debited":0,"ByChild":0,"IsDirect":1,"BundleName":"\u0641\u0626\u0629 60 \u062c\u064a\u062c\u0627 8000 \u0631\u064a\u0627\u0644","BundleCode":"30","ExCode":null,"TransNumber":"********044123","OperationID":0,"AccountID":"337620","State":0,"StateClass":"\u0627\u0644\u0639\u0645\u0644\u064a\u0629 \u0641\u0627\u0634\u0644\u0629","Identifier":"b388e55bb399bca5","AdminNote":"\u0641\u0634\u0644 \u0641\u064a \u0627\u062c\u0631\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629 \u062a\u0645 \u0627\u0644\u063a\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629","AccountNote":"\u0641\u0634\u0644 \u0641\u064a \u0627\u062c\u0631\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629 \u062a\u0645 \u0627\u0644\u063a\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629","Description":null,"Responded":1,"RequestInfo":"200#*********#8000#87","ResponseTime":"2023-03-26 04:41:23","ResponseStatus":0,"ExecutionPeroid":"23","FaildRequest":0,"FailedReason":"\u0641\u0634\u0644 \u0641\u064a \u0627\u062c\u0631\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629","FailedType":0,"Cured":0,"CuredBy":null,"CuredInfo":null,"InspectInfo":null,"Flag":2,"Action":0,"QuotaionID":0,"SyncID":0,"id":4223826,"ResponseInfo":"Error#9#Payment was failed  - 5521"}  
[2023-03-26 04:41:23] local.DEBUG: response faild  
[2023-03-26 04:41:23] local.DEBUG: validateee  
[2023-03-26 04:41:23] local.DEBUG: response faild  
[2023-03-26 04:51:24] local.INFO: header  
[2023-03-26 04:51:24] local.INFO: header after fliter  
[2023-03-26 04:51:24] local.INFO: Body  after fliter  
[2023-03-26 04:51:24] local.INFO: array (
)  
[2023-03-26 04:51:24] local.INFO: transaction14  
[2023-03-26 04:51:24] local.INFO: first inquery phone = *********  
[2023-03-26 04:51:27] local.DEBUG: response querySubBalance  
[2023-03-26 04:51:27] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#8,000.00#8,000.00#6.55 MB#18-04-2023#0#0##8500.00#4G 60</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-26 04:51:27] local.DEBUG: array (
  0 => 'OK',
  1 => '8,000.00',
  2 => '8,000.00',
  3 => '6.55 MB',
  4 => '18-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '8500.00',
  9 => '4G 60',
)  
[2023-03-26 04:51:27] local.DEBUG: print  before faction by provider price  
[2023-03-26 04:51:27] local.DEBUG: print  after faction by provider price  
[2023-03-26 04:51:27] local.DEBUG: فئة 60 جيجا 8000 ريال  
[2023-03-26 04:51:27] local.DEBUG: print1  
[2023-03-26 04:51:27] local.DEBUG: print  2  
[2023-03-26 04:51:31] local.INFO: header  
[2023-03-26 04:51:31] local.CRITICAL: ****************************1  
[2023-03-26 04:51:31] local.ALERT: reach here  
[2023-03-26 04:51:31] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '8000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ثمانية ألف  ر.ي.',
  ),
)  
[2023-03-26 04:51:31] local.ERROR: المبلغ  
[2023-03-26 04:51:31] local.ERROR: 8,000.00  
[2023-03-26 04:51:31] local.ERROR: مبلغ وقدرة  
[2023-03-26 04:51:31] local.ALERT: 200  
[2023-03-26 04:51:31] local.ALERT: 3  
[2023-03-26 04:51:31] local.ALERT: 200  
[2023-03-26 04:51:31] local.ALERT: 3  
[2023-03-26 04:51:31] local.ALERT: 200  
[2023-03-26 04:51:31] local.ALERT: 3  
[2023-03-26 04:51:31] local.ALERT: 200  
[2023-03-26 04:51:31] local.ALERT: 3  
[2023-03-26 04:51:31] local.ALERT: 200  
[2023-03-26 04:51:31] local.ALERT: 3  
[2023-03-26 04:51:31] local.ALERT: 200  
[2023-03-26 04:51:31] local.ALERT: 3  
[2023-03-26 04:51:31] local.ALERT: 200  
[2023-03-26 04:51:31] local.ALERT: 3  
[2023-03-26 04:51:31] local.ALERT: 200  
[2023-03-26 04:51:31] local.ALERT: 3  
[2023-03-26 04:51:31] local.ALERT: 200  
[2023-03-26 04:51:31] local.ALERT: 3  
[2023-03-26 04:51:31] local.ALERT: 200  
[2023-03-26 04:51:31] local.ALERT: 2  
[2023-03-26 04:51:31] local.ALERT: 200  
[2023-03-26 04:51:31] local.ALERT: 2  
[2023-03-26 04:51:31] local.ALERT: 200  
[2023-03-26 04:51:31] local.ALERT: 2  
[2023-03-26 04:51:31] local.ALERT: 200  
[2023-03-26 04:51:31] local.ALERT: 2  
[2023-03-26 04:51:31] local.ALERT: 200  
[2023-03-26 04:51:31] local.ALERT: 2  
[2023-03-26 04:51:31] local.ALERT: 200  
[2023-03-26 04:51:31] local.ALERT: 2  
[2023-03-26 04:51:31] local.ALERT: 200  
[2023-03-26 04:51:31] local.ALERT: 2  
[2023-03-26 04:51:31] local.ALERT: 200  
[2023-03-26 04:51:31] local.ALERT: 1  
[2023-03-26 04:51:31] local.ALERT: 200  
[2023-03-26 04:51:31] local.ALERT: 1  
[2023-03-26 04:51:31] local.ALERT: 200  
[2023-03-26 04:51:31] local.ALERT: 1  
[2023-03-26 04:51:31] local.ALERT: 200  
[2023-03-26 04:51:31] local.ALERT: 1  
[2023-03-26 04:51:31] local.ALERT: 200  
[2023-03-26 04:51:31] local.ALERT: 1  
[2023-03-26 04:51:31] local.ALERT: 200  
[2023-03-26 04:51:31] local.ALERT: 2  
[2023-03-26 04:51:31] local.ALERT: 200  
[2023-03-26 04:51:31] local.ALERT: 10013  
[2023-03-26 04:51:31] local.ALERT: 200  
[2023-03-26 04:51:31] local.ALERT: 10013  
[2023-03-26 04:51:31] local.ALERT: 200  
[2023-03-26 04:51:31] local.ALERT: 10013  
[2023-03-26 04:51:31] local.ALERT: 200  
[2023-03-26 04:51:31] local.ALERT: 10013  
[2023-03-26 04:51:31] local.ALERT: 200  
[2023-03-26 04:51:31] local.ALERT: 1  
[2023-03-26 04:51:31] local.ALERT: 200  
[2023-03-26 04:51:31] local.ALERT: 3  
[2023-03-26 04:51:31] local.ALERT: 200  
[2023-03-26 04:51:31] local.ALERT: 3  
[2023-03-26 04:51:31] local.ALERT: 200  
[2023-03-26 04:51:31] local.ALERT: 40  
[2023-03-26 04:51:31] local.ALERT: 200  
[2023-03-26 04:51:31] local.ALERT: 40  
[2023-03-26 04:51:31] local.ALERT: 200  
[2023-03-26 04:51:31] local.ALERT: 40  
[2023-03-26 04:51:31] local.ALERT: 200  
[2023-03-26 04:51:31] local.ALERT: 40  
[2023-03-26 04:51:31] local.ALERT: 200  
[2023-03-26 04:51:31] local.ALERT: 200  
[2023-03-26 04:51:31] local.ALERT: 200  
[2023-03-26 04:51:31] local.ALERT: 200  
[2023-03-26 04:51:31] local.WARNING: 1  
[2023-03-26 04:51:31] local.WARNING: array (
  'ID' => 87,
  'Name' => 'فئة 60 جيجا 8000 ريال',
  'ServiceID' => 200,
  'Price' => 8000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 8000.0,
)  
[2023-03-26 04:51:31] local.ALERT: 200  
[2023-03-26 04:51:31] local.ALERT: 200  
[2023-03-26 04:51:31] local.ALERT: 200  
[2023-03-26 04:51:31] local.ALERT: 200  
[2023-03-26 04:51:31] local.ALERT: 200  
[2023-03-26 04:51:31] local.ALERT: 200  
[2023-03-26 04:51:31] local.ALERT: 200  
[2023-03-26 04:51:31] local.ALERT: 200  
[2023-03-26 04:51:31] local.ALERT: 200  
[2023-03-26 04:51:31] local.ALERT: 40  
[2023-03-26 04:51:31] local.CRITICAL: ****************************2  
[2023-03-26 04:51:31] local.CRITICAL: ****************************  
[2023-03-26 04:51:31] local.CRITICAL:   
[2023-03-26 04:51:31] local.CRITICAL: ****************************  
[2023-03-26 04:51:32] local.INFO: {
  "ClientBalanceResult": "365130.3600"
}  
[2023-03-26 04:51:32] local.INFO: array (
  'ClientBalanceResult' => '365130.3600',
)  
[2023-03-26 04:51:32] local.DEBUG: lattttef  
[2023-03-26 04:51:32] local.DEBUG: array (
  'ClientBalanceResult' => '365130.3600',
)  
[2023-03-26 04:51:32] local.INFO: transaction14  
[2023-03-26 04:51:32] local.INFO: first inquery phone = *********  
[2023-03-26 04:51:35] local.DEBUG: response querySubBalance  
[2023-03-26 04:51:35] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#8,000.00#8,000.00#6.55 MB#18-04-2023#0#0##8500.00#4G 60</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-26 04:51:35] local.DEBUG: array (
  0 => 'OK',
  1 => '8,000.00',
  2 => '8,000.00',
  3 => '6.55 MB',
  4 => '18-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '8500.00',
  9 => '4G 60',
)  
[2023-03-26 04:51:35] local.DEBUG: print  before faction by provider price  
[2023-03-26 04:51:35] local.DEBUG: print  after faction by provider price  
[2023-03-26 04:51:35] local.DEBUG: فئة 60 جيجا 8000 ريال  
[2023-03-26 04:51:35] local.DEBUG: print1  
[2023-03-26 04:51:35] local.DEBUG: print  2  
[2023-03-26 04:51:35] local.INFO: transaction1  
[2023-03-26 04:51:35] local.INFO: transaction2  
[2023-03-26 04:51:35] local.INFO: transaction3  
[2023-03-26 04:51:35] local.INFO: transaction4  
[2023-03-26 04:51:35] local.INFO: transaction4  
[2023-03-26 04:51:35] local.INFO: transaction5  
[2023-03-26 04:51:35] local.INFO: transaction6  
[2023-03-26 04:51:35] local.INFO: transaction7  
[2023-03-26 04:51:35] local.DEBUG: array (
  'AMT' => 8000.0,
  'CType' => 0,
  'FID' => 87,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '*********',
  'State' => 0,
  'lateflog' => '337620',
)  
[2023-03-26 04:51:35] local.INFO: transaction8  
[2023-03-26 04:51:35] local.INFO: transaction9  
[2023-03-26 04:51:35] local.INFO: transaction10  
[2023-03-26 04:51:35] local.INFO: transaction11  
[2023-03-26 04:51:35] local.INFO: 12  
[2023-03-26 04:51:35] local.INFO: transaction13  
[2023-03-26 04:51:35] local.INFO: transaction14  
[2023-03-26 04:51:35] local.INFO: transaction19  
[2023-03-26 04:51:35] local.INFO: transaction15  
[2023-03-26 04:51:35] local.INFO: transaction16  
[2023-03-26 04:51:35] local.INFO: 98#*********#8000.00#0  
[2023-03-26 04:51:35] local.INFO: transaction18  
[2023-03-26 04:51:35] local.INFO: array (
  0 => 'Error',
  1 => '9',
  2 => 'Payment was failed  - 5521',
)  
[2023-03-26 04:51:35] local.DEBUG: {"Number":********,"ServiceID":200,"NetworkID":null,"SubscriberNumber":"*********","Amount":8000,"FactionID":87,"RegionID":null,"LineType":"\u063a\u064a\u0631 \u0645\u0639\u0631\u0648\u0641","Date":"2023-03-26 04:51:35","Status":0,"Note":"\u0627\u0644\u0639\u0645\u0644\u064a\u0629 \u0641\u0627\u0634\u0644\u0629","CreditorAccountID":379015,"CurrencyID":1,"DebitorAccountID":"337620","AgentID":null,"RefNumber":null,"TransactionID":********,"ProviderID":10028,"EntryID":4400679,"PaymentEntryID":null,"Channel":2,"CreatedBy":"335360","BranchBy":null,"CreatedTime":"2023-03-26 04:51:35","BranchID":"1","ProviderRM":"\u0641\u0634\u0644 \u0641\u064a \u0627\u062c\u0631\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629","ProviderPrice":"8000.00","SubNote":null,"Datestamb":"********","UniqueNo":"********045135","Quantity":"8000.0000","UnitPrice":1,"UnitCost":1,"CostAmount":"8000.00","DifferentialAmount":0,"CommissionAmount":0,"Discount":0,"TotalCost":8000,"TotalAmount":8000,"Profits":0,"Method":2,"Type":0,"Class":0,"LType":"1","OperatorID":1,"AppTechApi":0,"BillNumber":"20000********","BillState":0,"Debited":0,"ByChild":0,"IsDirect":1,"BundleName":"\u0641\u0626\u0629 60 \u062c\u064a\u062c\u0627 8000 \u0631\u064a\u0627\u0644","BundleCode":"30","ExCode":null,"TransNumber":"********045135","OperationID":0,"AccountID":"337620","State":0,"StateClass":"\u0627\u0644\u0639\u0645\u0644\u064a\u0629 \u0641\u0627\u0634\u0644\u0629","Identifier":"b388e55bb399bca5","AdminNote":"\u0641\u0634\u0644 \u0641\u064a \u0627\u062c\u0631\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629 \u062a\u0645 \u0627\u0644\u063a\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629","AccountNote":"\u0641\u0634\u0644 \u0641\u064a \u0627\u062c\u0631\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629 \u062a\u0645 \u0627\u0644\u063a\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629","Description":null,"Responded":1,"RequestInfo":"200#*********#8000#87","ResponseTime":"2023-03-26 04:51:35","ResponseStatus":0,"ExecutionPeroid":"35","FaildRequest":0,"FailedReason":"\u0641\u0634\u0644 \u0641\u064a \u0627\u062c\u0631\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629","FailedType":0,"Cured":0,"CuredBy":null,"CuredInfo":null,"InspectInfo":null,"Flag":2,"Action":0,"QuotaionID":0,"SyncID":0,"id":4223849,"ResponseInfo":"Error#9#Payment was failed  - 5521"}  
[2023-03-26 04:51:35] local.DEBUG: response faild  
[2023-03-26 04:51:35] local.DEBUG: validateee  
[2023-03-26 04:51:35] local.DEBUG: response faild  
[2023-03-26 04:51:42] local.INFO: header  
[2023-03-26 04:51:42] local.CRITICAL: ****************************1  
[2023-03-26 04:51:42] local.ALERT: reach here  
[2023-03-26 04:51:43] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '8000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ثمانية ألف  ر.ي.',
  ),
)  
[2023-03-26 04:51:43] local.ERROR: المبلغ  
[2023-03-26 04:51:43] local.ERROR: 8,000.00  
[2023-03-26 04:51:43] local.ERROR: مبلغ وقدرة  
[2023-03-26 04:51:43] local.ALERT: 200  
[2023-03-26 04:51:43] local.ALERT: 3  
[2023-03-26 04:51:43] local.ALERT: 200  
[2023-03-26 04:51:43] local.ALERT: 3  
[2023-03-26 04:51:43] local.ALERT: 200  
[2023-03-26 04:51:43] local.ALERT: 3  
[2023-03-26 04:51:43] local.ALERT: 200  
[2023-03-26 04:51:43] local.ALERT: 3  
[2023-03-26 04:51:43] local.ALERT: 200  
[2023-03-26 04:51:43] local.ALERT: 3  
[2023-03-26 04:51:43] local.ALERT: 200  
[2023-03-26 04:51:43] local.ALERT: 3  
[2023-03-26 04:51:43] local.ALERT: 200  
[2023-03-26 04:51:43] local.ALERT: 3  
[2023-03-26 04:51:43] local.ALERT: 200  
[2023-03-26 04:51:43] local.ALERT: 3  
[2023-03-26 04:51:43] local.ALERT: 200  
[2023-03-26 04:51:43] local.ALERT: 3  
[2023-03-26 04:51:43] local.ALERT: 200  
[2023-03-26 04:51:43] local.ALERT: 2  
[2023-03-26 04:51:43] local.ALERT: 200  
[2023-03-26 04:51:43] local.ALERT: 2  
[2023-03-26 04:51:43] local.ALERT: 200  
[2023-03-26 04:51:43] local.ALERT: 2  
[2023-03-26 04:51:43] local.ALERT: 200  
[2023-03-26 04:51:43] local.ALERT: 2  
[2023-03-26 04:51:43] local.ALERT: 200  
[2023-03-26 04:51:43] local.ALERT: 2  
[2023-03-26 04:51:43] local.ALERT: 200  
[2023-03-26 04:51:43] local.ALERT: 2  
[2023-03-26 04:51:43] local.ALERT: 200  
[2023-03-26 04:51:43] local.ALERT: 2  
[2023-03-26 04:51:43] local.ALERT: 200  
[2023-03-26 04:51:43] local.ALERT: 1  
[2023-03-26 04:51:43] local.ALERT: 200  
[2023-03-26 04:51:43] local.ALERT: 1  
[2023-03-26 04:51:43] local.ALERT: 200  
[2023-03-26 04:51:43] local.ALERT: 1  
[2023-03-26 04:51:43] local.ALERT: 200  
[2023-03-26 04:51:43] local.ALERT: 1  
[2023-03-26 04:51:43] local.ALERT: 200  
[2023-03-26 04:51:43] local.ALERT: 1  
[2023-03-26 04:51:43] local.ALERT: 200  
[2023-03-26 04:51:43] local.ALERT: 2  
[2023-03-26 04:51:43] local.ALERT: 200  
[2023-03-26 04:51:43] local.ALERT: 10013  
[2023-03-26 04:51:43] local.ALERT: 200  
[2023-03-26 04:51:43] local.ALERT: 10013  
[2023-03-26 04:51:43] local.ALERT: 200  
[2023-03-26 04:51:43] local.ALERT: 10013  
[2023-03-26 04:51:43] local.ALERT: 200  
[2023-03-26 04:51:43] local.ALERT: 10013  
[2023-03-26 04:51:43] local.ALERT: 200  
[2023-03-26 04:51:43] local.ALERT: 1  
[2023-03-26 04:51:43] local.ALERT: 200  
[2023-03-26 04:51:43] local.ALERT: 3  
[2023-03-26 04:51:43] local.ALERT: 200  
[2023-03-26 04:51:43] local.ALERT: 3  
[2023-03-26 04:51:43] local.ALERT: 200  
[2023-03-26 04:51:43] local.ALERT: 40  
[2023-03-26 04:51:43] local.ALERT: 200  
[2023-03-26 04:51:43] local.ALERT: 40  
[2023-03-26 04:51:43] local.ALERT: 200  
[2023-03-26 04:51:43] local.ALERT: 40  
[2023-03-26 04:51:43] local.ALERT: 200  
[2023-03-26 04:51:43] local.ALERT: 40  
[2023-03-26 04:51:43] local.ALERT: 200  
[2023-03-26 04:51:43] local.ALERT: 200  
[2023-03-26 04:51:43] local.ALERT: 200  
[2023-03-26 04:51:43] local.ALERT: 200  
[2023-03-26 04:51:43] local.WARNING: 1  
[2023-03-26 04:51:43] local.WARNING: array (
  'ID' => 87,
  'Name' => 'فئة 60 جيجا 8000 ريال',
  'ServiceID' => 200,
  'Price' => 8000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 8000.0,
)  
[2023-03-26 04:51:43] local.ALERT: 200  
[2023-03-26 04:51:43] local.ALERT: 200  
[2023-03-26 04:51:43] local.ALERT: 200  
[2023-03-26 04:51:43] local.ALERT: 200  
[2023-03-26 04:51:43] local.ALERT: 200  
[2023-03-26 04:51:43] local.ALERT: 200  
[2023-03-26 04:51:43] local.ALERT: 200  
[2023-03-26 04:51:43] local.ALERT: 200  
[2023-03-26 04:51:43] local.ALERT: 200  
[2023-03-26 04:51:43] local.ALERT: 40  
[2023-03-26 04:51:43] local.CRITICAL: ****************************2  
[2023-03-26 04:51:43] local.CRITICAL: ****************************  
[2023-03-26 04:51:43] local.CRITICAL:   
[2023-03-26 04:51:43] local.CRITICAL: ****************************  
[2023-03-26 04:51:43] local.INFO: {
  "ClientBalanceResult": "365130.3600"
}  
[2023-03-26 04:51:43] local.INFO: array (
  'ClientBalanceResult' => '365130.3600',
)  
[2023-03-26 04:51:43] local.DEBUG: lattttef  
[2023-03-26 04:51:43] local.DEBUG: array (
  'ClientBalanceResult' => '365130.3600',
)  
[2023-03-26 04:51:43] local.INFO: transaction14  
[2023-03-26 04:51:43] local.INFO: first inquery phone = *********  
[2023-03-26 04:51:46] local.DEBUG: response querySubBalance  
[2023-03-26 04:51:46] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#8,000.00#8,000.00#6.55 MB#18-04-2023#0#0##8500.00#4G 60</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-26 04:51:46] local.DEBUG: array (
  0 => 'OK',
  1 => '8,000.00',
  2 => '8,000.00',
  3 => '6.55 MB',
  4 => '18-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '8500.00',
  9 => '4G 60',
)  
[2023-03-26 04:51:46] local.DEBUG: print  before faction by provider price  
[2023-03-26 04:51:46] local.DEBUG: print  after faction by provider price  
[2023-03-26 04:51:46] local.DEBUG: فئة 60 جيجا 8000 ريال  
[2023-03-26 04:51:46] local.DEBUG: print1  
[2023-03-26 04:51:46] local.DEBUG: print  2  
[2023-03-26 04:51:46] local.INFO: transaction1  
[2023-03-26 04:51:46] local.INFO: transaction2  
[2023-03-26 04:51:46] local.INFO: transaction3  
[2023-03-26 04:51:46] local.INFO: transaction4  
[2023-03-26 04:51:46] local.INFO: transaction4  
[2023-03-26 04:51:46] local.INFO: transaction5  
[2023-03-26 04:51:46] local.INFO: transaction6  
[2023-03-26 04:51:46] local.INFO: transaction7  
[2023-03-26 04:51:46] local.DEBUG: array (
  'AMT' => 8000.0,
  'CType' => 0,
  'FID' => 87,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '*********',
  'State' => 0,
  'lateflog' => '337620',
)  
[2023-03-26 04:51:46] local.INFO: transaction8  
[2023-03-26 04:51:47] local.INFO: transaction9  
[2023-03-26 04:51:47] local.INFO: transaction10  
[2023-03-26 04:51:47] local.INFO: transaction11  
[2023-03-26 04:51:47] local.INFO: 12  
[2023-03-26 04:51:47] local.INFO: transaction13  
[2023-03-26 04:51:47] local.INFO: transaction14  
[2023-03-26 04:51:47] local.INFO: transaction19  
[2023-03-26 04:51:47] local.INFO: transaction15  
[2023-03-26 04:51:47] local.INFO: transaction16  
[2023-03-26 04:51:47] local.INFO: 98#*********#8000.00#0  
[2023-03-26 04:51:47] local.INFO: transaction18  
[2023-03-26 04:51:47] local.INFO: array (
  0 => 'Error',
  1 => '9',
  2 => 'Payment was failed  - 5521',
)  
[2023-03-26 04:51:47] local.DEBUG: {"Number":********,"ServiceID":200,"NetworkID":null,"SubscriberNumber":"*********","Amount":8000,"FactionID":87,"RegionID":null,"LineType":"\u063a\u064a\u0631 \u0645\u0639\u0631\u0648\u0641","Date":"2023-03-26 04:51:47","Status":0,"Note":"\u0627\u0644\u0639\u0645\u0644\u064a\u0629 \u0641\u0627\u0634\u0644\u0629","CreditorAccountID":379015,"CurrencyID":1,"DebitorAccountID":"337620","AgentID":null,"RefNumber":null,"TransactionID":********,"ProviderID":10028,"EntryID":4400680,"PaymentEntryID":null,"Channel":2,"CreatedBy":"335360","BranchBy":null,"CreatedTime":"2023-03-26 04:51:47","BranchID":"1","ProviderRM":"\u0641\u0634\u0644 \u0641\u064a \u0627\u062c\u0631\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629","ProviderPrice":"8000.00","SubNote":null,"Datestamb":"********","UniqueNo":"********045147","Quantity":"8000.0000","UnitPrice":1,"UnitCost":1,"CostAmount":"8000.00","DifferentialAmount":0,"CommissionAmount":0,"Discount":0,"TotalCost":8000,"TotalAmount":8000,"Profits":0,"Method":2,"Type":0,"Class":0,"LType":"1","OperatorID":1,"AppTechApi":0,"BillNumber":"20000********","BillState":0,"Debited":0,"ByChild":0,"IsDirect":1,"BundleName":"\u0641\u0626\u0629 60 \u062c\u064a\u062c\u0627 8000 \u0631\u064a\u0627\u0644","BundleCode":"30","ExCode":null,"TransNumber":"********045147","OperationID":0,"AccountID":"337620","State":0,"StateClass":"\u0627\u0644\u0639\u0645\u0644\u064a\u0629 \u0641\u0627\u0634\u0644\u0629","Identifier":"b388e55bb399bca5","AdminNote":"\u0641\u0634\u0644 \u0641\u064a \u0627\u062c\u0631\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629 \u062a\u0645 \u0627\u0644\u063a\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629","AccountNote":"\u0641\u0634\u0644 \u0641\u064a \u0627\u062c\u0631\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629 \u062a\u0645 \u0627\u0644\u063a\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629","Description":null,"Responded":1,"RequestInfo":"200#*********#8000#87","ResponseTime":"2023-03-26 04:51:47","ResponseStatus":0,"ExecutionPeroid":"47","FaildRequest":0,"FailedReason":"\u0641\u0634\u0644 \u0641\u064a \u0627\u062c\u0631\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629","FailedType":0,"Cured":0,"CuredBy":null,"CuredInfo":null,"InspectInfo":null,"Flag":2,"Action":0,"QuotaionID":0,"SyncID":0,"id":4223850,"ResponseInfo":"Error#9#Payment was failed  - 5521"}  
[2023-03-26 04:51:47] local.DEBUG: response faild  
[2023-03-26 04:51:47] local.DEBUG: validateee  
[2023-03-26 04:51:47] local.DEBUG: response faild  
[2023-03-26 05:45:15] local.INFO: header  
[2023-03-26 05:45:15] local.INFO: header after fliter  
[2023-03-26 05:45:15] local.INFO: Body  after fliter  
[2023-03-26 05:45:15] local.INFO: array (
)  
[2023-03-26 05:45:15] local.INFO: transaction14  
[2023-03-26 05:45:15] local.INFO: first inquery phone = *********  
[2023-03-26 05:45:18] local.DEBUG: response querySubBalance  
[2023-03-26 05:45:18] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#8,000.00#8,000.00#6.55 MB#18-04-2023#0#0##8500.00#4G 60</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-26 05:45:18] local.DEBUG: array (
  0 => 'OK',
  1 => '8,000.00',
  2 => '8,000.00',
  3 => '6.55 MB',
  4 => '18-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '8500.00',
  9 => '4G 60',
)  
[2023-03-26 05:45:18] local.DEBUG: print  before faction by provider price  
[2023-03-26 05:45:18] local.DEBUG: print  after faction by provider price  
[2023-03-26 05:45:18] local.DEBUG: فئة 60 جيجا 8000 ريال  
[2023-03-26 05:45:18] local.DEBUG: print1  
[2023-03-26 05:45:18] local.DEBUG: print  2  
[2023-03-26 05:45:30] local.INFO: header  
[2023-03-26 05:45:30] local.CRITICAL: ****************************1  
[2023-03-26 05:45:30] local.ALERT: reach here  
[2023-03-26 05:45:30] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '8000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ثمانية ألف  ر.ي.',
  ),
)  
[2023-03-26 05:45:30] local.ERROR: المبلغ  
[2023-03-26 05:45:30] local.ERROR: 8,000.00  
[2023-03-26 05:45:30] local.ERROR: مبلغ وقدرة  
[2023-03-26 05:45:30] local.ALERT: 200  
[2023-03-26 05:45:30] local.ALERT: 3  
[2023-03-26 05:45:30] local.ALERT: 200  
[2023-03-26 05:45:30] local.ALERT: 3  
[2023-03-26 05:45:30] local.ALERT: 200  
[2023-03-26 05:45:30] local.ALERT: 3  
[2023-03-26 05:45:30] local.ALERT: 200  
[2023-03-26 05:45:30] local.ALERT: 3  
[2023-03-26 05:45:30] local.ALERT: 200  
[2023-03-26 05:45:30] local.ALERT: 3  
[2023-03-26 05:45:30] local.ALERT: 200  
[2023-03-26 05:45:30] local.ALERT: 3  
[2023-03-26 05:45:30] local.ALERT: 200  
[2023-03-26 05:45:30] local.ALERT: 3  
[2023-03-26 05:45:30] local.ALERT: 200  
[2023-03-26 05:45:30] local.ALERT: 3  
[2023-03-26 05:45:30] local.ALERT: 200  
[2023-03-26 05:45:30] local.ALERT: 3  
[2023-03-26 05:45:30] local.ALERT: 200  
[2023-03-26 05:45:30] local.ALERT: 2  
[2023-03-26 05:45:30] local.ALERT: 200  
[2023-03-26 05:45:30] local.ALERT: 2  
[2023-03-26 05:45:30] local.ALERT: 200  
[2023-03-26 05:45:30] local.ALERT: 2  
[2023-03-26 05:45:30] local.ALERT: 200  
[2023-03-26 05:45:30] local.ALERT: 2  
[2023-03-26 05:45:30] local.ALERT: 200  
[2023-03-26 05:45:30] local.ALERT: 2  
[2023-03-26 05:45:30] local.ALERT: 200  
[2023-03-26 05:45:30] local.ALERT: 2  
[2023-03-26 05:45:30] local.ALERT: 200  
[2023-03-26 05:45:30] local.ALERT: 2  
[2023-03-26 05:45:30] local.ALERT: 200  
[2023-03-26 05:45:30] local.ALERT: 1  
[2023-03-26 05:45:30] local.ALERT: 200  
[2023-03-26 05:45:30] local.ALERT: 1  
[2023-03-26 05:45:30] local.ALERT: 200  
[2023-03-26 05:45:30] local.ALERT: 1  
[2023-03-26 05:45:30] local.ALERT: 200  
[2023-03-26 05:45:30] local.ALERT: 1  
[2023-03-26 05:45:30] local.ALERT: 200  
[2023-03-26 05:45:30] local.ALERT: 1  
[2023-03-26 05:45:30] local.ALERT: 200  
[2023-03-26 05:45:30] local.ALERT: 2  
[2023-03-26 05:45:30] local.ALERT: 200  
[2023-03-26 05:45:30] local.ALERT: 10013  
[2023-03-26 05:45:30] local.ALERT: 200  
[2023-03-26 05:45:30] local.ALERT: 10013  
[2023-03-26 05:45:30] local.ALERT: 200  
[2023-03-26 05:45:30] local.ALERT: 10013  
[2023-03-26 05:45:30] local.ALERT: 200  
[2023-03-26 05:45:30] local.ALERT: 10013  
[2023-03-26 05:45:30] local.ALERT: 200  
[2023-03-26 05:45:30] local.ALERT: 1  
[2023-03-26 05:45:30] local.ALERT: 200  
[2023-03-26 05:45:30] local.ALERT: 3  
[2023-03-26 05:45:30] local.ALERT: 200  
[2023-03-26 05:45:30] local.ALERT: 3  
[2023-03-26 05:45:30] local.ALERT: 200  
[2023-03-26 05:45:30] local.ALERT: 40  
[2023-03-26 05:45:30] local.ALERT: 200  
[2023-03-26 05:45:30] local.ALERT: 40  
[2023-03-26 05:45:30] local.ALERT: 200  
[2023-03-26 05:45:30] local.ALERT: 40  
[2023-03-26 05:45:30] local.ALERT: 200  
[2023-03-26 05:45:30] local.ALERT: 40  
[2023-03-26 05:45:30] local.ALERT: 200  
[2023-03-26 05:45:30] local.ALERT: 200  
[2023-03-26 05:45:30] local.ALERT: 200  
[2023-03-26 05:45:30] local.ALERT: 200  
[2023-03-26 05:45:30] local.WARNING: 1  
[2023-03-26 05:45:30] local.WARNING: array (
  'ID' => 87,
  'Name' => 'فئة 60 جيجا 8000 ريال',
  'ServiceID' => 200,
  'Price' => 8000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 8000.0,
)  
[2023-03-26 05:45:30] local.ALERT: 200  
[2023-03-26 05:45:30] local.ALERT: 200  
[2023-03-26 05:45:30] local.ALERT: 200  
[2023-03-26 05:45:30] local.ALERT: 200  
[2023-03-26 05:45:30] local.ALERT: 200  
[2023-03-26 05:45:30] local.ALERT: 200  
[2023-03-26 05:45:30] local.ALERT: 200  
[2023-03-26 05:45:30] local.ALERT: 200  
[2023-03-26 05:45:30] local.ALERT: 200  
[2023-03-26 05:45:30] local.ALERT: 40  
[2023-03-26 05:45:30] local.CRITICAL: ****************************2  
[2023-03-26 05:45:30] local.CRITICAL: ****************************  
[2023-03-26 05:45:30] local.CRITICAL:   
[2023-03-26 05:45:30] local.CRITICAL: ****************************  
[2023-03-26 05:45:31] local.INFO: {
  "ClientBalanceResult": "363730.3600"
}  
[2023-03-26 05:45:31] local.INFO: array (
  'ClientBalanceResult' => '363730.3600',
)  
[2023-03-26 05:45:31] local.DEBUG: lattttef  
[2023-03-26 05:45:31] local.DEBUG: array (
  'ClientBalanceResult' => '363730.3600',
)  
[2023-03-26 05:45:31] local.INFO: transaction14  
[2023-03-26 05:45:31] local.INFO: first inquery phone = *********  
[2023-03-26 05:45:34] local.DEBUG: response querySubBalance  
[2023-03-26 05:45:34] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#8,000.00#8,000.00#6.55 MB#18-04-2023#0#0##8500.00#4G 60</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-26 05:45:34] local.DEBUG: array (
  0 => 'OK',
  1 => '8,000.00',
  2 => '8,000.00',
  3 => '6.55 MB',
  4 => '18-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '8500.00',
  9 => '4G 60',
)  
[2023-03-26 05:45:34] local.DEBUG: print  before faction by provider price  
[2023-03-26 05:45:34] local.DEBUG: print  after faction by provider price  
[2023-03-26 05:45:34] local.DEBUG: فئة 60 جيجا 8000 ريال  
[2023-03-26 05:45:34] local.DEBUG: print1  
[2023-03-26 05:45:34] local.DEBUG: print  2  
[2023-03-26 05:45:34] local.INFO: transaction1  
[2023-03-26 05:45:34] local.INFO: transaction2  
[2023-03-26 05:45:34] local.INFO: transaction3  
[2023-03-26 05:45:34] local.INFO: transaction4  
[2023-03-26 05:45:34] local.INFO: transaction4  
[2023-03-26 05:45:34] local.INFO: transaction5  
[2023-03-26 05:45:34] local.INFO: transaction6  
[2023-03-26 05:45:34] local.INFO: transaction7  
[2023-03-26 05:45:34] local.DEBUG: array (
  'AMT' => 8000.0,
  'CType' => 0,
  'FID' => 87,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '*********',
  'State' => 0,
  'lateflog' => '337620',
)  
[2023-03-26 05:45:34] local.INFO: transaction8  
[2023-03-26 05:45:35] local.INFO: transaction9  
[2023-03-26 05:45:35] local.INFO: transaction10  
[2023-03-26 05:45:35] local.INFO: transaction11  
[2023-03-26 05:45:35] local.INFO: 12  
[2023-03-26 05:45:35] local.INFO: transaction13  
[2023-03-26 05:45:35] local.INFO: transaction14  
[2023-03-26 05:45:35] local.INFO: transaction19  
[2023-03-26 05:45:35] local.INFO: transaction15  
[2023-03-26 05:45:35] local.INFO: transaction16  
[2023-03-26 05:45:35] local.INFO: 98#*********#8000.00#0  
[2023-03-26 05:45:35] local.INFO: transaction18  
[2023-03-26 05:45:35] local.INFO: array (
  0 => 'Error',
  1 => '9',
  2 => 'Payment was failed  - 5521',
)  
[2023-03-26 05:45:35] local.DEBUG: {"Number":********,"ServiceID":200,"NetworkID":null,"SubscriberNumber":"*********","Amount":8000,"FactionID":87,"RegionID":null,"LineType":"\u063a\u064a\u0631 \u0645\u0639\u0631\u0648\u0641","Date":"2023-03-26 05:45:35","Status":0,"Note":"\u0627\u0644\u0639\u0645\u0644\u064a\u0629 \u0641\u0627\u0634\u0644\u0629","CreditorAccountID":379015,"CurrencyID":1,"DebitorAccountID":"337620","AgentID":null,"RefNumber":null,"TransactionID":********,"ProviderID":10028,"EntryID":4400730,"PaymentEntryID":null,"Channel":2,"CreatedBy":"335360","BranchBy":null,"CreatedTime":"2023-03-26 05:45:35","BranchID":"1","ProviderRM":"\u0641\u0634\u0644 \u0641\u064a \u0627\u062c\u0631\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629","ProviderPrice":"8000.00","SubNote":null,"Datestamb":"********","UniqueNo":"********054535","Quantity":"8000.0000","UnitPrice":1,"UnitCost":1,"CostAmount":"8000.00","DifferentialAmount":0,"CommissionAmount":0,"Discount":0,"TotalCost":8000,"TotalAmount":8000,"Profits":0,"Method":2,"Type":0,"Class":0,"LType":"1","OperatorID":1,"AppTechApi":0,"BillNumber":"20000********","BillState":0,"Debited":0,"ByChild":0,"IsDirect":1,"BundleName":"\u0641\u0626\u0629 60 \u062c\u064a\u062c\u0627 8000 \u0631\u064a\u0627\u0644","BundleCode":"30","ExCode":null,"TransNumber":"********054535","OperationID":0,"AccountID":"337620","State":0,"StateClass":"\u0627\u0644\u0639\u0645\u0644\u064a\u0629 \u0641\u0627\u0634\u0644\u0629","Identifier":"b388e55bb399bca5","AdminNote":"\u0641\u0634\u0644 \u0641\u064a \u0627\u062c\u0631\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629 \u062a\u0645 \u0627\u0644\u063a\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629","AccountNote":"\u0641\u0634\u0644 \u0641\u064a \u0627\u062c\u0631\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629 \u062a\u0645 \u0627\u0644\u063a\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629","Description":null,"Responded":1,"RequestInfo":"200#*********#8000#87","ResponseTime":"2023-03-26 05:45:35","ResponseStatus":0,"ExecutionPeroid":"35","FaildRequest":0,"FailedReason":"\u0641\u0634\u0644 \u0641\u064a \u0627\u062c\u0631\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629","FailedType":0,"Cured":0,"CuredBy":null,"CuredInfo":null,"InspectInfo":null,"Flag":2,"Action":0,"QuotaionID":0,"SyncID":0,"id":4223899,"ResponseInfo":"Error#9#Payment was failed  - 5521"}  
[2023-03-26 05:45:35] local.DEBUG: response faild  
[2023-03-26 05:45:35] local.DEBUG: validateee  
[2023-03-26 05:45:35] local.DEBUG: response faild  
[2023-03-26 06:03:18] local.INFO: header  
[2023-03-26 06:03:18] local.INFO: header after fliter  
[2023-03-26 06:03:18] local.INFO: Body  after fliter  
[2023-03-26 06:03:18] local.INFO: array (
)  
[2023-03-26 06:03:18] local.INFO: transaction14  
[2023-03-26 06:03:18] local.INFO: first inquery phone = *********  
[2023-03-26 06:03:21] local.DEBUG: response querySubBalance  
[2023-03-26 06:03:21] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#8,000.00#8,000.00#6.55 MB#18-04-2023#0#0##8500.00#4G 60</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-26 06:03:21] local.DEBUG: array (
  0 => 'OK',
  1 => '8,000.00',
  2 => '8,000.00',
  3 => '6.55 MB',
  4 => '18-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '8500.00',
  9 => '4G 60',
)  
[2023-03-26 06:03:21] local.DEBUG: print  before faction by provider price  
[2023-03-26 06:03:21] local.DEBUG: print  after faction by provider price  
[2023-03-26 06:03:21] local.DEBUG: فئة 60 جيجا 8000 ريال  
[2023-03-26 06:03:21] local.DEBUG: print1  
[2023-03-26 06:03:21] local.DEBUG: print  2  
[2023-03-26 06:03:26] local.INFO: header  
[2023-03-26 06:03:26] local.CRITICAL: ****************************1  
[2023-03-26 06:03:26] local.ALERT: reach here  
[2023-03-26 06:03:26] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '8000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ثمانية ألف  ر.ي.',
  ),
)  
[2023-03-26 06:03:26] local.ERROR: المبلغ  
[2023-03-26 06:03:26] local.ERROR: 8,000.00  
[2023-03-26 06:03:26] local.ERROR: مبلغ وقدرة  
[2023-03-26 06:03:27] local.ALERT: 200  
[2023-03-26 06:03:27] local.ALERT: 3  
[2023-03-26 06:03:27] local.ALERT: 200  
[2023-03-26 06:03:27] local.ALERT: 3  
[2023-03-26 06:03:27] local.ALERT: 200  
[2023-03-26 06:03:27] local.ALERT: 3  
[2023-03-26 06:03:27] local.ALERT: 200  
[2023-03-26 06:03:27] local.ALERT: 3  
[2023-03-26 06:03:27] local.ALERT: 200  
[2023-03-26 06:03:27] local.ALERT: 3  
[2023-03-26 06:03:27] local.ALERT: 200  
[2023-03-26 06:03:27] local.ALERT: 3  
[2023-03-26 06:03:27] local.ALERT: 200  
[2023-03-26 06:03:27] local.ALERT: 3  
[2023-03-26 06:03:27] local.ALERT: 200  
[2023-03-26 06:03:27] local.ALERT: 3  
[2023-03-26 06:03:27] local.ALERT: 200  
[2023-03-26 06:03:27] local.ALERT: 3  
[2023-03-26 06:03:27] local.ALERT: 200  
[2023-03-26 06:03:27] local.ALERT: 2  
[2023-03-26 06:03:27] local.ALERT: 200  
[2023-03-26 06:03:27] local.ALERT: 2  
[2023-03-26 06:03:27] local.ALERT: 200  
[2023-03-26 06:03:27] local.ALERT: 2  
[2023-03-26 06:03:27] local.ALERT: 200  
[2023-03-26 06:03:27] local.ALERT: 2  
[2023-03-26 06:03:27] local.ALERT: 200  
[2023-03-26 06:03:27] local.ALERT: 2  
[2023-03-26 06:03:27] local.ALERT: 200  
[2023-03-26 06:03:27] local.ALERT: 2  
[2023-03-26 06:03:27] local.ALERT: 200  
[2023-03-26 06:03:27] local.ALERT: 2  
[2023-03-26 06:03:27] local.ALERT: 200  
[2023-03-26 06:03:27] local.ALERT: 1  
[2023-03-26 06:03:27] local.ALERT: 200  
[2023-03-26 06:03:27] local.ALERT: 1  
[2023-03-26 06:03:27] local.ALERT: 200  
[2023-03-26 06:03:27] local.ALERT: 1  
[2023-03-26 06:03:27] local.ALERT: 200  
[2023-03-26 06:03:27] local.ALERT: 1  
[2023-03-26 06:03:27] local.ALERT: 200  
[2023-03-26 06:03:27] local.ALERT: 1  
[2023-03-26 06:03:27] local.ALERT: 200  
[2023-03-26 06:03:27] local.ALERT: 2  
[2023-03-26 06:03:27] local.ALERT: 200  
[2023-03-26 06:03:27] local.ALERT: 10013  
[2023-03-26 06:03:27] local.ALERT: 200  
[2023-03-26 06:03:27] local.ALERT: 10013  
[2023-03-26 06:03:27] local.ALERT: 200  
[2023-03-26 06:03:27] local.ALERT: 10013  
[2023-03-26 06:03:27] local.ALERT: 200  
[2023-03-26 06:03:27] local.ALERT: 10013  
[2023-03-26 06:03:27] local.ALERT: 200  
[2023-03-26 06:03:27] local.ALERT: 1  
[2023-03-26 06:03:27] local.ALERT: 200  
[2023-03-26 06:03:27] local.ALERT: 3  
[2023-03-26 06:03:27] local.ALERT: 200  
[2023-03-26 06:03:27] local.ALERT: 3  
[2023-03-26 06:03:27] local.ALERT: 200  
[2023-03-26 06:03:27] local.ALERT: 40  
[2023-03-26 06:03:27] local.ALERT: 200  
[2023-03-26 06:03:27] local.ALERT: 40  
[2023-03-26 06:03:27] local.ALERT: 200  
[2023-03-26 06:03:27] local.ALERT: 40  
[2023-03-26 06:03:27] local.ALERT: 200  
[2023-03-26 06:03:27] local.ALERT: 40  
[2023-03-26 06:03:27] local.ALERT: 200  
[2023-03-26 06:03:27] local.ALERT: 200  
[2023-03-26 06:03:27] local.ALERT: 200  
[2023-03-26 06:03:27] local.ALERT: 200  
[2023-03-26 06:03:27] local.WARNING: 1  
[2023-03-26 06:03:27] local.WARNING: array (
  'ID' => 87,
  'Name' => 'فئة 60 جيجا 8000 ريال',
  'ServiceID' => 200,
  'Price' => 8000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 8000.0,
)  
[2023-03-26 06:03:27] local.ALERT: 200  
[2023-03-26 06:03:27] local.ALERT: 200  
[2023-03-26 06:03:27] local.ALERT: 200  
[2023-03-26 06:03:27] local.ALERT: 200  
[2023-03-26 06:03:27] local.ALERT: 200  
[2023-03-26 06:03:27] local.ALERT: 200  
[2023-03-26 06:03:27] local.ALERT: 200  
[2023-03-26 06:03:27] local.ALERT: 200  
[2023-03-26 06:03:27] local.ALERT: 200  
[2023-03-26 06:03:27] local.ALERT: 40  
[2023-03-26 06:03:27] local.CRITICAL: ****************************2  
[2023-03-26 06:03:27] local.CRITICAL: ****************************  
[2023-03-26 06:03:27] local.CRITICAL:   
[2023-03-26 06:03:27] local.CRITICAL: ****************************  
[2023-03-26 06:03:27] local.INFO: {
  "ClientBalanceResult": "362210.3600"
}  
[2023-03-26 06:03:27] local.INFO: array (
  'ClientBalanceResult' => '362210.3600',
)  
[2023-03-26 06:03:27] local.DEBUG: lattttef  
[2023-03-26 06:03:27] local.DEBUG: array (
  'ClientBalanceResult' => '362210.3600',
)  
[2023-03-26 06:03:27] local.INFO: transaction14  
[2023-03-26 06:03:27] local.INFO: first inquery phone = *********  
[2023-03-26 06:03:30] local.DEBUG: response querySubBalance  
[2023-03-26 06:03:30] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#8,000.00#8,000.00#6.55 MB#18-04-2023#0#0##8500.00#4G 60</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-26 06:03:30] local.DEBUG: array (
  0 => 'OK',
  1 => '8,000.00',
  2 => '8,000.00',
  3 => '6.55 MB',
  4 => '18-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '8500.00',
  9 => '4G 60',
)  
[2023-03-26 06:03:30] local.DEBUG: print  before faction by provider price  
[2023-03-26 06:03:30] local.DEBUG: print  after faction by provider price  
[2023-03-26 06:03:30] local.DEBUG: فئة 60 جيجا 8000 ريال  
[2023-03-26 06:03:30] local.DEBUG: print1  
[2023-03-26 06:03:30] local.DEBUG: print  2  
[2023-03-26 06:03:30] local.INFO: transaction1  
[2023-03-26 06:03:30] local.INFO: transaction2  
[2023-03-26 06:03:30] local.INFO: transaction3  
[2023-03-26 06:03:30] local.INFO: transaction4  
[2023-03-26 06:03:30] local.INFO: transaction4  
[2023-03-26 06:03:30] local.INFO: transaction5  
[2023-03-26 06:03:30] local.INFO: transaction6  
[2023-03-26 06:03:30] local.INFO: transaction7  
[2023-03-26 06:03:30] local.DEBUG: array (
  'AMT' => 8000.0,
  'CType' => 0,
  'FID' => 87,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '*********',
  'State' => 0,
  'lateflog' => '337620',
)  
[2023-03-26 06:03:30] local.INFO: transaction8  
[2023-03-26 06:03:31] local.INFO: transaction9  
[2023-03-26 06:03:31] local.INFO: transaction10  
[2023-03-26 06:03:31] local.INFO: transaction11  
[2023-03-26 06:03:31] local.INFO: 12  
[2023-03-26 06:03:31] local.INFO: transaction13  
[2023-03-26 06:03:31] local.INFO: transaction14  
[2023-03-26 06:03:31] local.INFO: transaction19  
[2023-03-26 06:03:31] local.INFO: transaction15  
[2023-03-26 06:03:31] local.INFO: transaction16  
[2023-03-26 06:03:31] local.INFO: 98#*********#8000.00#0  
[2023-03-26 06:03:31] local.INFO: transaction18  
[2023-03-26 06:03:31] local.INFO: array (
  0 => 'Error',
  1 => '9',
  2 => 'Payment was failed  - 5521',
)  
[2023-03-26 06:03:31] local.DEBUG: {"Number":********,"ServiceID":200,"NetworkID":null,"SubscriberNumber":"*********","Amount":8000,"FactionID":87,"RegionID":null,"LineType":"\u063a\u064a\u0631 \u0645\u0639\u0631\u0648\u0641","Date":"2023-03-26 06:03:31","Status":0,"Note":"\u0627\u0644\u0639\u0645\u0644\u064a\u0629 \u0641\u0627\u0634\u0644\u0629","CreditorAccountID":379015,"CurrencyID":1,"DebitorAccountID":"337620","AgentID":null,"RefNumber":null,"TransactionID":********,"ProviderID":10028,"EntryID":4400737,"PaymentEntryID":null,"Channel":2,"CreatedBy":"335360","BranchBy":null,"CreatedTime":"2023-03-26 06:03:31","BranchID":"1","ProviderRM":"\u0641\u0634\u0644 \u0641\u064a \u0627\u062c\u0631\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629","ProviderPrice":"8000.00","SubNote":null,"Datestamb":"********","UniqueNo":"********060331","Quantity":"8000.0000","UnitPrice":1,"UnitCost":1,"CostAmount":"8000.00","DifferentialAmount":0,"CommissionAmount":0,"Discount":0,"TotalCost":8000,"TotalAmount":8000,"Profits":0,"Method":2,"Type":0,"Class":0,"LType":"1","OperatorID":1,"AppTechApi":0,"BillNumber":"20000********","BillState":0,"Debited":0,"ByChild":0,"IsDirect":1,"BundleName":"\u0641\u0626\u0629 60 \u062c\u064a\u062c\u0627 8000 \u0631\u064a\u0627\u0644","BundleCode":"30","ExCode":null,"TransNumber":"********060331","OperationID":0,"AccountID":"337620","State":0,"StateClass":"\u0627\u0644\u0639\u0645\u0644\u064a\u0629 \u0641\u0627\u0634\u0644\u0629","Identifier":"b388e55bb399bca5","AdminNote":"\u0641\u0634\u0644 \u0641\u064a \u0627\u062c\u0631\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629 \u062a\u0645 \u0627\u0644\u063a\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629","AccountNote":"\u0641\u0634\u0644 \u0641\u064a \u0627\u062c\u0631\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629 \u062a\u0645 \u0627\u0644\u063a\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629","Description":null,"Responded":1,"RequestInfo":"200#*********#8000#87","ResponseTime":"2023-03-26 06:03:31","ResponseStatus":0,"ExecutionPeroid":"31","FaildRequest":0,"FailedReason":"\u0641\u0634\u0644 \u0641\u064a \u0627\u062c\u0631\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629","FailedType":0,"Cured":0,"CuredBy":null,"CuredInfo":null,"InspectInfo":null,"Flag":2,"Action":0,"QuotaionID":0,"SyncID":0,"id":4223906,"ResponseInfo":"Error#9#Payment was failed  - 5521"}  
[2023-03-26 06:03:31] local.DEBUG: response faild  
[2023-03-26 06:03:31] local.DEBUG: validateee  
[2023-03-26 06:03:31] local.DEBUG: response faild  
[2023-03-26 06:13:18] local.INFO: header  
[2023-03-26 06:13:18] local.CRITICAL: ****************************1  
[2023-03-26 06:13:18] local.ALERT: reach here  
[2023-03-26 06:13:19] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '4000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'أربعة ألف  ر.ي.',
  ),
)  
[2023-03-26 06:13:19] local.ERROR: المبلغ  
[2023-03-26 06:13:19] local.ERROR: 4,000.00  
[2023-03-26 06:13:19] local.ERROR: مبلغ وقدرة  
[2023-03-26 06:13:19] local.ALERT: 200  
[2023-03-26 06:13:19] local.ALERT: 3  
[2023-03-26 06:13:19] local.ALERT: 200  
[2023-03-26 06:13:19] local.ALERT: 3  
[2023-03-26 06:13:19] local.ALERT: 200  
[2023-03-26 06:13:19] local.ALERT: 3  
[2023-03-26 06:13:19] local.ALERT: 200  
[2023-03-26 06:13:19] local.ALERT: 3  
[2023-03-26 06:13:19] local.ALERT: 200  
[2023-03-26 06:13:19] local.ALERT: 3  
[2023-03-26 06:13:19] local.ALERT: 200  
[2023-03-26 06:13:19] local.ALERT: 3  
[2023-03-26 06:13:19] local.ALERT: 200  
[2023-03-26 06:13:19] local.ALERT: 3  
[2023-03-26 06:13:19] local.ALERT: 200  
[2023-03-26 06:13:19] local.ALERT: 3  
[2023-03-26 06:13:19] local.ALERT: 200  
[2023-03-26 06:13:19] local.ALERT: 3  
[2023-03-26 06:13:19] local.ALERT: 200  
[2023-03-26 06:13:19] local.ALERT: 2  
[2023-03-26 06:13:19] local.ALERT: 200  
[2023-03-26 06:13:19] local.ALERT: 2  
[2023-03-26 06:13:19] local.ALERT: 200  
[2023-03-26 06:13:19] local.ALERT: 2  
[2023-03-26 06:13:19] local.ALERT: 200  
[2023-03-26 06:13:19] local.ALERT: 2  
[2023-03-26 06:13:19] local.ALERT: 200  
[2023-03-26 06:13:19] local.ALERT: 2  
[2023-03-26 06:13:19] local.ALERT: 200  
[2023-03-26 06:13:19] local.ALERT: 2  
[2023-03-26 06:13:19] local.ALERT: 200  
[2023-03-26 06:13:19] local.ALERT: 2  
[2023-03-26 06:13:19] local.ALERT: 200  
[2023-03-26 06:13:19] local.ALERT: 1  
[2023-03-26 06:13:19] local.ALERT: 200  
[2023-03-26 06:13:19] local.ALERT: 1  
[2023-03-26 06:13:19] local.ALERT: 200  
[2023-03-26 06:13:19] local.ALERT: 1  
[2023-03-26 06:13:19] local.ALERT: 200  
[2023-03-26 06:13:19] local.ALERT: 1  
[2023-03-26 06:13:19] local.ALERT: 200  
[2023-03-26 06:13:19] local.ALERT: 1  
[2023-03-26 06:13:19] local.ALERT: 200  
[2023-03-26 06:13:19] local.ALERT: 2  
[2023-03-26 06:13:19] local.ALERT: 200  
[2023-03-26 06:13:19] local.ALERT: 10013  
[2023-03-26 06:13:19] local.ALERT: 200  
[2023-03-26 06:13:19] local.ALERT: 10013  
[2023-03-26 06:13:19] local.ALERT: 200  
[2023-03-26 06:13:19] local.ALERT: 10013  
[2023-03-26 06:13:19] local.ALERT: 200  
[2023-03-26 06:13:19] local.ALERT: 10013  
[2023-03-26 06:13:19] local.ALERT: 200  
[2023-03-26 06:13:19] local.ALERT: 1  
[2023-03-26 06:13:19] local.ALERT: 200  
[2023-03-26 06:13:19] local.ALERT: 3  
[2023-03-26 06:13:19] local.ALERT: 200  
[2023-03-26 06:13:19] local.ALERT: 3  
[2023-03-26 06:13:19] local.ALERT: 200  
[2023-03-26 06:13:19] local.ALERT: 40  
[2023-03-26 06:13:19] local.ALERT: 200  
[2023-03-26 06:13:19] local.ALERT: 40  
[2023-03-26 06:13:19] local.ALERT: 200  
[2023-03-26 06:13:19] local.ALERT: 40  
[2023-03-26 06:13:19] local.ALERT: 200  
[2023-03-26 06:13:19] local.ALERT: 40  
[2023-03-26 06:13:19] local.ALERT: 200  
[2023-03-26 06:13:19] local.ALERT: 200  
[2023-03-26 06:13:19] local.WARNING: 1  
[2023-03-26 06:13:19] local.WARNING: array (
  'ID' => 86,
  'Name' => 'فئة 25 جيجا 4000 ريال',
  'ServiceID' => 200,
  'Price' => 4000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 4000.0,
)  
[2023-03-26 06:13:19] local.ALERT: 200  
[2023-03-26 06:13:19] local.ALERT: 200  
[2023-03-26 06:13:19] local.ALERT: 200  
[2023-03-26 06:13:19] local.ALERT: 200  
[2023-03-26 06:13:19] local.ALERT: 200  
[2023-03-26 06:13:19] local.ALERT: 200  
[2023-03-26 06:13:19] local.ALERT: 200  
[2023-03-26 06:13:19] local.ALERT: 200  
[2023-03-26 06:13:19] local.ALERT: 200  
[2023-03-26 06:13:19] local.ALERT: 200  
[2023-03-26 06:13:19] local.ALERT: 200  
[2023-03-26 06:13:19] local.ALERT: 40  
[2023-03-26 06:13:19] local.CRITICAL: ****************************2  
[2023-03-26 06:13:19] local.CRITICAL: ****************************  
[2023-03-26 06:13:19] local.CRITICAL:   
[2023-03-26 06:13:19] local.CRITICAL: ****************************  
[2023-03-26 06:13:19] local.INFO: {
  "ClientBalanceResult": "23244.8300"
}  
[2023-03-26 06:13:19] local.INFO: array (
  'ClientBalanceResult' => '23244.8300',
)  
[2023-03-26 06:13:19] local.DEBUG: lattttef  
[2023-03-26 06:13:19] local.DEBUG: array (
  'ClientBalanceResult' => '23244.8300',
)  
[2023-03-26 06:13:19] local.INFO: transaction14  
[2023-03-26 06:13:19] local.INFO: first inquery phone = *********  
[2023-03-26 06:13:22] local.DEBUG: response querySubBalance  
[2023-03-26 06:13:22] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#0.00 B#21-04-2023#0#0##2500.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-26 06:13:22] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '0.00 B',
  4 => '21-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '2500.00',
  9 => '4G 25',
)  
[2023-03-26 06:13:22] local.DEBUG: print  before faction by provider price  
[2023-03-26 06:13:22] local.DEBUG: print  after faction by provider price  
[2023-03-26 06:13:22] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-26 06:13:22] local.DEBUG: print1  
[2023-03-26 06:13:22] local.DEBUG: print  2  
[2023-03-26 06:13:22] local.INFO: transaction1  
[2023-03-26 06:13:22] local.INFO: transaction2  
[2023-03-26 06:13:22] local.INFO: transaction3  
[2023-03-26 06:13:22] local.INFO: transaction4  
[2023-03-26 06:13:22] local.INFO: transaction4  
[2023-03-26 06:13:22] local.INFO: transaction5  
[2023-03-26 06:13:22] local.INFO: transaction6  
[2023-03-26 06:13:22] local.INFO: transaction7  
[2023-03-26 06:13:22] local.DEBUG: array (
  'AMT' => 4000.0,
  'CType' => 0,
  'FID' => 86,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '*********',
  'State' => 0,
  'lateflog' => '588030',
)  
[2023-03-26 06:13:22] local.INFO: transaction8  
[2023-03-26 06:13:22] local.INFO: transaction9  
[2023-03-26 06:13:22] local.INFO: transaction10  
[2023-03-26 06:13:22] local.INFO: transaction11  
[2023-03-26 06:13:22] local.INFO: 12  
[2023-03-26 06:13:22] local.INFO: transaction13  
[2023-03-26 06:13:22] local.INFO: transaction14  
[2023-03-26 06:13:22] local.INFO: transaction19  
[2023-03-26 06:13:22] local.INFO: transaction15  
[2023-03-26 06:13:22] local.INFO: transaction16  
[2023-03-26 06:13:22] local.INFO: 98#*********#4000.00#0  
[2023-03-26 06:13:22] local.INFO: transaction18  
[2023-03-26 06:13:22] local.INFO: array (
  0 => 'Error',
  1 => '9',
  2 => 'Payment was failed  - 5521',
)  
[2023-03-26 06:13:23] local.DEBUG: {"Number":********,"ServiceID":200,"NetworkID":null,"SubscriberNumber":"*********","Amount":4000,"FactionID":86,"RegionID":null,"LineType":"\u063a\u064a\u0631 \u0645\u0639\u0631\u0648\u0641","Date":"2023-03-26 06:13:22","Status":0,"Note":"\u0627\u0644\u0639\u0645\u0644\u064a\u0629 \u0641\u0627\u0634\u0644\u0629","CreditorAccountID":379015,"CurrencyID":1,"DebitorAccountID":"588030","AgentID":null,"RefNumber":null,"TransactionID":********,"ProviderID":10028,"EntryID":4400742,"PaymentEntryID":null,"Channel":2,"CreatedBy":"584329","BranchBy":null,"CreatedTime":"2023-03-26 06:13:22","BranchID":"1","ProviderRM":"\u0641\u0634\u0644 \u0641\u064a \u0627\u062c\u0631\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629","ProviderPrice":"4000.00","SubNote":null,"Datestamb":"********","UniqueNo":"********061322","Quantity":"4000.0000","UnitPrice":1,"UnitCost":1,"CostAmount":"4000.00","DifferentialAmount":0,"CommissionAmount":0,"Discount":0,"TotalCost":4000,"TotalAmount":4000,"Profits":0,"Method":2,"Type":0,"Class":0,"LType":"1","OperatorID":1,"AppTechApi":0,"BillNumber":"20000********","BillState":0,"Debited":0,"ByChild":0,"IsDirect":1,"BundleName":"\u0641\u0626\u0629 25 \u062c\u064a\u062c\u0627 4000 \u0631\u064a\u0627\u0644","BundleCode":"30","ExCode":null,"TransNumber":"********061322","OperationID":0,"AccountID":"588030","State":0,"StateClass":"\u0627\u0644\u0639\u0645\u0644\u064a\u0629 \u0641\u0627\u0634\u0644\u0629","Identifier":"34dffd789979fe43","AdminNote":"\u0641\u0634\u0644 \u0641\u064a \u0627\u062c\u0631\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629 \u062a\u0645 \u0627\u0644\u063a\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629","AccountNote":"\u0641\u0634\u0644 \u0641\u064a \u0627\u062c\u0631\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629 \u062a\u0645 \u0627\u0644\u063a\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629","Description":null,"Responded":1,"RequestInfo":"200#*********#4000#86","ResponseTime":"2023-03-26 06:13:22","ResponseStatus":0,"ExecutionPeroid":"22","FaildRequest":0,"FailedReason":"\u0641\u0634\u0644 \u0641\u064a \u0627\u062c\u0631\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629","FailedType":0,"Cured":0,"CuredBy":null,"CuredInfo":null,"InspectInfo":null,"Flag":2,"Action":0,"QuotaionID":0,"SyncID":0,"id":4223912,"ResponseInfo":"Error#9#Payment was failed  - 5521"}  
[2023-03-26 06:13:23] local.DEBUG: response faild  
[2023-03-26 06:13:23] local.DEBUG: validateee  
[2023-03-26 06:13:23] local.DEBUG: response faild  
[2023-03-26 06:14:14] local.INFO: header  
[2023-03-26 06:14:14] local.CRITICAL: ****************************1  
[2023-03-26 06:14:14] local.ALERT: reach here  
[2023-03-26 06:14:14] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '4000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'أربعة ألف  ر.ي.',
  ),
)  
[2023-03-26 06:14:14] local.ERROR: المبلغ  
[2023-03-26 06:14:14] local.ERROR: 4,000.00  
[2023-03-26 06:14:14] local.ERROR: مبلغ وقدرة  
[2023-03-26 06:14:14] local.ALERT: 200  
[2023-03-26 06:14:14] local.ALERT: 3  
[2023-03-26 06:14:14] local.ALERT: 200  
[2023-03-26 06:14:14] local.ALERT: 3  
[2023-03-26 06:14:14] local.ALERT: 200  
[2023-03-26 06:14:14] local.ALERT: 3  
[2023-03-26 06:14:14] local.ALERT: 200  
[2023-03-26 06:14:14] local.ALERT: 3  
[2023-03-26 06:14:14] local.ALERT: 200  
[2023-03-26 06:14:14] local.ALERT: 3  
[2023-03-26 06:14:14] local.ALERT: 200  
[2023-03-26 06:14:14] local.ALERT: 3  
[2023-03-26 06:14:14] local.ALERT: 200  
[2023-03-26 06:14:14] local.ALERT: 3  
[2023-03-26 06:14:14] local.ALERT: 200  
[2023-03-26 06:14:14] local.ALERT: 3  
[2023-03-26 06:14:14] local.ALERT: 200  
[2023-03-26 06:14:14] local.ALERT: 3  
[2023-03-26 06:14:14] local.ALERT: 200  
[2023-03-26 06:14:14] local.ALERT: 2  
[2023-03-26 06:14:14] local.ALERT: 200  
[2023-03-26 06:14:14] local.ALERT: 2  
[2023-03-26 06:14:14] local.ALERT: 200  
[2023-03-26 06:14:14] local.ALERT: 2  
[2023-03-26 06:14:14] local.ALERT: 200  
[2023-03-26 06:14:14] local.ALERT: 2  
[2023-03-26 06:14:14] local.ALERT: 200  
[2023-03-26 06:14:14] local.ALERT: 2  
[2023-03-26 06:14:14] local.ALERT: 200  
[2023-03-26 06:14:14] local.ALERT: 2  
[2023-03-26 06:14:14] local.ALERT: 200  
[2023-03-26 06:14:14] local.ALERT: 2  
[2023-03-26 06:14:14] local.ALERT: 200  
[2023-03-26 06:14:14] local.ALERT: 1  
[2023-03-26 06:14:14] local.ALERT: 200  
[2023-03-26 06:14:14] local.ALERT: 1  
[2023-03-26 06:14:14] local.ALERT: 200  
[2023-03-26 06:14:14] local.ALERT: 1  
[2023-03-26 06:14:14] local.ALERT: 200  
[2023-03-26 06:14:14] local.ALERT: 1  
[2023-03-26 06:14:14] local.ALERT: 200  
[2023-03-26 06:14:14] local.ALERT: 1  
[2023-03-26 06:14:14] local.ALERT: 200  
[2023-03-26 06:14:14] local.ALERT: 2  
[2023-03-26 06:14:14] local.ALERT: 200  
[2023-03-26 06:14:14] local.ALERT: 10013  
[2023-03-26 06:14:14] local.ALERT: 200  
[2023-03-26 06:14:14] local.ALERT: 10013  
[2023-03-26 06:14:14] local.ALERT: 200  
[2023-03-26 06:14:14] local.ALERT: 10013  
[2023-03-26 06:14:14] local.ALERT: 200  
[2023-03-26 06:14:14] local.ALERT: 10013  
[2023-03-26 06:14:14] local.ALERT: 200  
[2023-03-26 06:14:14] local.ALERT: 1  
[2023-03-26 06:14:14] local.ALERT: 200  
[2023-03-26 06:14:14] local.ALERT: 3  
[2023-03-26 06:14:14] local.ALERT: 200  
[2023-03-26 06:14:14] local.ALERT: 3  
[2023-03-26 06:14:14] local.ALERT: 200  
[2023-03-26 06:14:14] local.ALERT: 40  
[2023-03-26 06:14:14] local.ALERT: 200  
[2023-03-26 06:14:14] local.ALERT: 40  
[2023-03-26 06:14:14] local.ALERT: 200  
[2023-03-26 06:14:14] local.ALERT: 40  
[2023-03-26 06:14:14] local.ALERT: 200  
[2023-03-26 06:14:14] local.ALERT: 40  
[2023-03-26 06:14:14] local.ALERT: 200  
[2023-03-26 06:14:14] local.ALERT: 200  
[2023-03-26 06:14:14] local.WARNING: 1  
[2023-03-26 06:14:14] local.WARNING: array (
  'ID' => 86,
  'Name' => 'فئة 25 جيجا 4000 ريال',
  'ServiceID' => 200,
  'Price' => 4000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 4000.0,
)  
[2023-03-26 06:14:14] local.ALERT: 200  
[2023-03-26 06:14:14] local.ALERT: 200  
[2023-03-26 06:14:14] local.ALERT: 200  
[2023-03-26 06:14:14] local.ALERT: 200  
[2023-03-26 06:14:14] local.ALERT: 200  
[2023-03-26 06:14:15] local.ALERT: 200  
[2023-03-26 06:14:15] local.ALERT: 200  
[2023-03-26 06:14:15] local.ALERT: 200  
[2023-03-26 06:14:15] local.ALERT: 200  
[2023-03-26 06:14:15] local.ALERT: 200  
[2023-03-26 06:14:15] local.ALERT: 200  
[2023-03-26 06:14:15] local.ALERT: 40  
[2023-03-26 06:14:15] local.CRITICAL: ****************************2  
[2023-03-26 06:14:15] local.CRITICAL: ****************************  
[2023-03-26 06:14:15] local.CRITICAL:   
[2023-03-26 06:14:15] local.CRITICAL: ****************************  
[2023-03-26 06:14:15] local.INFO: {
  "ClientBalanceResult": "23244.8300"
}  
[2023-03-26 06:14:15] local.INFO: array (
  'ClientBalanceResult' => '23244.8300',
)  
[2023-03-26 06:14:15] local.DEBUG: lattttef  
[2023-03-26 06:14:15] local.DEBUG: array (
  'ClientBalanceResult' => '23244.8300',
)  
[2023-03-26 06:14:15] local.INFO: transaction14  
[2023-03-26 06:14:15] local.INFO: first inquery phone = *********  
[2023-03-26 06:14:18] local.DEBUG: response querySubBalance  
[2023-03-26 06:14:18] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#0.00 B#21-04-2023#0#0##2500.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-26 06:14:18] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '0.00 B',
  4 => '21-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '2500.00',
  9 => '4G 25',
)  
[2023-03-26 06:14:18] local.DEBUG: print  before faction by provider price  
[2023-03-26 06:14:18] local.DEBUG: print  after faction by provider price  
[2023-03-26 06:14:18] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-26 06:14:18] local.DEBUG: print1  
[2023-03-26 06:14:18] local.DEBUG: print  2  
[2023-03-26 06:14:18] local.INFO: transaction1  
[2023-03-26 06:14:18] local.INFO: transaction2  
[2023-03-26 06:14:18] local.INFO: transaction3  
[2023-03-26 06:14:18] local.INFO: transaction4  
[2023-03-26 06:14:18] local.INFO: transaction4  
[2023-03-26 06:14:18] local.INFO: transaction5  
[2023-03-26 06:14:18] local.INFO: transaction6  
[2023-03-26 06:14:18] local.INFO: transaction7  
[2023-03-26 06:14:18] local.DEBUG: array (
  'AMT' => 4000.0,
  'CType' => 0,
  'FID' => 86,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '*********',
  'State' => 0,
  'lateflog' => '588030',
)  
[2023-03-26 06:14:18] local.INFO: transaction8  
[2023-03-26 06:14:18] local.INFO: transaction9  
[2023-03-26 06:14:18] local.INFO: transaction10  
[2023-03-26 06:14:18] local.INFO: transaction11  
[2023-03-26 06:14:18] local.INFO: 12  
[2023-03-26 06:14:18] local.INFO: transaction13  
[2023-03-26 06:14:18] local.INFO: transaction14  
[2023-03-26 06:14:18] local.INFO: transaction19  
[2023-03-26 06:14:18] local.INFO: transaction15  
[2023-03-26 06:14:18] local.INFO: transaction16  
[2023-03-26 06:14:18] local.INFO: 98#*********#4000.00#0  
[2023-03-26 06:14:19] local.INFO: transaction18  
[2023-03-26 06:14:19] local.INFO: array (
  0 => 'Error',
  1 => '9',
  2 => 'Payment was failed  - 5521',
)  
[2023-03-26 06:14:19] local.DEBUG: {"Number":********,"ServiceID":200,"NetworkID":null,"SubscriberNumber":"*********","Amount":4000,"FactionID":86,"RegionID":null,"LineType":"\u063a\u064a\u0631 \u0645\u0639\u0631\u0648\u0641","Date":"2023-03-26 06:14:18","Status":0,"Note":"\u0627\u0644\u0639\u0645\u0644\u064a\u0629 \u0641\u0627\u0634\u0644\u0629","CreditorAccountID":379015,"CurrencyID":1,"DebitorAccountID":"588030","AgentID":null,"RefNumber":null,"TransactionID":********,"ProviderID":10028,"EntryID":4400743,"PaymentEntryID":null,"Channel":2,"CreatedBy":"584329","BranchBy":null,"CreatedTime":"2023-03-26 06:14:18","BranchID":"1","ProviderRM":"\u0641\u0634\u0644 \u0641\u064a \u0627\u062c\u0631\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629","ProviderPrice":"4000.00","SubNote":null,"Datestamb":"********","UniqueNo":"********061418","Quantity":"4000.0000","UnitPrice":1,"UnitCost":1,"CostAmount":"4000.00","DifferentialAmount":0,"CommissionAmount":0,"Discount":0,"TotalCost":4000,"TotalAmount":4000,"Profits":0,"Method":2,"Type":0,"Class":0,"LType":"1","OperatorID":1,"AppTechApi":0,"BillNumber":"20000********","BillState":0,"Debited":0,"ByChild":0,"IsDirect":1,"BundleName":"\u0641\u0626\u0629 25 \u062c\u064a\u062c\u0627 4000 \u0631\u064a\u0627\u0644","BundleCode":"30","ExCode":null,"TransNumber":"********061418","OperationID":0,"AccountID":"588030","State":0,"StateClass":"\u0627\u0644\u0639\u0645\u0644\u064a\u0629 \u0641\u0627\u0634\u0644\u0629","Identifier":"34dffd789979fe43","AdminNote":"\u0641\u0634\u0644 \u0641\u064a \u0627\u062c\u0631\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629 \u062a\u0645 \u0627\u0644\u063a\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629","AccountNote":"\u0641\u0634\u0644 \u0641\u064a \u0627\u062c\u0631\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629 \u062a\u0645 \u0627\u0644\u063a\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629","Description":null,"Responded":1,"RequestInfo":"200#*********#4000#86","ResponseTime":"2023-03-26 06:14:19","ResponseStatus":0,"ExecutionPeroid":"19","FaildRequest":0,"FailedReason":"\u0641\u0634\u0644 \u0641\u064a \u0627\u062c\u0631\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629","FailedType":0,"Cured":0,"CuredBy":null,"CuredInfo":null,"InspectInfo":null,"Flag":2,"Action":0,"QuotaionID":0,"SyncID":0,"id":4223913,"ResponseInfo":"Error#9#Payment was failed  - 5521"}  
[2023-03-26 06:14:19] local.DEBUG: response faild  
[2023-03-26 06:14:19] local.DEBUG: validateee  
[2023-03-26 06:14:19] local.DEBUG: response faild  
[2023-03-26 07:12:17] local.INFO: header  
[2023-03-26 07:12:17] local.CRITICAL: ****************************1  
[2023-03-26 07:12:17] local.ALERT: reach here  
[2023-03-26 07:12:17] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '4000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'أربعة ألف  ر.ي.',
  ),
)  
[2023-03-26 07:12:17] local.ERROR: المبلغ  
[2023-03-26 07:12:17] local.ERROR: 4,000.00  
[2023-03-26 07:12:17] local.ERROR: مبلغ وقدرة  
[2023-03-26 07:12:17] local.ALERT: 200  
[2023-03-26 07:12:17] local.ALERT: 3  
[2023-03-26 07:12:17] local.ALERT: 200  
[2023-03-26 07:12:17] local.ALERT: 3  
[2023-03-26 07:12:17] local.ALERT: 200  
[2023-03-26 07:12:17] local.ALERT: 3  
[2023-03-26 07:12:17] local.ALERT: 200  
[2023-03-26 07:12:17] local.ALERT: 3  
[2023-03-26 07:12:17] local.ALERT: 200  
[2023-03-26 07:12:17] local.ALERT: 3  
[2023-03-26 07:12:17] local.ALERT: 200  
[2023-03-26 07:12:17] local.ALERT: 3  
[2023-03-26 07:12:17] local.ALERT: 200  
[2023-03-26 07:12:17] local.ALERT: 3  
[2023-03-26 07:12:17] local.ALERT: 200  
[2023-03-26 07:12:17] local.ALERT: 3  
[2023-03-26 07:12:17] local.ALERT: 200  
[2023-03-26 07:12:17] local.ALERT: 3  
[2023-03-26 07:12:17] local.ALERT: 200  
[2023-03-26 07:12:17] local.ALERT: 2  
[2023-03-26 07:12:17] local.ALERT: 200  
[2023-03-26 07:12:17] local.ALERT: 2  
[2023-03-26 07:12:17] local.ALERT: 200  
[2023-03-26 07:12:17] local.ALERT: 2  
[2023-03-26 07:12:17] local.ALERT: 200  
[2023-03-26 07:12:17] local.ALERT: 2  
[2023-03-26 07:12:17] local.ALERT: 200  
[2023-03-26 07:12:17] local.ALERT: 2  
[2023-03-26 07:12:17] local.ALERT: 200  
[2023-03-26 07:12:17] local.ALERT: 2  
[2023-03-26 07:12:17] local.ALERT: 200  
[2023-03-26 07:12:17] local.ALERT: 2  
[2023-03-26 07:12:17] local.ALERT: 200  
[2023-03-26 07:12:17] local.ALERT: 1  
[2023-03-26 07:12:17] local.ALERT: 200  
[2023-03-26 07:12:17] local.ALERT: 1  
[2023-03-26 07:12:17] local.ALERT: 200  
[2023-03-26 07:12:17] local.ALERT: 1  
[2023-03-26 07:12:17] local.ALERT: 200  
[2023-03-26 07:12:17] local.ALERT: 1  
[2023-03-26 07:12:17] local.ALERT: 200  
[2023-03-26 07:12:17] local.ALERT: 1  
[2023-03-26 07:12:17] local.ALERT: 200  
[2023-03-26 07:12:17] local.ALERT: 2  
[2023-03-26 07:12:17] local.ALERT: 200  
[2023-03-26 07:12:17] local.ALERT: 10013  
[2023-03-26 07:12:17] local.ALERT: 200  
[2023-03-26 07:12:17] local.ALERT: 10013  
[2023-03-26 07:12:17] local.ALERT: 200  
[2023-03-26 07:12:17] local.ALERT: 10013  
[2023-03-26 07:12:17] local.ALERT: 200  
[2023-03-26 07:12:17] local.ALERT: 10013  
[2023-03-26 07:12:17] local.ALERT: 200  
[2023-03-26 07:12:17] local.ALERT: 1  
[2023-03-26 07:12:17] local.ALERT: 200  
[2023-03-26 07:12:17] local.ALERT: 3  
[2023-03-26 07:12:17] local.ALERT: 200  
[2023-03-26 07:12:17] local.ALERT: 3  
[2023-03-26 07:12:17] local.ALERT: 200  
[2023-03-26 07:12:17] local.ALERT: 40  
[2023-03-26 07:12:17] local.ALERT: 200  
[2023-03-26 07:12:17] local.ALERT: 40  
[2023-03-26 07:12:17] local.ALERT: 200  
[2023-03-26 07:12:17] local.ALERT: 40  
[2023-03-26 07:12:17] local.ALERT: 200  
[2023-03-26 07:12:17] local.ALERT: 40  
[2023-03-26 07:12:17] local.ALERT: 200  
[2023-03-26 07:12:17] local.ALERT: 200  
[2023-03-26 07:12:17] local.WARNING: 1  
[2023-03-26 07:12:17] local.WARNING: array (
  'ID' => 86,
  'Name' => 'فئة 25 جيجا 4000 ريال',
  'ServiceID' => 200,
  'Price' => 4000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 4000.0,
)  
[2023-03-26 07:12:17] local.ALERT: 200  
[2023-03-26 07:12:17] local.ALERT: 200  
[2023-03-26 07:12:17] local.ALERT: 200  
[2023-03-26 07:12:17] local.ALERT: 200  
[2023-03-26 07:12:17] local.ALERT: 200  
[2023-03-26 07:12:17] local.ALERT: 200  
[2023-03-26 07:12:17] local.ALERT: 200  
[2023-03-26 07:12:17] local.ALERT: 200  
[2023-03-26 07:12:17] local.ALERT: 200  
[2023-03-26 07:12:17] local.ALERT: 200  
[2023-03-26 07:12:17] local.ALERT: 200  
[2023-03-26 07:12:17] local.ALERT: 40  
[2023-03-26 07:12:17] local.CRITICAL: ****************************2  
[2023-03-26 07:12:17] local.CRITICAL: ****************************  
[2023-03-26 07:12:17] local.CRITICAL:   
[2023-03-26 07:12:17] local.CRITICAL: ****************************  
[2023-03-26 07:12:17] local.INFO: {
  "ClientBalanceResult": "22754.8800"
}  
[2023-03-26 07:12:17] local.INFO: array (
  'ClientBalanceResult' => '22754.8800',
)  
[2023-03-26 07:12:17] local.DEBUG: lattttef  
[2023-03-26 07:12:17] local.DEBUG: array (
  'ClientBalanceResult' => '22754.8800',
)  
[2023-03-26 07:12:17] local.INFO: transaction14  
[2023-03-26 07:12:17] local.INFO: first inquery phone = *********  
[2023-03-26 07:12:21] local.DEBUG: response querySubBalance  
[2023-03-26 07:12:21] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#0.00 B#21-04-2023#0#0##2500.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-26 07:12:21] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '0.00 B',
  4 => '21-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '2500.00',
  9 => '4G 25',
)  
[2023-03-26 07:12:21] local.DEBUG: print  before faction by provider price  
[2023-03-26 07:12:21] local.DEBUG: print  after faction by provider price  
[2023-03-26 07:12:21] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-26 07:12:21] local.DEBUG: print1  
[2023-03-26 07:12:21] local.DEBUG: print  2  
[2023-03-26 07:12:21] local.INFO: transaction1  
[2023-03-26 07:12:21] local.INFO: transaction2  
[2023-03-26 07:12:21] local.INFO: transaction3  
[2023-03-26 07:12:21] local.INFO: transaction4  
[2023-03-26 07:12:21] local.INFO: transaction4  
[2023-03-26 07:12:21] local.INFO: transaction5  
[2023-03-26 07:12:21] local.INFO: transaction6  
[2023-03-26 07:12:21] local.INFO: transaction7  
[2023-03-26 07:12:21] local.DEBUG: array (
  'AMT' => 4000.0,
  'CType' => 0,
  'FID' => 86,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '*********',
  'State' => 0,
  'lateflog' => '588030',
)  
[2023-03-26 07:12:21] local.INFO: transaction8  
[2023-03-26 07:12:22] local.INFO: transaction9  
[2023-03-26 07:12:22] local.INFO: transaction10  
[2023-03-26 07:12:22] local.INFO: transaction11  
[2023-03-26 07:12:22] local.INFO: 12  
[2023-03-26 07:12:22] local.INFO: transaction13  
[2023-03-26 07:12:22] local.INFO: transaction14  
[2023-03-26 07:12:22] local.INFO: transaction19  
[2023-03-26 07:12:22] local.INFO: transaction15  
[2023-03-26 07:12:22] local.INFO: transaction16  
[2023-03-26 07:12:22] local.INFO: 98#*********#4000.00#0  
[2023-03-26 07:12:22] local.INFO: transaction18  
[2023-03-26 07:12:22] local.INFO: array (
  0 => 'Error',
  1 => '9',
  2 => 'Payment was failed  - 5521',
)  
[2023-03-26 07:12:22] local.DEBUG: {"Number":********,"ServiceID":200,"NetworkID":null,"SubscriberNumber":"*********","Amount":4000,"FactionID":86,"RegionID":null,"LineType":"\u063a\u064a\u0631 \u0645\u0639\u0631\u0648\u0641","Date":"2023-03-26 07:12:22","Status":0,"Note":"\u0627\u0644\u0639\u0645\u0644\u064a\u0629 \u0641\u0627\u0634\u0644\u0629","CreditorAccountID":379015,"CurrencyID":1,"DebitorAccountID":"588030","AgentID":null,"RefNumber":null,"TransactionID":********,"ProviderID":10028,"EntryID":4400753,"PaymentEntryID":null,"Channel":2,"CreatedBy":"584329","BranchBy":null,"CreatedTime":"2023-03-26 07:12:22","BranchID":"1","ProviderRM":"\u0641\u0634\u0644 \u0641\u064a \u0627\u062c\u0631\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629","ProviderPrice":"4000.00","SubNote":null,"Datestamb":"********","UniqueNo":"********071222","Quantity":"4000.0000","UnitPrice":1,"UnitCost":1,"CostAmount":"4000.00","DifferentialAmount":0,"CommissionAmount":0,"Discount":0,"TotalCost":4000,"TotalAmount":4000,"Profits":0,"Method":2,"Type":0,"Class":0,"LType":"1","OperatorID":1,"AppTechApi":0,"BillNumber":"20000********","BillState":0,"Debited":0,"ByChild":0,"IsDirect":1,"BundleName":"\u0641\u0626\u0629 25 \u062c\u064a\u062c\u0627 4000 \u0631\u064a\u0627\u0644","BundleCode":"30","ExCode":null,"TransNumber":"********071222","OperationID":0,"AccountID":"588030","State":0,"StateClass":"\u0627\u0644\u0639\u0645\u0644\u064a\u0629 \u0641\u0627\u0634\u0644\u0629","Identifier":"34dffd789979fe43","AdminNote":"\u0641\u0634\u0644 \u0641\u064a \u0627\u062c\u0631\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629 \u062a\u0645 \u0627\u0644\u063a\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629","AccountNote":"\u0641\u0634\u0644 \u0641\u064a \u0627\u062c\u0631\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629 \u062a\u0645 \u0627\u0644\u063a\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629","Description":null,"Responded":1,"RequestInfo":"200#*********#4000#86","ResponseTime":"2023-03-26 07:12:22","ResponseStatus":0,"ExecutionPeroid":"22","FaildRequest":0,"FailedReason":"\u0641\u0634\u0644 \u0641\u064a \u0627\u062c\u0631\u0627\u0621 \u0627\u0644\u0639\u0645\u0644\u064a\u0629","FailedType":0,"Cured":0,"CuredBy":null,"CuredInfo":null,"InspectInfo":null,"Flag":2,"Action":0,"QuotaionID":0,"SyncID":0,"id":4223925,"ResponseInfo":"Error#9#Payment was failed  - 5521"}  
[2023-03-26 07:12:22] local.DEBUG: response faild  
[2023-03-26 07:12:22] local.DEBUG: validateee  
[2023-03-26 07:12:22] local.DEBUG: response faild  
[2023-03-26 13:24:08] local.INFO: header  
[2023-03-26 13:24:08] local.INFO: header after fliter  
[2023-03-26 13:24:08] local.INFO: Body  after fliter  
[2023-03-26 13:24:08] local.INFO: array (
)  
[2023-03-26 13:24:08] local.INFO: transaction14  
[2023-03-26 13:24:08] local.INFO: first inquery phone = *********  
[2023-03-26 13:24:11] local.DEBUG: response querySubBalance  
[2023-03-26 13:24:11] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#8,000.00#8,000.00#6.55 MB#18-04-2023#0#0##8500.00#4G 60</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-26 13:24:11] local.DEBUG: array (
  0 => 'OK',
  1 => '8,000.00',
  2 => '8,000.00',
  3 => '6.55 MB',
  4 => '18-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '8500.00',
  9 => '4G 60',
)  
[2023-03-26 13:24:11] local.DEBUG: print  before faction by provider price  
[2023-03-26 13:24:11] local.DEBUG: print  after faction by provider price  
[2023-03-26 13:24:11] local.DEBUG: فئة 60 جيجا 8000 ريال  
[2023-03-26 13:24:11] local.DEBUG: print1  
[2023-03-26 13:24:11] local.DEBUG: print  2  
[2023-03-26 13:24:24] local.INFO: header  
[2023-03-26 13:24:24] local.CRITICAL: ****************************1  
[2023-03-26 13:24:24] local.ALERT: reach here  
[2023-03-26 13:24:24] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '8000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ثمانية ألف  ر.ي.',
  ),
)  
[2023-03-26 13:24:24] local.ERROR: المبلغ  
[2023-03-26 13:24:24] local.ERROR: 8,000.00  
[2023-03-26 13:24:24] local.ERROR: مبلغ وقدرة  
[2023-03-26 13:24:24] local.ALERT: 200  
[2023-03-26 13:24:24] local.ALERT: 3  
[2023-03-26 13:24:24] local.ALERT: 200  
[2023-03-26 13:24:24] local.ALERT: 3  
[2023-03-26 13:24:24] local.ALERT: 200  
[2023-03-26 13:24:24] local.ALERT: 3  
[2023-03-26 13:24:24] local.ALERT: 200  
[2023-03-26 13:24:24] local.ALERT: 3  
[2023-03-26 13:24:24] local.ALERT: 200  
[2023-03-26 13:24:24] local.ALERT: 3  
[2023-03-26 13:24:24] local.ALERT: 200  
[2023-03-26 13:24:24] local.ALERT: 3  
[2023-03-26 13:24:24] local.ALERT: 200  
[2023-03-26 13:24:24] local.ALERT: 3  
[2023-03-26 13:24:24] local.ALERT: 200  
[2023-03-26 13:24:24] local.ALERT: 3  
[2023-03-26 13:24:24] local.ALERT: 200  
[2023-03-26 13:24:24] local.ALERT: 3  
[2023-03-26 13:24:24] local.ALERT: 200  
[2023-03-26 13:24:24] local.ALERT: 2  
[2023-03-26 13:24:24] local.ALERT: 200  
[2023-03-26 13:24:24] local.ALERT: 2  
[2023-03-26 13:24:24] local.ALERT: 200  
[2023-03-26 13:24:24] local.ALERT: 2  
[2023-03-26 13:24:24] local.ALERT: 200  
[2023-03-26 13:24:24] local.ALERT: 2  
[2023-03-26 13:24:24] local.ALERT: 200  
[2023-03-26 13:24:24] local.ALERT: 2  
[2023-03-26 13:24:24] local.ALERT: 200  
[2023-03-26 13:24:24] local.ALERT: 2  
[2023-03-26 13:24:24] local.ALERT: 200  
[2023-03-26 13:24:24] local.ALERT: 2  
[2023-03-26 13:24:24] local.ALERT: 200  
[2023-03-26 13:24:24] local.ALERT: 1  
[2023-03-26 13:24:24] local.ALERT: 200  
[2023-03-26 13:24:24] local.ALERT: 1  
[2023-03-26 13:24:24] local.ALERT: 200  
[2023-03-26 13:24:24] local.ALERT: 1  
[2023-03-26 13:24:24] local.ALERT: 200  
[2023-03-26 13:24:24] local.ALERT: 1  
[2023-03-26 13:24:24] local.ALERT: 200  
[2023-03-26 13:24:24] local.ALERT: 1  
[2023-03-26 13:24:24] local.ALERT: 200  
[2023-03-26 13:24:24] local.ALERT: 2  
[2023-03-26 13:24:24] local.ALERT: 200  
[2023-03-26 13:24:24] local.ALERT: 10013  
[2023-03-26 13:24:24] local.ALERT: 200  
[2023-03-26 13:24:24] local.ALERT: 10013  
[2023-03-26 13:24:24] local.ALERT: 200  
[2023-03-26 13:24:24] local.ALERT: 10013  
[2023-03-26 13:24:24] local.ALERT: 200  
[2023-03-26 13:24:24] local.ALERT: 10013  
[2023-03-26 13:24:24] local.ALERT: 200  
[2023-03-26 13:24:24] local.ALERT: 1  
[2023-03-26 13:24:24] local.ALERT: 200  
[2023-03-26 13:24:24] local.ALERT: 3  
[2023-03-26 13:24:24] local.ALERT: 200  
[2023-03-26 13:24:24] local.ALERT: 3  
[2023-03-26 13:24:24] local.ALERT: 200  
[2023-03-26 13:24:24] local.ALERT: 40  
[2023-03-26 13:24:24] local.ALERT: 200  
[2023-03-26 13:24:24] local.ALERT: 40  
[2023-03-26 13:24:24] local.ALERT: 200  
[2023-03-26 13:24:24] local.ALERT: 40  
[2023-03-26 13:24:24] local.ALERT: 200  
[2023-03-26 13:24:24] local.ALERT: 40  
[2023-03-26 13:24:24] local.ALERT: 200  
[2023-03-26 13:24:24] local.ALERT: 200  
[2023-03-26 13:24:24] local.ALERT: 200  
[2023-03-26 13:24:24] local.ALERT: 200  
[2023-03-26 13:24:24] local.WARNING: 1  
[2023-03-26 13:24:24] local.WARNING: array (
  'ID' => 87,
  'Name' => 'فئة 60 جيجا 8000 ريال',
  'ServiceID' => 200,
  'Price' => 8000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 8000.0,
)  
[2023-03-26 13:24:24] local.ALERT: 200  
[2023-03-26 13:24:24] local.ALERT: 200  
[2023-03-26 13:24:24] local.ALERT: 200  
[2023-03-26 13:24:24] local.ALERT: 200  
[2023-03-26 13:24:24] local.ALERT: 200  
[2023-03-26 13:24:24] local.ALERT: 200  
[2023-03-26 13:24:24] local.ALERT: 200  
[2023-03-26 13:24:24] local.ALERT: 200  
[2023-03-26 13:24:24] local.ALERT: 200  
[2023-03-26 13:24:24] local.ALERT: 40  
[2023-03-26 13:24:24] local.CRITICAL: ****************************2  
[2023-03-26 13:24:24] local.CRITICAL: ****************************  
[2023-03-26 13:24:24] local.CRITICAL:   
[2023-03-26 13:24:24] local.CRITICAL: ****************************  
[2023-03-26 13:24:25] local.INFO: {
  "ClientBalanceResult": "361190.3600"
}  
[2023-03-26 13:24:25] local.INFO: array (
  'ClientBalanceResult' => '361190.3600',
)  
[2023-03-26 13:24:25] local.DEBUG: lattttef  
[2023-03-26 13:24:25] local.DEBUG: array (
  'ClientBalanceResult' => '361190.3600',
)  
[2023-03-26 13:24:25] local.INFO: transaction14  
[2023-03-26 13:24:25] local.INFO: first inquery phone = *********  
[2023-03-26 13:24:28] local.DEBUG: response querySubBalance  
[2023-03-26 13:24:28] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#8,000.00#8,000.00#6.55 MB#18-04-2023#0#0##8500.00#4G 60</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-26 13:24:28] local.DEBUG: array (
  0 => 'OK',
  1 => '8,000.00',
  2 => '8,000.00',
  3 => '6.55 MB',
  4 => '18-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '8500.00',
  9 => '4G 60',
)  
[2023-03-26 13:24:28] local.DEBUG: print  before faction by provider price  
[2023-03-26 13:24:28] local.DEBUG: print  after faction by provider price  
[2023-03-26 13:24:28] local.DEBUG: فئة 60 جيجا 8000 ريال  
[2023-03-26 13:24:28] local.DEBUG: print1  
[2023-03-26 13:24:28] local.DEBUG: print  2  
[2023-03-26 13:24:28] local.INFO: transaction1  
[2023-03-26 13:24:28] local.INFO: transaction2  
[2023-03-26 13:24:28] local.INFO: transaction3  
[2023-03-26 13:24:28] local.INFO: transaction4  
[2023-03-26 13:24:28] local.INFO: transaction4  
[2023-03-26 13:24:28] local.INFO: transaction5  
[2023-03-26 13:24:28] local.INFO: transaction6  
[2023-03-26 13:24:28] local.INFO: transaction7  
[2023-03-26 13:24:28] local.DEBUG: array (
  'AMT' => 8000.0,
  'CType' => 0,
  'FID' => 87,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '*********',
  'State' => 0,
  'lateflog' => '337620',
)  
[2023-03-26 13:24:28] local.INFO: transaction8  
[2023-03-26 13:24:29] local.INFO: transaction9  
[2023-03-26 13:24:29] local.INFO: transaction10  
[2023-03-26 13:24:29] local.INFO: transaction11  
[2023-03-26 13:24:29] local.INFO: 12  
[2023-03-26 13:24:29] local.INFO: transaction13  
[2023-03-26 13:24:29] local.INFO: transaction14  
[2023-03-26 13:24:29] local.INFO: transaction19  
[2023-03-26 13:24:29] local.INFO: transaction15  
[2023-03-26 13:24:29] local.INFO: transaction16  
[2023-03-26 13:24:29] local.INFO: 98#*********#8000.00#0  
[2023-03-26 13:24:38] local.INFO: transaction18  
[2023-03-26 13:24:38] local.INFO: array (
  0 => 'OK',
  1 => '8,634,341.83',
  2 => 'NONE',
  3 => '61172039',
  4 => '8,000.00',
)  
[2023-03-26 17:11:36] local.INFO: header  
[2023-03-26 17:11:36] local.INFO: header after fliter  
[2023-03-26 17:11:36] local.INFO: Body  after fliter  
[2023-03-26 17:11:36] local.INFO: array (
)  
[2023-03-26 17:11:36] local.INFO: transaction14  
[2023-03-26 17:11:36] local.INFO: first inquery phone = 101017939  
[2023-03-26 17:11:39] local.DEBUG: response querySubBalance  
[2023-03-26 17:11:39] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#8,000.00#8,000.00#21.42 GB#28-03-2023#0#0##3980.00#4G 60</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-26 17:11:39] local.DEBUG: array (
  0 => 'OK',
  1 => '8,000.00',
  2 => '8,000.00',
  3 => '21.42 GB',
  4 => '28-03-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '3980.00',
  9 => '4G 60',
)  
[2023-03-26 17:11:39] local.DEBUG: print  before faction by provider price  
[2023-03-26 17:11:39] local.DEBUG: print  after faction by provider price  
[2023-03-26 17:11:39] local.DEBUG: فئة 60 جيجا 8000 ريال  
[2023-03-26 17:11:39] local.DEBUG: print1  
[2023-03-26 17:11:39] local.DEBUG: print  2  
[2023-03-26 17:11:43] local.INFO: header  
[2023-03-26 17:11:43] local.CRITICAL: ****************************1  
[2023-03-26 17:11:43] local.ALERT: reach here  
[2023-03-26 17:11:43] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '4000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'أربعة ألف  ر.ي.',
  ),
)  
[2023-03-26 17:11:43] local.ERROR: المبلغ  
[2023-03-26 17:11:43] local.ERROR: 4,000.00  
[2023-03-26 17:11:43] local.ERROR: مبلغ وقدرة  
[2023-03-26 17:11:43] local.ALERT: 200  
[2023-03-26 17:11:43] local.ALERT: 3  
[2023-03-26 17:11:43] local.ALERT: 200  
[2023-03-26 17:11:43] local.ALERT: 3  
[2023-03-26 17:11:43] local.ALERT: 200  
[2023-03-26 17:11:43] local.ALERT: 3  
[2023-03-26 17:11:43] local.ALERT: 200  
[2023-03-26 17:11:43] local.ALERT: 3  
[2023-03-26 17:11:43] local.ALERT: 200  
[2023-03-26 17:11:43] local.ALERT: 3  
[2023-03-26 17:11:43] local.ALERT: 200  
[2023-03-26 17:11:43] local.ALERT: 3  
[2023-03-26 17:11:43] local.ALERT: 200  
[2023-03-26 17:11:43] local.ALERT: 3  
[2023-03-26 17:11:43] local.ALERT: 200  
[2023-03-26 17:11:43] local.ALERT: 3  
[2023-03-26 17:11:43] local.ALERT: 200  
[2023-03-26 17:11:43] local.ALERT: 3  
[2023-03-26 17:11:43] local.ALERT: 200  
[2023-03-26 17:11:43] local.ALERT: 2  
[2023-03-26 17:11:43] local.ALERT: 200  
[2023-03-26 17:11:43] local.ALERT: 2  
[2023-03-26 17:11:43] local.ALERT: 200  
[2023-03-26 17:11:43] local.ALERT: 2  
[2023-03-26 17:11:43] local.ALERT: 200  
[2023-03-26 17:11:43] local.ALERT: 2  
[2023-03-26 17:11:43] local.ALERT: 200  
[2023-03-26 17:11:43] local.ALERT: 2  
[2023-03-26 17:11:43] local.ALERT: 200  
[2023-03-26 17:11:43] local.ALERT: 2  
[2023-03-26 17:11:43] local.ALERT: 200  
[2023-03-26 17:11:43] local.ALERT: 2  
[2023-03-26 17:11:43] local.ALERT: 200  
[2023-03-26 17:11:43] local.ALERT: 1  
[2023-03-26 17:11:43] local.ALERT: 200  
[2023-03-26 17:11:43] local.ALERT: 1  
[2023-03-26 17:11:43] local.ALERT: 200  
[2023-03-26 17:11:43] local.ALERT: 1  
[2023-03-26 17:11:43] local.ALERT: 200  
[2023-03-26 17:11:43] local.ALERT: 1  
[2023-03-26 17:11:43] local.ALERT: 200  
[2023-03-26 17:11:43] local.ALERT: 1  
[2023-03-26 17:11:43] local.ALERT: 200  
[2023-03-26 17:11:43] local.ALERT: 2  
[2023-03-26 17:11:43] local.ALERT: 200  
[2023-03-26 17:11:43] local.ALERT: 10013  
[2023-03-26 17:11:43] local.ALERT: 200  
[2023-03-26 17:11:43] local.ALERT: 10013  
[2023-03-26 17:11:43] local.ALERT: 200  
[2023-03-26 17:11:43] local.ALERT: 10013  
[2023-03-26 17:11:43] local.ALERT: 200  
[2023-03-26 17:11:43] local.ALERT: 10013  
[2023-03-26 17:11:43] local.ALERT: 200  
[2023-03-26 17:11:43] local.ALERT: 1  
[2023-03-26 17:11:43] local.ALERT: 200  
[2023-03-26 17:11:43] local.ALERT: 3  
[2023-03-26 17:11:43] local.ALERT: 200  
[2023-03-26 17:11:43] local.ALERT: 3  
[2023-03-26 17:11:43] local.ALERT: 200  
[2023-03-26 17:11:43] local.ALERT: 40  
[2023-03-26 17:11:43] local.ALERT: 200  
[2023-03-26 17:11:43] local.ALERT: 40  
[2023-03-26 17:11:43] local.ALERT: 200  
[2023-03-26 17:11:43] local.ALERT: 40  
[2023-03-26 17:11:43] local.ALERT: 200  
[2023-03-26 17:11:43] local.ALERT: 40  
[2023-03-26 17:11:43] local.ALERT: 200  
[2023-03-26 17:11:43] local.ALERT: 200  
[2023-03-26 17:11:43] local.WARNING: 1  
[2023-03-26 17:11:43] local.WARNING: array (
  'ID' => 86,
  'Name' => 'فئة 25 جيجا 4000 ريال',
  'ServiceID' => 200,
  'Price' => 4000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 4000.0,
)  
[2023-03-26 17:11:43] local.ALERT: 200  
[2023-03-26 17:11:43] local.ALERT: 200  
[2023-03-26 17:11:43] local.ALERT: 200  
[2023-03-26 17:11:43] local.ALERT: 200  
[2023-03-26 17:11:43] local.ALERT: 200  
[2023-03-26 17:11:43] local.ALERT: 200  
[2023-03-26 17:11:43] local.ALERT: 200  
[2023-03-26 17:11:43] local.ALERT: 200  
[2023-03-26 17:11:43] local.ALERT: 200  
[2023-03-26 17:11:43] local.ALERT: 200  
[2023-03-26 17:11:43] local.ALERT: 200  
[2023-03-26 17:11:43] local.ALERT: 40  
[2023-03-26 17:11:43] local.CRITICAL: ****************************2  
[2023-03-26 17:11:43] local.CRITICAL: ****************************  
[2023-03-26 17:11:43] local.CRITICAL:   
[2023-03-26 17:11:43] local.CRITICAL: ****************************  
[2023-03-26 17:11:43] local.INFO: {
  "ClientBalanceResult": "8162.2150"
}  
[2023-03-26 17:11:43] local.INFO: array (
  'ClientBalanceResult' => '8162.2150',
)  
[2023-03-26 17:11:43] local.DEBUG: lattttef  
[2023-03-26 17:11:43] local.DEBUG: array (
  'ClientBalanceResult' => '8162.2150',
)  
[2023-03-26 17:11:43] local.INFO: transaction14  
[2023-03-26 17:11:43] local.INFO: first inquery phone = 101017939  
[2023-03-26 17:11:46] local.DEBUG: response querySubBalance  
[2023-03-26 17:11:46] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#8,000.00#8,000.00#21.42 GB#28-03-2023#0#0##3980.00#4G 60</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-26 17:11:46] local.DEBUG: array (
  0 => 'OK',
  1 => '8,000.00',
  2 => '8,000.00',
  3 => '21.42 GB',
  4 => '28-03-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '3980.00',
  9 => '4G 60',
)  
[2023-03-26 17:11:46] local.DEBUG: print  before faction by provider price  
[2023-03-26 17:11:46] local.DEBUG: print  after faction by provider price  
[2023-03-26 17:11:46] local.DEBUG: فئة 60 جيجا 8000 ريال  
[2023-03-26 17:11:46] local.DEBUG: print1  
[2023-03-26 17:11:46] local.DEBUG: print  2  
[2023-03-26 17:31:27] local.INFO: header  
[2023-03-26 17:31:27] local.CRITICAL: ****************************1  
[2023-03-26 17:31:27] local.ALERT: reach here  
[2023-03-26 17:31:27] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '4000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'أربعة ألف  ر.ي.',
  ),
)  
[2023-03-26 17:31:27] local.ERROR: المبلغ  
[2023-03-26 17:31:27] local.ERROR: 4,000.00  
[2023-03-26 17:31:27] local.ERROR: مبلغ وقدرة  
[2023-03-26 17:31:27] local.ALERT: 200  
[2023-03-26 17:31:27] local.ALERT: 3  
[2023-03-26 17:31:27] local.ALERT: 200  
[2023-03-26 17:31:27] local.ALERT: 3  
[2023-03-26 17:31:27] local.ALERT: 200  
[2023-03-26 17:31:27] local.ALERT: 3  
[2023-03-26 17:31:27] local.ALERT: 200  
[2023-03-26 17:31:27] local.ALERT: 3  
[2023-03-26 17:31:27] local.ALERT: 200  
[2023-03-26 17:31:27] local.ALERT: 3  
[2023-03-26 17:31:27] local.ALERT: 200  
[2023-03-26 17:31:27] local.ALERT: 3  
[2023-03-26 17:31:27] local.ALERT: 200  
[2023-03-26 17:31:27] local.ALERT: 3  
[2023-03-26 17:31:27] local.ALERT: 200  
[2023-03-26 17:31:27] local.ALERT: 3  
[2023-03-26 17:31:27] local.ALERT: 200  
[2023-03-26 17:31:27] local.ALERT: 3  
[2023-03-26 17:31:27] local.ALERT: 200  
[2023-03-26 17:31:27] local.ALERT: 2  
[2023-03-26 17:31:27] local.ALERT: 200  
[2023-03-26 17:31:27] local.ALERT: 2  
[2023-03-26 17:31:27] local.ALERT: 200  
[2023-03-26 17:31:27] local.ALERT: 2  
[2023-03-26 17:31:27] local.ALERT: 200  
[2023-03-26 17:31:27] local.ALERT: 2  
[2023-03-26 17:31:27] local.ALERT: 200  
[2023-03-26 17:31:27] local.ALERT: 2  
[2023-03-26 17:31:27] local.ALERT: 200  
[2023-03-26 17:31:27] local.ALERT: 2  
[2023-03-26 17:31:27] local.ALERT: 200  
[2023-03-26 17:31:27] local.ALERT: 2  
[2023-03-26 17:31:27] local.ALERT: 200  
[2023-03-26 17:31:27] local.ALERT: 1  
[2023-03-26 17:31:27] local.ALERT: 200  
[2023-03-26 17:31:27] local.ALERT: 1  
[2023-03-26 17:31:27] local.ALERT: 200  
[2023-03-26 17:31:27] local.ALERT: 1  
[2023-03-26 17:31:27] local.ALERT: 200  
[2023-03-26 17:31:27] local.ALERT: 1  
[2023-03-26 17:31:27] local.ALERT: 200  
[2023-03-26 17:31:27] local.ALERT: 1  
[2023-03-26 17:31:27] local.ALERT: 200  
[2023-03-26 17:31:27] local.ALERT: 2  
[2023-03-26 17:31:27] local.ALERT: 200  
[2023-03-26 17:31:27] local.ALERT: 10013  
[2023-03-26 17:31:27] local.ALERT: 200  
[2023-03-26 17:31:27] local.ALERT: 10013  
[2023-03-26 17:31:27] local.ALERT: 200  
[2023-03-26 17:31:27] local.ALERT: 10013  
[2023-03-26 17:31:27] local.ALERT: 200  
[2023-03-26 17:31:27] local.ALERT: 10013  
[2023-03-26 17:31:27] local.ALERT: 200  
[2023-03-26 17:31:27] local.ALERT: 1  
[2023-03-26 17:31:27] local.ALERT: 200  
[2023-03-26 17:31:27] local.ALERT: 3  
[2023-03-26 17:31:27] local.ALERT: 200  
[2023-03-26 17:31:27] local.ALERT: 3  
[2023-03-26 17:31:27] local.ALERT: 200  
[2023-03-26 17:31:27] local.ALERT: 40  
[2023-03-26 17:31:27] local.ALERT: 200  
[2023-03-26 17:31:27] local.ALERT: 40  
[2023-03-26 17:31:27] local.ALERT: 200  
[2023-03-26 17:31:27] local.ALERT: 40  
[2023-03-26 17:31:27] local.ALERT: 200  
[2023-03-26 17:31:27] local.ALERT: 40  
[2023-03-26 17:31:27] local.ALERT: 200  
[2023-03-26 17:31:27] local.ALERT: 200  
[2023-03-26 17:31:27] local.WARNING: 1  
[2023-03-26 17:31:27] local.WARNING: array (
  'ID' => 86,
  'Name' => 'فئة 25 جيجا 4000 ريال',
  'ServiceID' => 200,
  'Price' => 4000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 4000.0,
)  
[2023-03-26 17:31:27] local.ALERT: 200  
[2023-03-26 17:31:27] local.ALERT: 200  
[2023-03-26 17:31:27] local.ALERT: 200  
[2023-03-26 17:31:27] local.ALERT: 200  
[2023-03-26 17:31:27] local.ALERT: 200  
[2023-03-26 17:31:27] local.ALERT: 200  
[2023-03-26 17:31:27] local.ALERT: 200  
[2023-03-26 17:31:27] local.ALERT: 200  
[2023-03-26 17:31:27] local.ALERT: 200  
[2023-03-26 17:31:27] local.ALERT: 200  
[2023-03-26 17:31:27] local.ALERT: 200  
[2023-03-26 17:31:27] local.ALERT: 40  
[2023-03-26 17:31:27] local.CRITICAL: ****************************2  
[2023-03-26 17:31:27] local.CRITICAL: ****************************  
[2023-03-26 17:31:27] local.CRITICAL:   
[2023-03-26 17:31:27] local.CRITICAL: ****************************  
[2023-03-26 17:31:27] local.INFO: {
  "ClientBalanceResult": "297502.4300"
}  
[2023-03-26 17:31:27] local.INFO: array (
  'ClientBalanceResult' => '297502.4300',
)  
[2023-03-26 17:31:27] local.DEBUG: lattttef  
[2023-03-26 17:31:27] local.DEBUG: array (
  'ClientBalanceResult' => '297502.4300',
)  
[2023-03-26 17:31:27] local.INFO: transaction14  
[2023-03-26 17:31:27] local.INFO: first inquery phone = 101017939  
[2023-03-26 17:31:30] local.DEBUG: response querySubBalance  
[2023-03-26 17:31:30] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#8,000.00#8,000.00#21.42 GB#28-03-2023#0#0##3980.00#4G 60</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-26 17:31:30] local.DEBUG: array (
  0 => 'OK',
  1 => '8,000.00',
  2 => '8,000.00',
  3 => '21.42 GB',
  4 => '28-03-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '3980.00',
  9 => '4G 60',
)  
[2023-03-26 17:31:30] local.DEBUG: print  before faction by provider price  
[2023-03-26 17:31:30] local.DEBUG: print  after faction by provider price  
[2023-03-26 17:31:30] local.DEBUG: فئة 60 جيجا 8000 ريال  
[2023-03-26 17:31:30] local.DEBUG: print1  
[2023-03-26 17:31:30] local.DEBUG: print  2  
[2023-03-26 17:32:21] local.INFO: header  
[2023-03-26 17:32:21] local.INFO: header after fliter  
[2023-03-26 17:32:21] local.INFO: Body  after fliter  
[2023-03-26 17:32:21] local.INFO: array (
)  
[2023-03-26 17:32:21] local.INFO: transaction14  
[2023-03-26 17:32:21] local.INFO: first inquery phone = 103322059  
[2023-03-26 17:32:23] local.DEBUG: response querySubBalance  
[2023-03-26 17:32:23] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#102.05 GB#20-04-2023#0#0##7000.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-26 17:32:23] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '102.05 GB',
  4 => '20-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '7000.00',
  9 => '4G 250',
)  
[2023-03-26 17:32:23] local.DEBUG: print  before faction by provider price  
[2023-03-26 17:32:23] local.DEBUG: print  after faction by provider price  
[2023-03-26 17:32:23] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-26 17:32:23] local.DEBUG: print1  
[2023-03-26 17:32:23] local.DEBUG: print  2  
[2023-03-26 17:32:42] local.INFO: header  
[2023-03-26 17:32:42] local.INFO: header after fliter  
[2023-03-26 17:32:42] local.INFO: Body  after fliter  
[2023-03-26 17:32:42] local.INFO: array (
)  
[2023-03-26 17:32:42] local.INFO: transaction14  
[2023-03-26 17:32:42] local.INFO: first inquery phone = 103322169  
[2023-03-26 17:32:44] local.DEBUG: response querySubBalance  
[2023-03-26 17:32:44] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#113.38 GB#20-04-2023#0#0##7000.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-26 17:32:44] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '113.38 GB',
  4 => '20-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '7000.00',
  9 => '4G 250',
)  
[2023-03-26 17:32:44] local.DEBUG: print  before faction by provider price  
[2023-03-26 17:32:44] local.DEBUG: print  after faction by provider price  
[2023-03-26 17:32:44] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-26 17:32:44] local.DEBUG: print1  
[2023-03-26 17:32:44] local.DEBUG: print  2  
[2023-03-26 21:15:55] local.INFO: header  
[2023-03-26 21:15:55] local.INFO: header after fliter  
[2023-03-26 21:15:55] local.INFO: Body  after fliter  
[2023-03-26 21:15:55] local.INFO: array (
)  
[2023-03-26 21:15:55] local.INFO: transaction14  
[2023-03-26 21:15:55] local.INFO: first inquery phone = 103322059  
[2023-03-26 21:15:57] local.DEBUG: response querySubBalance  
[2023-03-26 21:15:57] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#98.58 GB#20-04-2023#0#0##7000.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-26 21:15:57] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '98.58 GB',
  4 => '20-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '7000.00',
  9 => '4G 250',
)  
[2023-03-26 21:15:57] local.DEBUG: print  before faction by provider price  
[2023-03-26 21:15:57] local.DEBUG: print  after faction by provider price  
[2023-03-26 21:15:57] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-26 21:15:57] local.DEBUG: print1  
[2023-03-26 21:15:57] local.DEBUG: print  2  
[2023-03-26 21:16:31] local.INFO: header  
[2023-03-26 21:16:31] local.INFO: header after fliter  
[2023-03-26 21:16:31] local.INFO: Body  after fliter  
[2023-03-26 21:16:31] local.INFO: array (
)  
[2023-03-26 21:16:31] local.INFO: transaction14  
[2023-03-26 21:16:31] local.INFO: first inquery phone = 103322169  
[2023-03-26 21:16:34] local.DEBUG: response querySubBalance  
[2023-03-26 21:16:34] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#110.01 GB#20-04-2023#0#0##7000.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-26 21:16:34] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '110.01 GB',
  4 => '20-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '7000.00',
  9 => '4G 250',
)  
[2023-03-26 21:16:34] local.DEBUG: print  before faction by provider price  
[2023-03-26 21:16:34] local.DEBUG: print  after faction by provider price  
[2023-03-26 21:16:34] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-26 21:16:34] local.DEBUG: print1  
[2023-03-26 21:16:34] local.DEBUG: print  2  
[2023-03-26 21:16:42] local.INFO: header  
[2023-03-26 21:16:42] local.INFO: header after fliter  
[2023-03-26 21:16:42] local.INFO: Body  after fliter  
[2023-03-26 21:16:42] local.INFO: array (
)  
[2023-03-26 21:16:42] local.INFO: transaction14  
[2023-03-26 21:16:42] local.INFO: first inquery phone = 103322192  
[2023-03-26 21:16:44] local.DEBUG: response querySubBalance  
[2023-03-26 21:16:44] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#46,000.00#46,000.00#287.07 GB#18-04-2023#0#0##5000.00#4G 500</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-26 21:16:44] local.DEBUG: array (
  0 => 'OK',
  1 => '46,000.00',
  2 => '46,000.00',
  3 => '287.07 GB',
  4 => '18-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '5000.00',
  9 => '4G 500',
)  
[2023-03-26 21:16:44] local.DEBUG: print  before faction by provider price  
[2023-03-26 21:16:44] local.DEBUG: print  after faction by provider price  
[2023-03-26 21:16:44] local.DEBUG: فئة 500 جيجا 46000 ريال  
[2023-03-26 21:16:44] local.DEBUG: print1  
[2023-03-26 21:16:44] local.DEBUG: print  2  
[2023-03-26 21:57:20] local.INFO: header  
[2023-03-26 21:57:20] local.CRITICAL: ****************************1  
[2023-03-26 21:57:20] local.ALERT: reach here  
[2023-03-26 21:57:21] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '4000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'أربعة ألف  ر.ي.',
  ),
)  
[2023-03-26 21:57:21] local.ERROR: المبلغ  
[2023-03-26 21:57:21] local.ERROR: 4,000.00  
[2023-03-26 21:57:21] local.ERROR: مبلغ وقدرة  
[2023-03-26 21:57:21] local.ALERT: 200  
[2023-03-26 21:57:21] local.ALERT: 3  
[2023-03-26 21:57:21] local.ALERT: 200  
[2023-03-26 21:57:21] local.ALERT: 3  
[2023-03-26 21:57:21] local.ALERT: 200  
[2023-03-26 21:57:21] local.ALERT: 3  
[2023-03-26 21:57:21] local.ALERT: 200  
[2023-03-26 21:57:21] local.ALERT: 3  
[2023-03-26 21:57:21] local.ALERT: 200  
[2023-03-26 21:57:21] local.ALERT: 3  
[2023-03-26 21:57:21] local.ALERT: 200  
[2023-03-26 21:57:21] local.ALERT: 3  
[2023-03-26 21:57:21] local.ALERT: 200  
[2023-03-26 21:57:21] local.ALERT: 3  
[2023-03-26 21:57:21] local.ALERT: 200  
[2023-03-26 21:57:21] local.ALERT: 3  
[2023-03-26 21:57:21] local.ALERT: 200  
[2023-03-26 21:57:21] local.ALERT: 3  
[2023-03-26 21:57:21] local.ALERT: 200  
[2023-03-26 21:57:21] local.ALERT: 2  
[2023-03-26 21:57:21] local.ALERT: 200  
[2023-03-26 21:57:21] local.ALERT: 2  
[2023-03-26 21:57:21] local.ALERT: 200  
[2023-03-26 21:57:21] local.ALERT: 2  
[2023-03-26 21:57:21] local.ALERT: 200  
[2023-03-26 21:57:21] local.ALERT: 2  
[2023-03-26 21:57:21] local.ALERT: 200  
[2023-03-26 21:57:21] local.ALERT: 2  
[2023-03-26 21:57:21] local.ALERT: 200  
[2023-03-26 21:57:21] local.ALERT: 2  
[2023-03-26 21:57:21] local.ALERT: 200  
[2023-03-26 21:57:21] local.ALERT: 2  
[2023-03-26 21:57:21] local.ALERT: 200  
[2023-03-26 21:57:21] local.ALERT: 1  
[2023-03-26 21:57:21] local.ALERT: 200  
[2023-03-26 21:57:21] local.ALERT: 1  
[2023-03-26 21:57:21] local.ALERT: 200  
[2023-03-26 21:57:21] local.ALERT: 1  
[2023-03-26 21:57:21] local.ALERT: 200  
[2023-03-26 21:57:21] local.ALERT: 1  
[2023-03-26 21:57:21] local.ALERT: 200  
[2023-03-26 21:57:21] local.ALERT: 1  
[2023-03-26 21:57:21] local.ALERT: 200  
[2023-03-26 21:57:21] local.ALERT: 2  
[2023-03-26 21:57:21] local.ALERT: 200  
[2023-03-26 21:57:21] local.ALERT: 10013  
[2023-03-26 21:57:21] local.ALERT: 200  
[2023-03-26 21:57:21] local.ALERT: 10013  
[2023-03-26 21:57:21] local.ALERT: 200  
[2023-03-26 21:57:21] local.ALERT: 10013  
[2023-03-26 21:57:21] local.ALERT: 200  
[2023-03-26 21:57:21] local.ALERT: 10013  
[2023-03-26 21:57:21] local.ALERT: 200  
[2023-03-26 21:57:21] local.ALERT: 1  
[2023-03-26 21:57:21] local.ALERT: 200  
[2023-03-26 21:57:21] local.ALERT: 3  
[2023-03-26 21:57:21] local.ALERT: 200  
[2023-03-26 21:57:21] local.ALERT: 3  
[2023-03-26 21:57:21] local.ALERT: 200  
[2023-03-26 21:57:21] local.ALERT: 40  
[2023-03-26 21:57:21] local.ALERT: 200  
[2023-03-26 21:57:21] local.ALERT: 40  
[2023-03-26 21:57:21] local.ALERT: 200  
[2023-03-26 21:57:21] local.ALERT: 40  
[2023-03-26 21:57:21] local.ALERT: 200  
[2023-03-26 21:57:21] local.ALERT: 40  
[2023-03-26 21:57:21] local.ALERT: 200  
[2023-03-26 21:57:21] local.ALERT: 200  
[2023-03-26 21:57:21] local.WARNING: 1  
[2023-03-26 21:57:21] local.WARNING: array (
  'ID' => 86,
  'Name' => 'فئة 25 جيجا 4000 ريال',
  'ServiceID' => 200,
  'Price' => 4000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 4000.0,
)  
[2023-03-26 21:57:21] local.ALERT: 200  
[2023-03-26 21:57:21] local.ALERT: 200  
[2023-03-26 21:57:21] local.ALERT: 200  
[2023-03-26 21:57:21] local.ALERT: 200  
[2023-03-26 21:57:21] local.ALERT: 200  
[2023-03-26 21:57:21] local.ALERT: 200  
[2023-03-26 21:57:21] local.ALERT: 200  
[2023-03-26 21:57:21] local.ALERT: 200  
[2023-03-26 21:57:21] local.ALERT: 200  
[2023-03-26 21:57:21] local.ALERT: 200  
[2023-03-26 21:57:21] local.ALERT: 200  
[2023-03-26 21:57:21] local.ALERT: 40  
[2023-03-26 21:57:21] local.CRITICAL: ****************************2  
[2023-03-26 21:57:21] local.CRITICAL: ****************************  
[2023-03-26 21:57:21] local.CRITICAL:   
[2023-03-26 21:57:21] local.CRITICAL: ****************************  
[2023-03-26 21:57:21] local.INFO: {
  "ClientBalanceResult": "22264.9300"
}  
[2023-03-26 21:57:21] local.INFO: array (
  'ClientBalanceResult' => '22264.9300',
)  
[2023-03-26 21:57:21] local.DEBUG: lattttef  
[2023-03-26 21:57:21] local.DEBUG: array (
  'ClientBalanceResult' => '22264.9300',
)  
[2023-03-26 21:57:21] local.INFO: transaction14  
[2023-03-26 21:57:21] local.INFO: first inquery phone = *********  
[2023-03-26 21:57:25] local.DEBUG: response querySubBalance  
[2023-03-26 21:57:25] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#0.00 B#21-04-2023#0#0##2500.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-26 21:57:25] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '0.00 B',
  4 => '21-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '2500.00',
  9 => '4G 25',
)  
[2023-03-26 21:57:25] local.DEBUG: print  before faction by provider price  
[2023-03-26 21:57:25] local.DEBUG: print  after faction by provider price  
[2023-03-26 21:57:25] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-26 21:57:25] local.DEBUG: print1  
[2023-03-26 21:57:25] local.DEBUG: print  2  
[2023-03-26 21:57:25] local.INFO: transaction1  
[2023-03-26 21:57:25] local.INFO: transaction2  
[2023-03-26 21:57:25] local.INFO: transaction3  
[2023-03-26 21:57:25] local.INFO: transaction4  
[2023-03-26 21:57:25] local.INFO: transaction4  
[2023-03-26 21:57:25] local.INFO: transaction5  
[2023-03-26 21:57:25] local.INFO: transaction6  
[2023-03-26 21:57:25] local.INFO: transaction7  
[2023-03-26 21:57:25] local.DEBUG: array (
  'AMT' => 4000.0,
  'CType' => 0,
  'FID' => 86,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '*********',
  'State' => 0,
  'lateflog' => '588030',
)  
[2023-03-26 21:57:25] local.INFO: transaction8  
[2023-03-26 21:57:25] local.INFO: transaction9  
[2023-03-26 21:57:25] local.INFO: transaction10  
[2023-03-26 21:57:25] local.INFO: transaction11  
[2023-03-26 21:57:25] local.INFO: 12  
[2023-03-26 21:57:25] local.INFO: transaction13  
[2023-03-26 21:57:25] local.INFO: transaction14  
[2023-03-26 21:57:25] local.INFO: transaction19  
[2023-03-26 21:57:25] local.INFO: transaction15  
[2023-03-26 21:57:25] local.INFO: transaction16  
[2023-03-26 21:57:25] local.INFO: 98#*********#4000.00#0  
[2023-03-26 21:57:35] local.DEBUG: {"Number":********,"ServiceID":200,"NetworkID":null,"SubscriberNumber":"*********","Amount":4000,"FactionID":86,"RegionID":null,"LineType":"\u063a\u064a\u0631 \u0645\u0639\u0631\u0648\u0641","Date":"2023-03-26 21:57:25","Status":2,"Note":null,"CreditorAccountID":379015,"CurrencyID":1,"DebitorAccountID":"588030","AgentID":null,"RefNumber":null,"TransactionID":********,"ProviderID":10028,"EntryID":4404230,"PaymentEntryID":null,"Channel":2,"CreatedBy":"584329","BranchBy":null,"CreatedTime":"2023-03-26 21:57:25","BranchID":"1","ProviderRM":"","ProviderPrice":"4000.00","SubNote":null,"Datestamb":"********","UniqueNo":"********215725","Quantity":"4000.0000","UnitPrice":1,"UnitCost":1,"CostAmount":"4000.00","DifferentialAmount":0,"CommissionAmount":0,"Discount":0,"TotalCost":4000,"TotalAmount":4000,"Profits":0,"Method":2,"Type":0,"Class":0,"LType":"1","OperatorID":1,"AppTechApi":0,"BillNumber":"20000********","BillState":0,"Debited":1,"ByChild":0,"IsDirect":1,"BundleName":"\u0641\u0626\u0629 25 \u062c\u064a\u062c\u0627 4000 \u0631\u064a\u0627\u0644","BundleCode":"30","ExCode":null,"TransNumber":"********215725","OperationID":0,"AccountID":"588030","State":0,"StateClass":"","Identifier":"34dffd789979fe43","AdminNote":"","AccountNote":"","Description":null,"Responded":0,"RequestInfo":"200#*********#4000#86","ResponseTime":"2023-03-26 21:57:25","ResponseStatus":0,"ExecutionPeroid":"25","FaildRequest":0,"FailedReason":null,"FailedType":0,"Cured":0,"CuredBy":null,"CuredInfo":null,"InspectInfo":null,"Flag":2,"Action":0,"QuotaionID":0,"SyncID":0,"id":4227295}  
[2023-03-26 21:58:18] local.INFO: header  
[2023-03-26 21:58:18] local.CRITICAL: ****************************1  
[2023-03-26 21:58:18] local.ALERT: reach here  
[2023-03-26 21:58:18] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '4000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'أربعة ألف  ر.ي.',
  ),
)  
[2023-03-26 21:58:18] local.ERROR: المبلغ  
[2023-03-26 21:58:18] local.ERROR: 4,000.00  
[2023-03-26 21:58:18] local.ERROR: مبلغ وقدرة  
[2023-03-26 21:58:18] local.ALERT: 200  
[2023-03-26 21:58:18] local.ALERT: 3  
[2023-03-26 21:58:18] local.ALERT: 200  
[2023-03-26 21:58:18] local.ALERT: 3  
[2023-03-26 21:58:18] local.ALERT: 200  
[2023-03-26 21:58:18] local.ALERT: 3  
[2023-03-26 21:58:18] local.ALERT: 200  
[2023-03-26 21:58:18] local.ALERT: 3  
[2023-03-26 21:58:18] local.ALERT: 200  
[2023-03-26 21:58:18] local.ALERT: 3  
[2023-03-26 21:58:18] local.ALERT: 200  
[2023-03-26 21:58:18] local.ALERT: 3  
[2023-03-26 21:58:18] local.ALERT: 200  
[2023-03-26 21:58:18] local.ALERT: 3  
[2023-03-26 21:58:18] local.ALERT: 200  
[2023-03-26 21:58:18] local.ALERT: 3  
[2023-03-26 21:58:18] local.ALERT: 200  
[2023-03-26 21:58:18] local.ALERT: 3  
[2023-03-26 21:58:18] local.ALERT: 200  
[2023-03-26 21:58:18] local.ALERT: 2  
[2023-03-26 21:58:18] local.ALERT: 200  
[2023-03-26 21:58:18] local.ALERT: 2  
[2023-03-26 21:58:18] local.ALERT: 200  
[2023-03-26 21:58:18] local.ALERT: 2  
[2023-03-26 21:58:18] local.ALERT: 200  
[2023-03-26 21:58:18] local.ALERT: 2  
[2023-03-26 21:58:18] local.ALERT: 200  
[2023-03-26 21:58:18] local.ALERT: 2  
[2023-03-26 21:58:18] local.ALERT: 200  
[2023-03-26 21:58:18] local.ALERT: 2  
[2023-03-26 21:58:18] local.ALERT: 200  
[2023-03-26 21:58:18] local.ALERT: 2  
[2023-03-26 21:58:18] local.ALERT: 200  
[2023-03-26 21:58:18] local.ALERT: 1  
[2023-03-26 21:58:18] local.ALERT: 200  
[2023-03-26 21:58:18] local.ALERT: 1  
[2023-03-26 21:58:18] local.ALERT: 200  
[2023-03-26 21:58:18] local.ALERT: 1  
[2023-03-26 21:58:18] local.ALERT: 200  
[2023-03-26 21:58:18] local.ALERT: 1  
[2023-03-26 21:58:18] local.ALERT: 200  
[2023-03-26 21:58:18] local.ALERT: 1  
[2023-03-26 21:58:18] local.ALERT: 200  
[2023-03-26 21:58:18] local.ALERT: 2  
[2023-03-26 21:58:18] local.ALERT: 200  
[2023-03-26 21:58:18] local.ALERT: 10013  
[2023-03-26 21:58:18] local.ALERT: 200  
[2023-03-26 21:58:18] local.ALERT: 10013  
[2023-03-26 21:58:18] local.ALERT: 200  
[2023-03-26 21:58:18] local.ALERT: 10013  
[2023-03-26 21:58:18] local.ALERT: 200  
[2023-03-26 21:58:18] local.ALERT: 10013  
[2023-03-26 21:58:18] local.ALERT: 200  
[2023-03-26 21:58:18] local.ALERT: 1  
[2023-03-26 21:58:18] local.ALERT: 200  
[2023-03-26 21:58:18] local.ALERT: 3  
[2023-03-26 21:58:18] local.ALERT: 200  
[2023-03-26 21:58:18] local.ALERT: 3  
[2023-03-26 21:58:18] local.ALERT: 200  
[2023-03-26 21:58:18] local.ALERT: 40  
[2023-03-26 21:58:18] local.ALERT: 200  
[2023-03-26 21:58:18] local.ALERT: 40  
[2023-03-26 21:58:18] local.ALERT: 200  
[2023-03-26 21:58:18] local.ALERT: 40  
[2023-03-26 21:58:18] local.ALERT: 200  
[2023-03-26 21:58:18] local.ALERT: 40  
[2023-03-26 21:58:18] local.ALERT: 200  
[2023-03-26 21:58:18] local.ALERT: 200  
[2023-03-26 21:58:18] local.WARNING: 1  
[2023-03-26 21:58:18] local.WARNING: array (
  'ID' => 86,
  'Name' => 'فئة 25 جيجا 4000 ريال',
  'ServiceID' => 200,
  'Price' => 4000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 4000.0,
)  
[2023-03-26 21:58:18] local.ALERT: 200  
[2023-03-26 21:58:18] local.ALERT: 200  
[2023-03-26 21:58:18] local.ALERT: 200  
[2023-03-26 21:58:18] local.ALERT: 200  
[2023-03-26 21:58:18] local.ALERT: 200  
[2023-03-26 21:58:18] local.ALERT: 200  
[2023-03-26 21:58:18] local.ALERT: 200  
[2023-03-26 21:58:18] local.ALERT: 200  
[2023-03-26 21:58:18] local.ALERT: 200  
[2023-03-26 21:58:18] local.ALERT: 200  
[2023-03-26 21:58:18] local.ALERT: 200  
[2023-03-26 21:58:18] local.ALERT: 40  
[2023-03-26 21:58:18] local.CRITICAL: ****************************2  
[2023-03-26 21:58:18] local.CRITICAL: ****************************  
[2023-03-26 21:58:18] local.CRITICAL:   
[2023-03-26 21:58:18] local.CRITICAL: ****************************  
[2023-03-26 21:58:18] local.INFO: {
  "ClientBalanceResult": "22264.9300"
}  
[2023-03-26 21:58:18] local.INFO: array (
  'ClientBalanceResult' => '22264.9300',
)  
[2023-03-26 21:58:18] local.DEBUG: lattttef  
[2023-03-26 21:58:18] local.DEBUG: array (
  'ClientBalanceResult' => '22264.9300',
)  
[2023-03-26 21:58:18] local.INFO: transaction14  
[2023-03-26 21:58:18] local.INFO: first inquery phone = *********  
[2023-03-26 21:58:20] local.DEBUG: response querySubBalance  
[2023-03-26 21:58:20] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#0.00 B#21-04-2023#0#0##2500.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-26 21:58:20] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '0.00 B',
  4 => '21-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '2500.00',
  9 => '4G 25',
)  
[2023-03-26 21:58:20] local.DEBUG: print  before faction by provider price  
[2023-03-26 21:58:20] local.DEBUG: print  after faction by provider price  
[2023-03-26 21:58:20] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-26 21:58:20] local.DEBUG: print1  
[2023-03-26 21:58:20] local.DEBUG: print  2  
[2023-03-26 21:58:20] local.INFO: transaction1  
[2023-03-26 21:58:20] local.INFO: transaction2  
[2023-03-26 21:58:20] local.INFO: transaction3  
[2023-03-26 21:58:20] local.INFO: transaction4  
[2023-03-26 21:58:20] local.INFO: transaction4  
[2023-03-26 21:58:20] local.INFO: transaction5  
[2023-03-26 21:58:20] local.INFO: transaction6  
[2023-03-26 21:58:20] local.INFO: transaction7  
[2023-03-26 21:58:20] local.DEBUG: array (
  'AMT' => 4000.0,
  'CType' => 0,
  'FID' => 86,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '*********',
  'State' => 0,
  'lateflog' => '588030',
)  
[2023-03-26 21:58:20] local.INFO: transaction8  
[2023-03-26 21:58:21] local.INFO: transaction9  
[2023-03-26 21:58:21] local.INFO: transaction10  
[2023-03-26 21:58:21] local.INFO: transaction11  
[2023-03-26 21:58:21] local.INFO: 12  
[2023-03-26 22:06:44] local.INFO: header  
[2023-03-26 22:06:44] local.INFO: header after fliter  
[2023-03-26 22:06:44] local.INFO: Body  after fliter  
[2023-03-26 22:06:44] local.INFO: array (
)  
[2023-03-26 22:06:44] local.INFO: transaction14  
[2023-03-26 22:06:44] local.INFO: first inquery phone = 106400449  
[2023-03-26 22:06:47] local.DEBUG: response querySubBalance  
[2023-03-26 22:06:47] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00##26-03-2023#0#0##.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-26 22:06:47] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '',
  4 => '26-03-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 25',
)  
[2023-03-26 22:06:47] local.DEBUG: print  before faction by provider price  
[2023-03-26 22:06:47] local.DEBUG: print  after faction by provider price  
[2023-03-26 22:06:47] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-26 22:06:47] local.DEBUG: print1  
[2023-03-26 22:06:47] local.DEBUG: print  2  
[2023-03-26 22:23:09] local.INFO: header  
[2023-03-26 22:23:09] local.INFO: header after fliter  
[2023-03-26 22:23:09] local.INFO: Body  after fliter  
[2023-03-26 22:23:09] local.INFO: array (
)  
[2023-03-26 22:23:09] local.INFO: transaction14  
[2023-03-26 22:23:09] local.INFO: first inquery phone = 103394759  
[2023-03-26 22:23:11] local.DEBUG: response querySubBalance  
[2023-03-26 22:23:11] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#22.71 GB#27-03-2023#0#0##1500.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-26 22:23:11] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '22.71 GB',
  4 => '27-03-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '1500.00',
  9 => '4G 25',
)  
[2023-03-26 22:23:11] local.DEBUG: print  before faction by provider price  
[2023-03-26 22:23:11] local.DEBUG: print  after faction by provider price  
[2023-03-26 22:23:11] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-26 22:23:11] local.DEBUG: print1  
[2023-03-26 22:23:11] local.DEBUG: print  2  
[2023-03-26 22:27:57] local.INFO: header  
[2023-03-26 22:27:57] local.CRITICAL: ****************************1  
[2023-03-26 22:27:57] local.ALERT: reach here  
[2023-03-26 22:27:57] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '4000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'أربعة ألف  ر.ي.',
  ),
)  
[2023-03-26 22:27:57] local.ERROR: المبلغ  
[2023-03-26 22:27:57] local.ERROR: 4,000.00  
[2023-03-26 22:27:57] local.ERROR: مبلغ وقدرة  
[2023-03-26 22:27:57] local.ALERT: 200  
[2023-03-26 22:27:57] local.ALERT: 3  
[2023-03-26 22:27:57] local.ALERT: 200  
[2023-03-26 22:27:57] local.ALERT: 3  
[2023-03-26 22:27:57] local.ALERT: 200  
[2023-03-26 22:27:57] local.ALERT: 3  
[2023-03-26 22:27:57] local.ALERT: 200  
[2023-03-26 22:27:57] local.ALERT: 3  
[2023-03-26 22:27:57] local.ALERT: 200  
[2023-03-26 22:27:57] local.ALERT: 3  
[2023-03-26 22:27:57] local.ALERT: 200  
[2023-03-26 22:27:57] local.ALERT: 3  
[2023-03-26 22:27:57] local.ALERT: 200  
[2023-03-26 22:27:57] local.ALERT: 3  
[2023-03-26 22:27:57] local.ALERT: 200  
[2023-03-26 22:27:57] local.ALERT: 3  
[2023-03-26 22:27:57] local.ALERT: 200  
[2023-03-26 22:27:57] local.ALERT: 3  
[2023-03-26 22:27:57] local.ALERT: 200  
[2023-03-26 22:27:57] local.ALERT: 2  
[2023-03-26 22:27:57] local.ALERT: 200  
[2023-03-26 22:27:57] local.ALERT: 2  
[2023-03-26 22:27:57] local.ALERT: 200  
[2023-03-26 22:27:57] local.ALERT: 2  
[2023-03-26 22:27:57] local.ALERT: 200  
[2023-03-26 22:27:57] local.ALERT: 2  
[2023-03-26 22:27:57] local.ALERT: 200  
[2023-03-26 22:27:57] local.ALERT: 2  
[2023-03-26 22:27:57] local.ALERT: 200  
[2023-03-26 22:27:57] local.ALERT: 2  
[2023-03-26 22:27:57] local.ALERT: 200  
[2023-03-26 22:27:57] local.ALERT: 2  
[2023-03-26 22:27:57] local.ALERT: 200  
[2023-03-26 22:27:57] local.ALERT: 1  
[2023-03-26 22:27:57] local.ALERT: 200  
[2023-03-26 22:27:57] local.ALERT: 1  
[2023-03-26 22:27:57] local.ALERT: 200  
[2023-03-26 22:27:57] local.ALERT: 1  
[2023-03-26 22:27:57] local.ALERT: 200  
[2023-03-26 22:27:57] local.ALERT: 1  
[2023-03-26 22:27:57] local.ALERT: 200  
[2023-03-26 22:27:57] local.ALERT: 1  
[2023-03-26 22:27:57] local.ALERT: 200  
[2023-03-26 22:27:57] local.ALERT: 2  
[2023-03-26 22:27:57] local.ALERT: 200  
[2023-03-26 22:27:57] local.ALERT: 10013  
[2023-03-26 22:27:57] local.ALERT: 200  
[2023-03-26 22:27:57] local.ALERT: 10013  
[2023-03-26 22:27:57] local.ALERT: 200  
[2023-03-26 22:27:57] local.ALERT: 10013  
[2023-03-26 22:27:57] local.ALERT: 200  
[2023-03-26 22:27:57] local.ALERT: 10013  
[2023-03-26 22:27:57] local.ALERT: 200  
[2023-03-26 22:27:57] local.ALERT: 1  
[2023-03-26 22:27:57] local.ALERT: 200  
[2023-03-26 22:27:57] local.ALERT: 3  
[2023-03-26 22:27:57] local.ALERT: 200  
[2023-03-26 22:27:57] local.ALERT: 3  
[2023-03-26 22:27:57] local.ALERT: 200  
[2023-03-26 22:27:57] local.ALERT: 40  
[2023-03-26 22:27:57] local.ALERT: 200  
[2023-03-26 22:27:57] local.ALERT: 40  
[2023-03-26 22:27:57] local.ALERT: 200  
[2023-03-26 22:27:57] local.ALERT: 40  
[2023-03-26 22:27:57] local.ALERT: 200  
[2023-03-26 22:27:57] local.ALERT: 40  
[2023-03-26 22:27:57] local.ALERT: 200  
[2023-03-26 22:27:57] local.ALERT: 200  
[2023-03-26 22:27:57] local.WARNING: 1  
[2023-03-26 22:27:57] local.WARNING: array (
  'ID' => 86,
  'Name' => 'فئة 25 جيجا 4000 ريال',
  'ServiceID' => 200,
  'Price' => 4000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 4000.0,
)  
[2023-03-26 22:27:57] local.ALERT: 200  
[2023-03-26 22:27:57] local.ALERT: 200  
[2023-03-26 22:27:57] local.ALERT: 200  
[2023-03-26 22:27:57] local.ALERT: 200  
[2023-03-26 22:27:57] local.ALERT: 200  
[2023-03-26 22:27:57] local.ALERT: 200  
[2023-03-26 22:27:57] local.ALERT: 200  
[2023-03-26 22:27:57] local.ALERT: 200  
[2023-03-26 22:27:57] local.ALERT: 200  
[2023-03-26 22:27:57] local.ALERT: 200  
[2023-03-26 22:27:57] local.ALERT: 200  
[2023-03-26 22:27:57] local.ALERT: 40  
[2023-03-26 22:27:57] local.CRITICAL: ****************************2  
[2023-03-26 22:27:57] local.CRITICAL: ****************************  
[2023-03-26 22:27:57] local.CRITICAL:   
[2023-03-26 22:27:57] local.CRITICAL: ****************************  
[2023-03-26 22:27:57] local.INFO: {
  "ClientBalanceResult": "8812.9700"
}  
[2023-03-26 22:27:57] local.INFO: array (
  'ClientBalanceResult' => '8812.9700',
)  
[2023-03-26 22:27:57] local.DEBUG: lattttef  
[2023-03-26 22:27:57] local.DEBUG: array (
  'ClientBalanceResult' => '8812.9700',
)  
[2023-03-26 22:27:57] local.INFO: transaction14  
[2023-03-26 22:27:57] local.INFO: first inquery phone = 103394759  
[2023-03-26 22:28:01] local.DEBUG: response querySubBalance  
[2023-03-26 22:28:01] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#22.71 GB#27-03-2023#0#0##1500.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-26 22:28:01] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '22.71 GB',
  4 => '27-03-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '1500.00',
  9 => '4G 25',
)  
[2023-03-26 22:28:01] local.DEBUG: print  before faction by provider price  
[2023-03-26 22:28:01] local.DEBUG: print  after faction by provider price  
[2023-03-26 22:28:01] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-26 22:28:01] local.DEBUG: print1  
[2023-03-26 22:28:01] local.DEBUG: print  2  
[2023-03-26 22:28:01] local.INFO: transaction1  
[2023-03-26 22:28:01] local.INFO: transaction2  
[2023-03-26 22:28:01] local.INFO: transaction3  
[2023-03-26 22:28:01] local.INFO: transaction4  
[2023-03-26 22:28:01] local.INFO: transaction4  
[2023-03-26 22:28:01] local.INFO: transaction5  
[2023-03-26 22:28:01] local.INFO: transaction6  
[2023-03-26 22:28:01] local.INFO: transaction7  
[2023-03-26 22:28:01] local.DEBUG: array (
  'AMT' => 4000.0,
  'CType' => 0,
  'FID' => 86,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '103394759',
  'State' => 0,
  'lateflog' => '572702',
)  
[2023-03-26 22:28:01] local.INFO: transaction8  
[2023-03-26 22:28:01] local.INFO: transaction9  
[2023-03-26 22:28:01] local.INFO: transaction10  
[2023-03-26 22:28:01] local.INFO: transaction11  
[2023-03-26 22:28:01] local.INFO: 12  
[2023-03-26 22:28:01] local.INFO: transaction13  
[2023-03-26 22:28:01] local.INFO: transaction14  
[2023-03-26 22:28:01] local.INFO: transaction19  
[2023-03-26 22:28:01] local.INFO: transaction15  
[2023-03-26 22:28:01] local.INFO: transaction16  
[2023-03-26 22:28:01] local.INFO: 98#103394759#4000.00#0  
[2023-03-26 22:28:11] local.INFO: transaction18  
[2023-03-26 22:28:11] local.INFO: array (
  0 => 'OK',
  1 => '7,664,372.83',
  2 => 'NONE',
  3 => '61233785',
  4 => '4,000.00',
)  
[2023-03-26 23:34:42] local.INFO: header  
[2023-03-26 23:34:42] local.CRITICAL: ****************************1  
[2023-03-26 23:34:42] local.ALERT: reach here  
[2023-03-26 23:34:43] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '4000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'أربعة ألف  ر.ي.',
  ),
)  
[2023-03-26 23:34:43] local.ERROR: المبلغ  
[2023-03-26 23:34:43] local.ERROR: 4,000.00  
[2023-03-26 23:34:43] local.ERROR: مبلغ وقدرة  
[2023-03-26 23:34:43] local.ALERT: 200  
[2023-03-26 23:34:43] local.ALERT: 3  
[2023-03-26 23:34:43] local.ALERT: 200  
[2023-03-26 23:34:43] local.ALERT: 3  
[2023-03-26 23:34:43] local.ALERT: 200  
[2023-03-26 23:34:43] local.ALERT: 3  
[2023-03-26 23:34:43] local.ALERT: 200  
[2023-03-26 23:34:43] local.ALERT: 3  
[2023-03-26 23:34:43] local.ALERT: 200  
[2023-03-26 23:34:43] local.ALERT: 3  
[2023-03-26 23:34:43] local.ALERT: 200  
[2023-03-26 23:34:43] local.ALERT: 3  
[2023-03-26 23:34:43] local.ALERT: 200  
[2023-03-26 23:34:43] local.ALERT: 3  
[2023-03-26 23:34:43] local.ALERT: 200  
[2023-03-26 23:34:43] local.ALERT: 3  
[2023-03-26 23:34:43] local.ALERT: 200  
[2023-03-26 23:34:43] local.ALERT: 3  
[2023-03-26 23:34:43] local.ALERT: 200  
[2023-03-26 23:34:43] local.ALERT: 2  
[2023-03-26 23:34:43] local.ALERT: 200  
[2023-03-26 23:34:43] local.ALERT: 2  
[2023-03-26 23:34:43] local.ALERT: 200  
[2023-03-26 23:34:43] local.ALERT: 2  
[2023-03-26 23:34:43] local.ALERT: 200  
[2023-03-26 23:34:43] local.ALERT: 2  
[2023-03-26 23:34:43] local.ALERT: 200  
[2023-03-26 23:34:43] local.ALERT: 2  
[2023-03-26 23:34:43] local.ALERT: 200  
[2023-03-26 23:34:43] local.ALERT: 2  
[2023-03-26 23:34:43] local.ALERT: 200  
[2023-03-26 23:34:43] local.ALERT: 2  
[2023-03-26 23:34:43] local.ALERT: 200  
[2023-03-26 23:34:43] local.ALERT: 1  
[2023-03-26 23:34:43] local.ALERT: 200  
[2023-03-26 23:34:43] local.ALERT: 1  
[2023-03-26 23:34:43] local.ALERT: 200  
[2023-03-26 23:34:43] local.ALERT: 1  
[2023-03-26 23:34:43] local.ALERT: 200  
[2023-03-26 23:34:43] local.ALERT: 1  
[2023-03-26 23:34:43] local.ALERT: 200  
[2023-03-26 23:34:43] local.ALERT: 1  
[2023-03-26 23:34:43] local.ALERT: 200  
[2023-03-26 23:34:43] local.ALERT: 2  
[2023-03-26 23:34:43] local.ALERT: 200  
[2023-03-26 23:34:43] local.ALERT: 10013  
[2023-03-26 23:34:43] local.ALERT: 200  
[2023-03-26 23:34:43] local.ALERT: 10013  
[2023-03-26 23:34:43] local.ALERT: 200  
[2023-03-26 23:34:43] local.ALERT: 10013  
[2023-03-26 23:34:43] local.ALERT: 200  
[2023-03-26 23:34:43] local.ALERT: 10013  
[2023-03-26 23:34:43] local.ALERT: 200  
[2023-03-26 23:34:43] local.ALERT: 1  
[2023-03-26 23:34:43] local.ALERT: 200  
[2023-03-26 23:34:43] local.ALERT: 3  
[2023-03-26 23:34:43] local.ALERT: 200  
[2023-03-26 23:34:43] local.ALERT: 3  
[2023-03-26 23:34:43] local.ALERT: 200  
[2023-03-26 23:34:43] local.ALERT: 40  
[2023-03-26 23:34:43] local.ALERT: 200  
[2023-03-26 23:34:43] local.ALERT: 40  
[2023-03-26 23:34:43] local.ALERT: 200  
[2023-03-26 23:34:43] local.ALERT: 40  
[2023-03-26 23:34:43] local.ALERT: 200  
[2023-03-26 23:34:43] local.ALERT: 40  
[2023-03-26 23:34:43] local.ALERT: 200  
[2023-03-26 23:34:43] local.ALERT: 200  
[2023-03-26 23:34:43] local.WARNING: 1  
[2023-03-26 23:34:43] local.WARNING: array (
  'ID' => 86,
  'Name' => 'فئة 25 جيجا 4000 ريال',
  'ServiceID' => 200,
  'Price' => 4000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 4000.0,
)  
[2023-03-26 23:34:43] local.ALERT: 200  
[2023-03-26 23:34:43] local.ALERT: 200  
[2023-03-26 23:34:43] local.ALERT: 200  
[2023-03-26 23:34:43] local.ALERT: 200  
[2023-03-26 23:34:43] local.ALERT: 200  
[2023-03-26 23:34:43] local.ALERT: 200  
[2023-03-26 23:34:43] local.ALERT: 200  
[2023-03-26 23:34:43] local.ALERT: 200  
[2023-03-26 23:34:43] local.ALERT: 200  
[2023-03-26 23:34:43] local.ALERT: 200  
[2023-03-26 23:34:43] local.ALERT: 200  
[2023-03-26 23:34:43] local.ALERT: 40  
[2023-03-26 23:34:43] local.CRITICAL: ****************************2  
[2023-03-26 23:34:43] local.CRITICAL: ****************************  
[2023-03-26 23:34:43] local.CRITICAL:   
[2023-03-26 23:34:43] local.CRITICAL: ****************************  
[2023-03-26 23:34:43] local.INFO: {
  "ClientBalanceResult": "293346.4300"
}  
[2023-03-26 23:34:43] local.INFO: array (
  'ClientBalanceResult' => '293346.4300',
)  
[2023-03-26 23:34:43] local.DEBUG: lattttef  
[2023-03-26 23:34:43] local.DEBUG: array (
  'ClientBalanceResult' => '293346.4300',
)  
[2023-03-26 23:34:43] local.INFO: transaction14  
[2023-03-26 23:34:43] local.INFO: first inquery phone = 101013261  
[2023-03-26 23:34:46] local.DEBUG: response querySubBalance  
[2023-03-26 23:34:46] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#8,000.00#8,000.00#0.00 B#01-04-2023#0#0##500.00#4G 60</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-26 23:34:46] local.DEBUG: array (
  0 => 'OK',
  1 => '8,000.00',
  2 => '8,000.00',
  3 => '0.00 B',
  4 => '01-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '500.00',
  9 => '4G 60',
)  
[2023-03-26 23:34:46] local.DEBUG: print  before faction by provider price  
[2023-03-26 23:34:46] local.DEBUG: print  after faction by provider price  
[2023-03-26 23:34:46] local.DEBUG: فئة 60 جيجا 8000 ريال  
[2023-03-26 23:34:46] local.DEBUG: print1  
[2023-03-26 23:34:46] local.DEBUG: print  2  
[2023-03-26 23:38:27] local.INFO: header  
[2023-03-26 23:38:27] local.INFO: header after fliter  
[2023-03-26 23:38:27] local.INFO: Body  after fliter  
[2023-03-26 23:38:27] local.INFO: array (
)  
[2023-03-26 23:38:27] local.INFO: transaction14  
[2023-03-26 23:38:27] local.INFO: first inquery phone = 101013261  
[2023-03-26 23:38:31] local.DEBUG: response querySubBalance  
[2023-03-26 23:38:31] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#8,000.00#8,000.00#0.00 B#01-04-2023#0#0##500.00#4G 60</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-26 23:38:31] local.DEBUG: array (
  0 => 'OK',
  1 => '8,000.00',
  2 => '8,000.00',
  3 => '0.00 B',
  4 => '01-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '500.00',
  9 => '4G 60',
)  
[2023-03-26 23:38:31] local.DEBUG: print  before faction by provider price  
[2023-03-26 23:38:31] local.DEBUG: print  after faction by provider price  
[2023-03-26 23:38:31] local.DEBUG: فئة 60 جيجا 8000 ريال  
[2023-03-26 23:38:31] local.DEBUG: print1  
[2023-03-26 23:38:31] local.DEBUG: print  2  
