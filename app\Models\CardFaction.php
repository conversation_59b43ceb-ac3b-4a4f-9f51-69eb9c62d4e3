<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CardFaction extends Model
{
    use HasFactory;

    protected $table = 'CardFaction';
    public $timestamps = false;

    protected $fillable = [
        'RowVersion',
        'Number',
        'Name',
        'CostPrice',
        'SelePrice',
        'CardTypeID',
        'Active',
        'Note',
        'OrderNo',
        'Description',
        'ProviderID',
        'Status',
        'BranchID',
        'CreatedBy',
        'CreatedTime',
    ];

    // public function branch()
    // {
    //     return $this->belongsTo(Branch::class, 'BranchID');
    // }

    public function cardType()
    {
        return $this->belongsTo(CardType::class, 'CardTypeID');
    }

    public function createdByUser()
    {
        return $this->belongsTo(UserInfo::class, 'CreatedBy');
    }
}
