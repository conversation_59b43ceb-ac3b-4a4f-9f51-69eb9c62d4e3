<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CardPayment extends Model
{
    use HasFactory;

    protected $table = 'CardPayment';
    public $timestamps = false;

    protected $fillable = [
        'RowVersion',
        'Number',
        'CardFactionID',
        'CardTypeID',
        'Amount',
        'CurrencyID',
        'Date',
        'Note',
        'Username',
        'Password',
        'Email',
        'Phone',
        'Channel',
        'EntryID',
        'CreditorAccountID',
        'DebitorAccountID',
        'ParentID',
        'ServiceID',
        'AccountID',
        'Status',
        'BranchID',
        'CreatedBy',
        'CreatedTime',
    ];

    // public function branch()
    // {
    //     return $this->belongsTo(Branch::class, 'BranchID');
    // }

    public function cardFaction()
    {
        return $this->belongsTo(CardFaction::class, 'CardFactionID');
    }

    public function cardType()
    {
        return $this->belongsTo(CardType::class, 'CardTypeID');
    }

    // public function currency()
    // {
    //     return $this->belongsTo(Currency::class, 'CurrencyID');
    // }

    public function parentOrder()
    {
        return $this->belongsTo(OrderInfo::class, 'ParentID');
    }

    // public function serviceInfo()
    // {
    //     return $this->belongsTo(ServiceInfo::class, 'ServiceID');
    // }

    public function createdByUser()
    {
        return $this->belongsTo(UserInfo::class, 'CreatedBy');
    }
}
